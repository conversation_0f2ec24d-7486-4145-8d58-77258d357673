# Changelog

## feature/aws-ses-v4x - master (2025-05-22T14:38:06+07:00 - 2025-05-08T11:28:16+07:00)

### Added or Changed
- Thêm trường `provider_name` và điều chỉnh logic hiển thị provider cho SES ([#b40368d](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/b40368d))
- <PERSON><PERSON>p nhật controller cấu hình email cho các domain AWS SES ([#7ab2eb5](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/7ab2eb5))
- <PERSON><PERSON><PERSON> tên <PERSON> topic và consumer group cho email upsert ([#bc64408](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/bc64408))
- Triển khai cấu hình region SES và cập nhật triển khai ([#1585282](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/1585282))
- Triển khai quản lý domain cho cấu hình email ([#afc5462](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/afc5462))
- Thực hiện tích hợp AWS SES và consumer đồng bộ NM ([#ffdcb47](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/ffdcb47))
- Chuyển sang các biến môi trường cho Kafka topic và group ID ([#7b6919c](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/7b6919c))
- Thực thi giới hạn cấu hình provider email chỉ đếm các loại SES và Mobio Mailer ([#52bd8a2](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/52bd8a2))

### Removed
- Xóa provider images và cấu hình cho các service không dùng nữa ([#4312d85](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/4312d85))
- Xóa provider config Mobio Mailer khỏi `script_init_provider_info.py` và cập nhật các tham chiếu `script_version` ([#25f9fab](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/25f9fab))
- Xóa file kết quả init script không cần thiết ([#354e801](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/354e801))
- Xóa source MySQL trong script `script_init_data_data_flow` ([#6815f6c](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/6815f6c))
### Fixed
- Thay đổi mã lỗi thành 409 khi domain được sử dụng trong các chiến dịch/workflow email ([#95f3b54](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/95f3b54))
- Sửa lỗi kiểm tra giới hạn cấu hình email trong tài khoản merchant ([#4f19d69](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/4f19d69))
- Xóa script init provider info ([#3b4cab9](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/3b4cab9))
- Kiểm tra các domain trùng lặp trước khi tạo cấu hình email ([#b762ce2](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/b762ce2))
- Sửa lỗi consumer group và deployment name trong file yaml ([#c8d37c4](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/c8d37c4))
- Thay đổi port mặc định thành 5000 để ổn định ứng dụng ([#dffd59a](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/dffd59a))
- Cập nhật text sale to oppty trong api get related object ([#1a7a2c0](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/1a7a2c0))
- Supported column, multi lang get related objects ([#2471db7](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/2471db7))
- Get information column table db2, oracle ([#0a96f07](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/0a96f07))
- Change text show error ([#7cb2509](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/7cb2509))
- Display sample data some special datatype ([#7197ce4](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/7197ce4))
- Schema postgres ([#0ddfad0](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/0ddfad0))
- Return pk and nomalize data in get sample ([#f98ecde](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/f98ecde))
- Case sensitive table, view ([#0436f2f](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/0436f2f))
- Postgres mode streaming must have pk ([#7c2b438](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/7c2b438))
- Schema_name oracle, valid mapping oracle ([#4cdb81d](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/4cdb81d))
- Correct data type support for Oracle database connection ([#0dc9d49](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/0dc9d49))
- Handle non-stringifiable data in query results to prevent errors ([#6a15b1b](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/6a15b1b))
- Correctly check for 'status_connect' field existence ([#a8e79fd](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/a8e79fd))
- Correct column type retrieval in database query ([#ed5d9ce](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/ed5d9ce))
- Only get supported datatype field mysql ([#8854e9a](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/8854e9a))
- Valid mapping config array type and add stop process filter ([#40dec1b](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/40dec1b))
- Build pipeline mysql ([#37ff217](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/37ff217))
- Valid mapping config array type and add stop process filter ([#97b0c1f](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/97b0c1f))
- Build pipeline mysql ([#4493a69](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/4493a69))
- Correctly filter streaming data in daily report job ([#e78618c](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/e78618c))
- Adjust time range and update daily report aggregation ([#738fc96](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/738fc96))
- Correct aggregation query and remove object attribute condition ([#6dfeee2](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/6dfeee2))
- Adjust end time and add log for daily report cron job ([#d6d6743](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/d6d6743))
- Adjust timezone for start/end times in streaming mode ([#879d8c0](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/879d8c0))
- Convert time strings to datetime objects for comparison ([#5d18e8d](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/5d18e8d))

### Under the hood
- Nâng cao xác thực domain và cấu hình AWS SES ([#1f0879e](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/1f0879e))
- Cập nhật cấu hình add on connector và hỗ trợ kiểu dữ liệu ([#c284773](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/c284773))
- Nâng cao logic kết nối database và xử lý dữ liệu ([#5341572](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/5341572))
- Chuyển script trạng thái kết nối sang thư mục đã version ([#495351d](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/495351d))
- Thực hiện script trạng thái connector và đồng bộ ([#b363528](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/b363528))
- Cập nhật sync daily report và trạng thái connector ở streaming mode ([#586f8ee](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/586f8ee))
- Check field mysql supported ([#6823ce3](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/6823ce3), [#097ad8b](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/097ad8b))
- Add use lib oracledb ([#df1942b](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/df1942b), [#9ad0cc9](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/9ad0cc9))
- Add db2 valid datatype ([#0258c91](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/0258c91), [#e9efb48](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/e9efb48))
- Update ([#6ed1cd2](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/6ed1cd2), [#271e15f](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/271e15f))
- Add schema postgres get sample ([#0c8aa7b](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/0c8aa7b), [#820801b](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/820801b))

### Dependencies
- Cập nhật các dependencies ([#ed4c7b2](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/ed4c7b2))

### Contributors
- @dao xuan loc ([#7b6919c](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/7b6919c), [#52bd8a2](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/52bd8a2), [#4f19d69](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/4f19d69), [#95f3b54](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/95f3b54), [#ed4c7b2](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/ed4c7b2), [#25f9fab](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/25f9fab), [#43e9136](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/43e9136), [#b40368d](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/b40368d), [#4312d85](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/4312d85), [#3b4cab9](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/3b4cab9), [#1f0879e](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/1f0879e), [#b762ce2](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/b762ce2), [#c8d37c4](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/c8d37c4), [#354e801](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/354e801), [#1585282](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/1585282), [#afc5462](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/afc5462), [#7ab2eb5](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/7ab2eb5), [#bc64408](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/bc64408), [#dffd59a](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/dffd59a), [#ffdcb47](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/ffdcb47))
- @tungdd ([#6ff1f4f](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/6ff1f4f), [#5d58a9a](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/5d58a9a), [#e78618c](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/e78618c), [#738fc96](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/738fc96), [#6dfeee2](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/6dfeee2), [#d6d6743](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/d6d6743), [#879d8c0](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/879d8c0), [#5d18e8d](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/5d18e8d), [#586f8ee](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/586f8ee), [#c284773](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/c284773), [#0dc9d49](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/0dc9d49), [#6a15b1b](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/6a15b1b), [#5341572](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/5341572), [#495351d](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/495351d), [#a8e79fd](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/a8e79fd), [#b363528](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/b363528), [#ed5d9ce](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/ed5d9ce))
- @Son ([#1a7a2c0](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/1a7a2c0), [#2471db7](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/2471db7), [#0a96f07](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/0a96f07), [#7cb2509](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/7cb2509), [#7197ce4](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/7197ce4), [#0ddfad0](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/0ddfad0), [#f98ecde](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/f98ecde), [#0436f2f](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/0436f2f), [#7c2b438](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/7c2b438), [#4cdb81d](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/4cdb81d), [#6815f6c](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/6815f6c), [#8854e9a](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/8854e9a), [#d53ab25](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/d53ab25), [#6823ce3](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/6823ce3), [#40dec1b](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/40dec1b), [#37ff217](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/37ff217), [#df1942b](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/df1942b), [#0258c91](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/0258c91), [#6ed1cd2](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/6ed1cd2), [#0c8aa7b](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/0c8aa7b), [#097ad8b](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/097ad8b), [#97b0c1f](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/97b0c1f), [#4493a69](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/4493a69), [#9ad0cc9](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/9ad0cc9), [#e9efb48](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/e9efb48), [#271e15f](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/271e15f), [#a0ebe78](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/a0ebe78), [#820801b](https://gitlab.mobio.vn/mobio/backend/projects/marketplacegroup/marketplace/-/commit/820801b))
