{"every_deploy": {"script_executed": ["PYTHONPATH=./ python3.11 scripts/script_init_provider_info.py"]}, "version": {"1": ["PYTHONPATH=./ python3.11 scripts/script_init_setting_add_on_connector.py"], "2": ["PYTHONPATH=./ python3.11 scripts/script_init_setting_add_on_connector.py"], "3": ["PYTHONPATH=./ python3.11 scripts/migrate/reports/script_migrate_df_report_to_daily_report.py", "PYTHONPATH=./ python3.11 scripts/script_convert_status_sync.py"], "4": ["PYTHONPATH=./ python3.11 scripts/v4dot44/script_add_status_connect_to_connector.py", "PYTHONPATH=./ python3.11 scripts/script_add_region_aws_ses.py", "PYTHONPATH=./ python3.11 scripts/script_init_provider_info.py"]}}