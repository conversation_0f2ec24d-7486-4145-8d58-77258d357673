[{"table": "file_upload", "name": "file_upload_search_idx_1", "fields": [{"field_name": "type", "index_type": 1}], "unique": false, "background": true}, {"table": "third_party_config", "name": "third_party_config_search_idx_1", "fields": [{"field_name": "merchant_id", "index_type": 1}, {"field_name": "status", "index_type": 1}, {"field_name": "website_url", "index_type": 1}], "unique": false, "background": true}, {"table": "connect_config", "name": "connect_config_search_idx_1", "fields": [{"field_name": "type", "index_type": 1}, {"field_name": "merchant_id", "index_type": 1}], "unique": false, "background": true}, {"table": "data_history", "name": "data_history_search_idx_1", "fields": [{"field_name": "webpush_config_id", "index_type": 1}, {"field_name": "merchant_id", "index_type": 1}], "unique": false, "background": true}, {"table": "data_history", "name": "data_history_search_idx_2", "fields": [{"field_name": "connect_config_id", "index_type": 1}, {"field_name": "merchant_id", "index_type": 1}], "unique": false, "background": true}, {"table": "sdk_script", "name": "sdk_script_search_idx_1", "fields": [{"field_name": "merchant_id", "index_type": 1}], "unique": false, "background": true}, {"table": "general_configuration_parameters", "name": "type_idx1", "fields": [{"field_name": "type", "index_type": 1}], "unique": false, "background": true}, {"table": "config_connectors", "name": "config_connectors_idx1", "fields": [{"field_name": "status_connect", "index_type": 1}], "unique": false, "background": true}, {"table": "config_connectors", "name": "config_connectors_idx2", "fields": [{"field_name": "merchant_id", "index_type": 1}, {"field_name": "keywords", "index_type": 1}, {"field_name": "status_connect", "index_type": 1}, {"field_name": "created_by", "index_type": 1}, {"field_name": "last_datetime_sync_data", "index_type": 1}], "unique": false, "background": true}, {"table": "config_connectors", "name": "config_connectors_idx3", "fields": [{"field_name": "merchant_id", "index_type": 1}, {"field_name": "name_ascii", "index_type": 1}], "unique": false, "background": true}, {"table": "config_connectors", "name": "config_connectors_idx4", "fields": [{"field_name": "merchant_id", "index_type": 1}, {"field_name": "data_type", "index_type": 1}], "unique": false, "background": true}, {"table": "config_connectors", "name": "config_connectors_idx5", "fields": [{"field_name": "status_connect", "index_type": 1}, {"field_name": "status_sync", "index_type": 1}], "unique": false, "background": true}, {"table": "config_app", "name": "merchant_1_name_search_1", "fields": [{"field_name": "merchant_id", "index_type": 1}, {"field_name": "name_search", "index_type": 1}], "unique": true, "background": true}, {"table": "daily_reports", "name": "index_idx1", "fields": [{"field_name": "connector_id", "index_type": 1}, {"field_name": "session_id", "index_type": 1}, {"field_name": "start_time", "index_type": 1}, {"field_name": "end_time", "index_type": 1}, {"field_name": "mode", "index_type": 1}], "unique": false, "background": true}]