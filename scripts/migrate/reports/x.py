#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 22/04/2025
"""


from bson import ObjectId
from mobio.libs.logging import MobioLogging

from src.common.utils import get_list_object_query_starrock
from src.models.reports.mongodb.daily_reports_model import DailyReportsModel
from src.models.reports.olap.dataflow.data_flow_model import DataFlowDialect


def agg_report_df_report_to_daily_report_in_db(start_time, end_time, page, per_page):
    """
    Chuyển đổi dữ liệu từ bảng dataflow_report thành dữ liệu cho bảng daily_reports
    """
    lst_object = get_list_object_query_starrock()

    str_column_select = "connector_id,session_id,SUM(total_rows) as total_rows, SUM(total_rows_success) as total_rows_success,SUM(total_rows_add) as total_rows_add,SUM(total_rows_updated) as total_rows_updated,SUM(total_rows_error) as total_rows_error,SUM(total_rows_find) as total_rows_find,MIN(start_time) as start_time,MAX(end_time) as end_time,MAX(connector_name) as connector_name,MAX(consume_status) as consume_status,MAX(process_status) as process_status,MAX(mode) as mode,"

    x1 = "MAX(connector_id) AS connector_id, 'total_streaming_prev_snapshot' AS session_id,SUM(total_rows) AS total_rows,SUM(total_rows_success) AS total_rows_success,SUM(total_rows_add) AS total_rows_add,SUM(total_rows_updated) AS total_rows_updated,SUM(total_rows_error) AS total_rows_error,SUM(total_rows_find) AS total_rows_find,MIN(start_time) AS start_time,MAX(end_time) AS end_time,MAX(connector_name) AS connector_name,MAX(consume_status) AS consume_status,MAX(process_status) AS process_status,MAX(mode) AS mode,"

    x2 = "sr.connector_id, sr.session_id,SUM(CASE WHEN sr.state = 'consume' AND sr.object is Null THEN sr.count ELSE 0 END) AS total_rows,SUM(CASE WHEN sr.state = 'processed' AND sr.object is Null THEN sr.count ELSE 0 END) AS total_rows_success,SUM(CASE WHEN sr.state = 'processed' AND sr.object is Null AND sr.action = 'add' THEN sr.count ELSE 0 END) AS total_rows_add,SUM(CASE WHEN sr.state = 'processed' AND sr.object is Null AND sr.action = 'update' THEN sr.count ELSE 0 END) AS total_rows_updated,SUM(CASE WHEN sr.state = 'error' AND sr.object is Null THEN sr.count ELSE 0 END) AS total_rows_error,SUM(CASE WHEN sr.state = 'processed' AND sr.object is Null AND sr.action = 'find' THEN sr.count ELSE 0 END) AS total_rows_find,ps.start_time as start_time,ps.end_time as end_time,ps.connector_name as connector_name,ps.consume_status as consume_status,ps.process_status as process_status,ps.mode as mode,"

    index = 0
    for object_item in lst_object:
        str_column_select += f"SUM({object_item}_total_rows) as {object_item}_total_rows, SUM({object_item}_total_rows_success) as {object_item}_total_rows_success, SUM({object_item}_total_rows_add) as {object_item}_total_rows_add, SUM({object_item}_total_rows_updated) as {object_item}_total_rows_updated, SUM({object_item}_total_rows_error) as {object_item}_total_rows_error, SUM({object_item}_total_rows_find) as {object_item}_total_rows_find"
        x1 += f"SUM({object_item}_total_rows) AS {object_item}_total_rows, SUM({object_item}_total_rows_success) AS {object_item}_total_rows_success, SUM({object_item}_total_rows_add) AS {object_item}_total_rows_add, SUM({object_item}_total_rows_updated) AS {object_item}_total_rows_updated, SUM({object_item}_total_rows_error) AS {object_item}_total_rows_error, SUM({object_item}_total_rows_find) AS {object_item}_total_rows_find"
        x2 += f"SUM(CASE WHEN sr.state = 'consume' AND sr.object = '{object_item}' THEN sr.count ELSE 0 END) AS {object_item}_total_rows,SUM(CASE WHEN sr.state = 'processed' AND sr.object = '{object_item}' THEN sr.count ELSE 0 END) AS {object_item}_total_rows_success,SUM(CASE WHEN sr.state = 'processed' AND sr.object = '{object_item}' AND sr.action = 'add' THEN sr.count ELSE 0 END) AS {object_item}_total_rows_add,SUM(CASE WHEN sr.state = 'processed' AND sr.object = '{object_item}' AND sr.action = 'update' THEN sr.count ELSE 0 END) AS {object_item}_total_rows_updated,SUM(CASE WHEN sr.state = 'error' AND sr.object = '{object_item}' THEN sr.count ELSE 0 END) AS {object_item}_total_rows_error,SUM(CASE WHEN sr.state = 'processed' AND sr.object = '{object_item}' AND sr.action = 'find' THEN sr.count ELSE 0 END) AS {object_item}_total_rows_find"
        if index != len(lst_object) - 1:
            str_column_select += ", "
            x1 += ", "
            x2 += ", "
        index += 1

    stmt = f"""
    SELECT
        {str_column_select}
    FROM
        (
            SELECT
                {x2}
            FROM
                data_flow.dataflow_report sr
            INNER JOIN data_flow.pipeline_session ps ON
                sr.session_id = ps.session_id
            GROUP BY
                sr.connector_id,
                sr.session_id,
                ps.start_time,
                ps.end_time,
                ps.connector_name,
                ps.consume_status,
                ps.process_status,
                ps.mode
        ) AS combined_data
    WHERE
        (start_time >= :start_time OR start_time IS NULL)
        AND (end_time <= :end_time OR end_time IS NULL)
    GROUP BY
        connector_id, session_id
    ORDER BY
        connector_id, session_id ASC
    LIMIT :page, :per_page
    """
    param_query = {
        "page": page,
        "per_page": per_page,
        "start_time": start_time,
        "end_time": end_time,
    }
    results = DataFlowDialect().process_query(stmt, param_query)
    if results:
        return results
    return []


def agg_report_df_report_to_daily_report(start_time, end_time, page, per_page):
    page = 0
    per_page = 1000
    while True:
        MobioLogging().info(f"Start cron_job_agg_report_df_log_to_daily_report page: {page}")
        skip_number = int(page) * int(per_page)
        results = agg_report_df_report_to_daily_report_in_db(start_time, end_time, skip_number, per_page)
        if not results:
            break
        data_inserts = []
        for result in results:
            MobioLogging().info(f"Update daily report for {result}")
            result_as_dict = result._asdict()
            filter_data = {
                "connector_id": result_as_dict["connector_id"],
                "session_id": result_as_dict["session_id"],
            }
            daily_report = DailyReportsModel().find_one(filter_data)
            if daily_report:
                continue
            result_as_dict["_id"] = ObjectId()
            data_inserts.append(result_as_dict)
        if data_inserts:
            DailyReportsModel().insert_many(data_inserts)
        if len(data_inserts) < per_page:
            break
        page += 1
    print("Done agg_report_df_report_to_daily_report")


if __name__ == "__main__":
    DailyReportsModel().delete_many({})
    agg_report_df_report_to_daily_report("2024-01-01 00:00:00", "2025-04-22 23:59:59", 0, 1000)
