#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 04/04/2025
"""


import datetime
import sys

from bson import ObjectId
from mobio.libs.logging import MobioLogging

from src.models.reports.mongodb.daily_reports_model import DailyReportsModel
from src.models.reports.olap.dataflow.data_flow_model import DataFlowDialect


def agg_report_df_report_to_daily_report(start_time, end_time, page, per_page):
    page = 0
    per_page = 1000
    while True:
        MobioLogging().info(f"Start cron_job_agg_report_df_log_to_daily_report page: {page}")
        skip_number = int(page) * int(per_page)
        results = DataFlowDialect().agg_report_df_report_to_daily_report(start_time, end_time, skip_number, per_page)
        if not results:
            MobioLogging().info("No data to migrate")
            break
        data_inserts = []
        for result in results:
            MobioLogging().info(f"Update daily report for {result}")
            result_as_dict = result._asdict()
            filter_data = {
                "connector_id": result_as_dict["connector_id"],
                "session_id": result_as_dict["session_id"],
            }
            daily_report = DailyReportsModel().find_one(filter_data)
            if daily_report:
                continue
            result_as_dict["_id"] = ObjectId()
            data_inserts.append(result_as_dict)
        if data_inserts:
            DailyReportsModel().insert_many(data_inserts)
        if len(data_inserts) < per_page:
            MobioLogging().info("Done migrate data")
            break
        page += 1
    print("Done agg_report_df_report_to_daily_report")


def migrate_df_log_to_daily_report_by_delta_date(delta_date: int):
    MobioLogging().info("Start cron_job_agg_report_df_log_to_daily_report")
    time_now = datetime.datetime.now(datetime.UTC)
    end_time = (time_now - datetime.timedelta(days=1)).replace(hour=23, minute=59, second=59, microsecond=999999)

    smt1 = """
        WITH deduplicated_data AS (
        SELECT
            connector_id,
            session_id,
            message_id,
            state,
            event_value,
            result,
            action
        FROM
            (
            SELECT
                connector_id,
                session_id,
                message_id,
                state,
                event_value,
                result,
                action,
                ROW_NUMBER() OVER ( PARTITION BY session_id,
                message_id,
                state
            ORDER BY
                created_time ASC ) AS row_num
            FROM
                data_flow.dataflow_log
            WHERE
                created_time >= :start_time
                AND created_time <= :end_time 
                ) AS tmp
        WHERE
            row_num = 1 )
        SELECT
            sr.connector_id,
            sr.session_id,
            SUM(CASE WHEN sr.state = 'consume' THEN 1 ELSE 0 END) AS total_rows,
            SUM(CASE WHEN sr.state = 'processed' AND sr.event_value IS NULL THEN 1 ELSE 0 END) AS total_rows_success,
            SUM(CASE WHEN sr.state = 'processed' AND sr.event_value IS NULL AND sr.action = 'add' THEN 1 ELSE 0 END) AS total_rows_add,
            SUM(CASE WHEN sr.state = 'processed' AND sr.event_value IS NULL AND sr.action = 'update' THEN 1 ELSE 0 END) AS total_rows_updated,
            SUM(CASE WHEN sr.state = 'error' THEN 1 ELSE 0 END) AS total_rows_error,
            SUM(CASE WHEN sr.state = 'processed' AND sr.event_value IS NULL AND sr.action = 'find' THEN 1 ELSE 0 END) AS total_rows_find,
            MIN(ps.start_time) AS start_time,
            MAX(ps.end_time) AS end_time,
            MAX(ps.connector_name) AS connector_name,
            MAX(ps.consume_status) AS consume_status,
            MAX(ps.process_status) AS process_status,
            MAX(ps.mode) AS mode,
            SUM(CASE WHEN sr.state = 'consume' AND get_json_string(sr.event_value, '$.dynamic_event_data') IS NOT NULL THEN 1 ELSE 0 END) AS profile_total_rows,
            SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed' THEN 1 ELSE 0 END) AS profile_total_rows_success,
            SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') = 'add' THEN 1 ELSE 0 END) AS profile_total_rows_add,
            SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') = 'update' THEN 1 ELSE 0 END) AS profile_total_rows_updated,
            SUM(CASE WHEN IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'error' THEN 1 ELSE 0 END) AS profile_total_rows_error,
            SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') = 'find' THEN 1 ELSE 0 END) AS profile_total_rows_find,
            SUM(CASE WHEN sr.state = 'consume' AND get_json_string(sr.event_value, '$.dynamic_event_data') IS NOT NULL THEN 1 ELSE 0 END) AS dynamic_event_total_rows,
            SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.dynamic_event.state'), '') = 'processed' THEN 1 ELSE 0 END) AS dynamic_event_total_rows_success,
            SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.dynamic_event.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.dynamic_event.action'), '') = 'add' THEN 1 ELSE 0 END) AS dynamic_event_total_rows_add,
            SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.dynamic_event.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.dynamic_event.action'), '') = 'update' THEN 1 ELSE 0 END) AS dynamic_event_total_rows_updated,
            SUM(CASE WHEN IFNULL(get_json_string(sr.result, '$.dynamic_event.state'), '') = 'error' THEN 1 ELSE 0 END) AS dynamic_event_total_rows_error,
            SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.dynamic_event.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.dynamic_event.action'), '') = 'find' THEN 1 ELSE 0 END) AS dynamic_event_total_rows_find,
            SUM(CASE WHEN sr.state = 'consume' AND get_json_string(sr.event_value, '$.product_holding_data') IS NOT NULL THEN 1 ELSE 0 END) AS product_holding_total_rows,
            SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.product_holding.state'), '') = 'processed' THEN 1 ELSE 0 END) AS product_holding_total_rows_success,
            SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.product_holding.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.product_holding.action'), '') = 'add' THEN 1 ELSE 0 END) AS product_holding_total_rows_add,
            SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.product_holding.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.product_holding.action'), '') = 'update' THEN 1 ELSE 0 END) AS product_holding_total_rows_updated,
            SUM(CASE WHEN IFNULL(get_json_string(sr.result, '$.product_holding.state'), '') = 'error' THEN 1 ELSE 0 END) AS product_holding_total_rows_error,
            SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.product_holding.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.product_holding.action'), '') = 'find' THEN 1 ELSE 0 END) AS product_holding_total_rows_find,
            SUM(CASE WHEN sr.state = 'consume' AND get_json_string(sr.event_value, '$.company_data') IS NOT NULL THEN 1 ELSE 0 END) AS company_total_rows,
            SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.company.state'), '') = 'processed' THEN 1 ELSE 0 END) AS company_total_rows_success,
            SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.company.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.company.action'), '') = 'add' THEN 1 ELSE 0 END) AS company_total_rows_add,
            SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.company.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.company.action'), '') = 'update' THEN 1 ELSE 0 END) AS company_total_rows_updated,
            SUM(CASE WHEN IFNULL(get_json_string(sr.result, '$.company.state'), '') = 'error' THEN 1 ELSE 0 END) AS company_total_rows_error,
            SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.company.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.company.action'), '') = 'find' THEN 1 ELSE 0 END) AS company_total_rows_find,
            SUM(CASE WHEN sr.state = 'consume' AND get_json_string(sr.event_value, '$.ticket_data') IS NOT NULL THEN 1 ELSE 0 END) AS ticket_total_rows,
            SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.ticket.state'), '') = 'processed' THEN 1 ELSE 0 END) AS ticket_total_rows_success,
            SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.ticket.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.ticket.action'), '') = 'add' THEN 1 ELSE 0 END) AS ticket_total_rows_add,
            SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.ticket.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.ticket.action'), '') = 'update' THEN 1 ELSE 0 END) AS ticket_total_rows_updated,
            SUM(CASE WHEN IFNULL(get_json_string(sr.result, '$.ticket.state'), '') = 'error' THEN 1 ELSE 0 END) AS ticket_total_rows_error,
            SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.ticket.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.ticket.action'), '') = 'find' THEN 1 ELSE 0 END) AS ticket_total_rows_find,
            SUM(CASE WHEN sr.state = 'consume' AND get_json_string(sr.event_value, '$.deal_data') IS NOT NULL THEN 1 ELSE 0 END) AS deal_total_rows,
            SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.deal.state'), '') = 'processed' THEN 1 ELSE 0 END) AS deal_total_rows_success,
            SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.deal.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.deal.action'), '') = 'add' THEN 1 ELSE 0 END) AS deal_total_rows_add,
            SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.deal.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.deal.action'), '') = 'update' THEN 1 ELSE 0 END) AS deal_total_rows_updated,
            SUM(CASE WHEN IFNULL(get_json_string(sr.result, '$.deal.state'), '') = 'error' THEN 1 ELSE 0 END) AS deal_total_rows_error,
            SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.deal.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.deal.action'), '') = 'find' THEN 1 ELSE 0 END) AS deal_total_rows_find
        FROM
            deduplicated_data sr
        INNER JOIN data_flow.pipeline_session ps ON
            sr.session_id = ps.session_id
        WHERE
            (ps.start_time >= :start_time
                OR ps.start_time IS NULL)
            AND (ps.end_time <= :end_time
                OR ps.end_time IS NULL)
        GROUP BY
            sr.connector_id,
            sr.session_id
        ORDER BY
            sr.session_id DESC
    """
    start_time_run = (
        (end_time - datetime.timedelta(days=1))
        .replace(hour=0, minute=0, second=0, microsecond=0)
        .strftime("%Y-%m-%d %H:%M:%S")
    )
    for i in range(delta_date):
        start_time_run = (
            (end_time - datetime.timedelta(days=i + 1))
            .replace(hour=0, minute=0, second=0, microsecond=0)
            .strftime("%Y-%m-%d %H:%M:%S")
        )
        end_time_run = (
            (end_time - datetime.timedelta(days=i))
            .replace(hour=23, minute=59, second=59, microsecond=999999)
            .strftime("%Y-%m-%d %H:%M:%S")
        )
        results = DataFlowDialect().process_query(smt1, {"start_time": start_time_run, "end_time": end_time_run})
        results = list(results) if results else []
        if not results:
            print(f"No data to migrate for {start_time_run} to {end_time_run}")
            break
        for result in results:
            result_as_dict = result._asdict()
            connector_id = result_as_dict["connector_id"]
            session_id = result_as_dict["session_id"]
            filter_data = {
                "connector_id": connector_id,
                "session_id": session_id,
            }
            daily_report = DailyReportsModel().find_one(filter_data)
            if daily_report:
                data_update = {"updated_by": "migrate_df_log_to_daily_report_by_delta_date"}
                for key in result_as_dict.keys():
                    if key not in [
                        "connector_id",
                        "session_id",
                        "start_time",
                        "mode",
                        "connector_name",
                        "consume_status",
                        "process_status",
                        "end_time",
                        "updated_by",
                    ]:
                        data_update[key] = (
                            daily_report[key] + result_as_dict[key]
                            if key in result_as_dict and key in daily_report
                            else result_as_dict[key]
                        )
                    if key == "end_time":
                        data_update[key] = result_as_dict[key]
                DailyReportsModel().update_one_query({"_id": daily_report["_id"]}, data_update)
            else:
                DailyReportsModel().insert(result_as_dict)
    print("Start agg_report_df_report_to_daily_report")
    print(f"Start time: {start_time_run}")
    agg_report_df_report_to_daily_report("2024-01-01 00:00:00", start_time_run, 0, 1000)
    print("Done")


if __name__ == "__main__":
    delta_date = sys.argv[1] if len(sys.argv) > 1 else 5
    migrate_df_log_to_daily_report_by_delta_date(delta_date=int(delta_date))
