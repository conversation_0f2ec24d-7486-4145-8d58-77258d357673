#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: LocDX
    Company: MobioVN
    Date created: 16/07/2025
"""
from src.common import utils
from src.models.data_flow.config_connectors_model import ConfigConnectorsModel
from pymongo import UpdateOne

if __name__ == "__main__":
    next_token = None
    per_page = 100
    while True:
        search_option = {
            "$or": [
                {"sort_time": {"$exists": False}},
                {"sort_time": None}
            ]
        }
        connector_configs, next_token = ConfigConnectorsModel().find_paginate_load_more(search_option, per_page, next_token)
        bulk_updates = []
        for connector in connector_configs:
            merchant_id = connector["merchant_id"]
            connector_id = connector.get("_id")
            updated_time = connector.get("updated_time")
            last_datetime_sync_data = connector.get("last_datetime_sync_data")
            sort_time = utils.compute_new_sort_time(updated_time, last_datetime_sync_data)

            filter_option = {
                "merchant_id": merchant_id,
                "_id": connector_id,
            }
            bulk_updates.append(UpdateOne(filter_option, {"$set": {"sort_time": sort_time}}))
        if bulk_updates:
            ConfigConnectorsModel().bulk_write_data(bulk_updates)
        if not next_token:
            break
