#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 31/10/2024
"""


from bson import ObjectId
from mobio.libs.logging import MobioLogging

from src.common import ConstantNM, ConstantThirdPartyConfigStatus
from src.internal_module.nm import NMHelper
from src.models.connect_config_model import AppConnectType, ConnectConfigModel
from src.models.third_party_config_model import (
    ThirdPartyConfigField,
    ThirdPartyConfigModel,
)


def sync_data_web_push_to_nm():
    webpushs = ThirdPartyConfigModel().find({"_id": ObjectId("65683d5657791056fc8efd88")})
    for third_party_config in webpushs:
        information_create = third_party_config.get("information_create")
        merchant_id = third_party_config.get("merchant_id")
        connect_information_config_id = third_party_config.get(ThirdPartyConfigField.CONNECT_INFORMATION_CONFIG_ID)
        if not connect_information_config_id:
            MobioLogging().error("handle_history_change_type_webpush :: not connect_information_config_id")
            continue
        connect_information_config_detail = ConnectConfigModel().get_current_config_by_type(
            connect_information_config_id, AppConnectType.FIREBASE
        )
        if not connect_information_config_detail:
            MobioLogging().error(
                "handle_history_change_type_webpush :: not connect_information_config_detail by id :: {}".format(
                    connect_information_config_id
                )
            )
            continue
        domain = third_party_config.get("website_url")
        status_config_nm = ConstantNM.StatusConfig.DISABLED
        status_third_party_config = third_party_config.get("status")
        if status_third_party_config == ConstantThirdPartyConfigStatus.ENABLE and not information_create:
            status_config_nm = ConstantNM.StatusConfig.ENABLE

        MobioLogging().info(
            "handle_history_change_type_webpush :: status_sync_nm :: domain :: {} :: {}".format(
                domain, status_config_nm
            )
        )
        status_sync_nm = NMHelper.update_config_push_firebase_nm(
            merchant_id, connect_information_config_detail, domain, status_config_nm
        )
        MobioLogging().info("handle_history_change_type_webpush :: status_sync_nm :: {}".format(status_sync_nm))


if __name__ == "__main__":
    sync_data_web_push_to_nm()
