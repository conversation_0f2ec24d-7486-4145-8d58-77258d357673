#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 11/11/2024
"""

from mobio.libs.logging import MobioLogging

from src.common.data_flow_constant import ConstantObjectHandle
from src.models.data_flow.config_connectors_model import ConfigConnectorsModel


def convert_object_attribute():
    number_object_profile = ConfigConnectorsModel().update_by_set(
        {"data_type": "data_in", "object": "profiles", "object_attribute": {"$exists": False}},
        {"object_attribute": ConstantObjectHandle.ObjectAttribute.PROFILE_ATTRIBUTE},
    )
    MobioLogging().info("Number object profile %s" % number_object_profile)

    number_object_sale = ConfigConnectorsModel().update_by_set(
        {"data_type": "data_in", "object": "sale", "object_attribute": {"$exists": False}},
        {"object_attribute": "sale"},
    )
    MobioLogging().info("Number object sale %s" % number_object_sale)


if __name__ == "__main__":
    convert_object_attribute()
