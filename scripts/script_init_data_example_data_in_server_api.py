#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 14/08/2024
"""

from src.common.data_flow_constant import (
    ConstantObjectHandle,
    ConstantTypeConfigurationGeneral,
)
from src.models.data_flow.general_configuration_parameters_model import (
    GeneralConfigurationParametersModel,
)


def script_init_data_sample_api():
    data_config = {
        "profiles": {
            "data": [
                {
                    "name": "Nguyễn Văn A",
                    "primary_email": "<EMAIL>",
                    "primary_phone": "0968xxxx81",
                    "profile_identify": [{"identify_type": "citizen_identity", "identify_value": "12324xxx"}],
                    "address_personal": [
                        {
                            "type": "contact_address",
                            "detail": "Số 6 ngõ 82 Duy Tân",
                            "district": "Phường Dịch Vọng Hậu",
                            "city": "Quận Cầu Giấy",
                            "county": "Thành phố Hà Nội",
                            "country": "Việt Nam",
                        }
                    ],
                    "source": "Nguồn A",
                }
            ],
        },
        "profiles_{}".format(ConstantObjectHandle.ObjectAttribute.PROFILE_PRODUCT_HOLING): {
            "data": [
                {
                    "product_holding": {
                        "product_code": "prd001",
                        "product_name": "Sản phẩm mẫu 01",
                        "account_open_date": "2023-08-23",
                        "balance": "1000000",
                        "interest_rate": "10",
                        "region_name": "Chi nhánh Hà Nội",
                        "product_status": "active",
                    },
                    "profile": {"cif": "********", "email": "<EMAIL>", "profile_name": "Nguyễn Văn A"},
                }
            ]
        },
        "profiles_{}".format(ConstantObjectHandle.ObjectAttribute.PROFILE_DYNAMIC_EVENT): {
            "data": [
                {
                    "profile_info": {
                        "name": "Nguyễn Văn A",
                        "primary_email": "<EMAIL>",
                        "primary_phone": "0968xxxx81",
                        "cif": "34654xxx2434",
                        "source": "Nguồn A",
                    },
                    "event_data": {
                        "action_time": "2025-01-01 08:00:00",
                        "string": "ABC",
                        "so_nguyen": 5000000,
                        "so_thap_phan": "********98765432.1234",
                        "ddmmyyy": "2025-01-01",
                        "ddmmyyy_hhmm": "2025-01-01 08:00:00",
                        "bolan": True,
                    },
                }
            ]
        },
        "profiles_{}".format(ConstantObjectHandle.ObjectAttribute.PROFILE_ATTRIBUTE): {
            "data": [
                {
                    "name": "Nguyễn Văn A",
                    "primary_email": "<EMAIL>",
                    "primary_phone": "0968xxxx81",
                    "profile_identify": [{"identify_type": "citizen_identity", "identify_value": "12324xxx"}],
                    "address_personal": [
                        {
                            "type": "contact_address",
                            "detail": "Số 6 ngõ 82 Duy Tân",
                            "district": "Phường Dịch Vọng Hậu",
                            "city": "Quận Cầu Giấy",
                            "county": "Thành phố Hà Nội",
                            "country": "Việt Nam",
                        }
                    ],
                    "source": "Nguồn A",
                }
            ],
        },
        "profiles_product_holding": {
            "data": [
                {
                    "product_holding": {
                        "product_code": "prd001",
                        "product_name": "Sản phẩm mẫu 01",
                        "account_open_date": "2023-08-23",
                        "balance": "1000000",
                        "interest_rate": "10",
                        "region_name": "Chi nhánh Hà Nội",
                        "product_status": "active",
                    },
                    "profile": {"cif": "********", "email": "<EMAIL>", "profile_name": "Nguyễn Văn A"},
                }
            ]
        },
        "profiles_dynamic_event": {
            "data": [
                {
                    "profile_info": {
                        "name": "Nguyễn Văn A",
                        "primary_email": "<EMAIL>",
                        "primary_phone": "0968xxxx81",
                        "cif": "34654xxx2434",
                        "source": "Nguồn A",
                    },
                    "event_data": {
                        "action_time": "2025-01-01 08:00:00",
                        "string": "ABC",
                        "so_nguyen": 5000000,
                        "so_thap_phan": "********98765432.1234",
                        "ddmmyyy": "2025-01-01",
                        "ddmmyyy_hhmm": "2025-01-01 08:00:00",
                        "bolan": True,
                    },
                }
            ]
        },
        "profiles_profile": {
            "data": [
                {
                    "name": "Nguyễn Văn A",
                    "primary_email": "<EMAIL>",
                    "primary_phone": "0968xxxx81",
                    "profile_identify": [{"identify_type": "citizen_identity", "identify_value": "12324xxx"}],
                    "address_personal": [
                        {
                            "type": "contact_address",
                            "detail": "Số 6 ngõ 82 Duy Tân",
                            "district": "Phường Dịch Vọng Hậu",
                            "city": "Quận Cầu Giấy",
                            "county": "Thành phố Hà Nội",
                            "country": "Việt Nam",
                        }
                    ],
                    "source": "Nguồn A",
                }
            ]
        },
        "profiles_product_holding": {
            "data": [
                {
                    "product_holding": {
                        "product_code": "prd001",
                        "product_name": "Sản phẩm mẫu 01",
                        "account_open_date": "2023-08-23",
                        "balance": "1000000",
                        "interest_rate": "10",
                        "region_name": "Chi nhánh Hà Nội",
                        "product_status": "active",
                    },
                    "profile": {"cif": "********", "email": "<EMAIL>", "profile_name": "Nguyễn Văn A"},
                }
            ]
        },
        "profiles_dynamic_event": {
            "data": [
                {
                    "profile_info": {
                        "name": "Nguyễn Văn A",
                        "primary_email": "<EMAIL>",
                        "primary_phone": "0968xxxx81",
                        "cif": "34654xxx2434",
                        "source": "Nguồn A",
                    },
                    "event_data": {
                        "action_time": "2025-01-01 08:00:00",
                        "string": "ABC",
                        "so_nguyen": 5000000,
                        "so_thap_phan": "********98765432.1234",
                        "ddmmyyy": "2025-01-01",
                        "ddmmyyy_hhmm": "2025-01-01 08:00:00",
                        "bolan": True,
                    },
                }
            ]
        },
        "profiles_profile": {
            "data": [
                {
                    "name": "Nguyễn Văn A",
                    "primary_email": "<EMAIL>",
                    "primary_phone": "0968xxxx81",
                    "profile_identify": [{"identify_type": "citizen_identity", "identify_value": "12324xxx"}],
                    "address_personal": [
                        {
                            "type": "contact_address",
                            "detail": "Số 6 ngõ 82 Duy Tân",
                            "district": "Phường Dịch Vọng Hậu",
                            "city": "Quận Cầu Giấy",
                            "county": "Thành phố Hà Nội",
                            "country": "Việt Nam",
                        }
                    ],
                    "source": "Nguồn A",
                }
            ]
        },
        "sale": {
            "data": [
                {
                    "name": "Tên đơn hàng 1",
                    "sale_process_id": "5d7f150be6481e870a8ce0ad",
                    "state_code": "LEAD",
                    "sale_value": 15000000,
                    "description": "Mô tả đơn hàng",
                    "assignee": [{"assignee_id": "45042df5-2202-4964-b05f-d53e21f5f895", "permission": "owner"}],
                    "estimate_time": "24/08/2024",
                    "reason_success": "",
                    "reason_fail": "",
                    "profiles": ["45042df5-2202-4964-b05f-d53e21f5f895"],
                }
            ]
        },
        "ticket": {
            "data": [
                {
                    "title": "Ticket 1",
                    "status_code": "OPEN",
                    "priority_code": "LOW",
                    "description": "Mô tả ticket",
                    "profiles": ["45042df5-2202-4964-b05f-d53e21f5f895"],
                }
            ]
        },
    }
    data_config_exist = GeneralConfigurationParametersModel().find_one(
        {"type": ConstantTypeConfigurationGeneral.DATA_SAMPLE_REQUEST_API}
    )
    if not data_config_exist:
        GeneralConfigurationParametersModel().insert(
            {"type": ConstantTypeConfigurationGeneral.DATA_SAMPLE_REQUEST_API, "data": data_config}
        )
        print("Insert data sample request API success")
    else:
        data_config_exist_id = data_config_exist.get("_id")
        GeneralConfigurationParametersModel().update_by_set({"_id": data_config_exist_id}, {"data": data_config})
    print("DONE")


if __name__ == "__main__":
    script_init_data_sample_api()
