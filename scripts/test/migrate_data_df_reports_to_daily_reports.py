#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 01/04/2025
"""

import datetime

from src.models.reports.mongodb.daily_reports_model import DailyReportsModel


def insert_data_sample(daily_reports_model):
    daily_reports_model.delete_many({})
    # Insert data sample
    lst_data_sample = [
        {
            "start_time": datetime.datetime.strptime("2025-04-01T04:09:33Z", "%Y-%m-%dT%H:%M:%SZ"),
            "end_time": datetime.datetime.strptime("2025-04-01T04:11:28Z", "%Y-%m-%dT%H:%M:%SZ"),
            "connector_id": 1914,
            "session_id": 20250401041035,
            "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
            "status": "done",
            "mode": "batch",
            "total_rows": 10,
            "total_rows_success": 10,
            "total_rows_error": 0,
            "dynamic_event_total_rows": 10,
            "dynamic_event_total_rows_success": 10,
            "dynamic_event_total_rows_error": 0,
            "dynamic_event_total_rows_action_updated": 0,
            "dynamic_event_total_rows_action_add": 0,
            "dynamic_event_total_rows_action_find": 0,
            "profile_total_rows": 10,
            "profile_total_rows_success": 10,
            "profile_total_rows_error": 0,
            "profile_total_rows_action_updated": 0,
            "profile_total_rows_action_add": 0,
            "profile_total_rows_action_find": 10,
        },
        {
            "start_time": datetime.datetime.strptime("2025-04-01T03:06:27Z", "%Y-%m-%dT%H:%M:%SZ"),
            "end_time": datetime.datetime.strptime("2025-04-01T03:59:23Z", "%Y-%m-%dT%H:%M:%SZ"),
            "connector_id": 1914,
            "session_id": 20250401031052,
            "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
            "status": "done",
            "mode": "batch",
            "total_rows": 10,
            "total_rows_success": 10,
            "total_rows_error": 0,
            "dynamic_event_total_rows": 10,
            "dynamic_event_total_rows_success": 10,
            "dynamic_event_total_rows_error": 0,
            "dynamic_event_total_rows_action_updated": 0,
            "dynamic_event_total_rows_action_add": 5,
            "dynamic_event_total_rows_action_find": 5,
            "profile_total_rows": 10,
            "profile_total_rows_success": 10,
            "profile_total_rows_error": 0,
            "profile_total_rows_action_updated": 0,
            "profile_total_rows_action_add": 0,
            "profile_total_rows_action_find": 10,
        },
        {
            "start_time": datetime.datetime.strptime("2025-03-31T08:37:07Z", "%Y-%m-%dT%H:%M:%SZ"),
            "end_time": datetime.datetime.strptime("2025-03-31T08:41:45Z", "%Y-%m-%dT%H:%M:%SZ"),
            "connector_id": 1914,
            "session_id": 20250331084686,
            "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
            "status": "done",
            "mode": "batch",
            "total_rows": 5,
            "total_rows_success": 5,
            "total_rows_error": 0,
            "dynamic_event_total_rows": 5,
            "dynamic_event_total_rows_success": 5,
            "dynamic_event_total_rows_error": 0,
            "dynamic_event_total_rows_action_updated": 0,
            "dynamic_event_total_rows_action_add": 5,
            "dynamic_event_total_rows_action_find": 0,
            "profile_total_rows": 5,
            "profile_total_rows_success": 5,
            "profile_total_rows_error": 0,
            "profile_total_rows_action_updated": 0,
            "profile_total_rows_action_add": 3,
            "profile_total_rows_action_find": 2,
        },
        {
            "start_time": datetime.datetime.strptime("2025-03-31T08:18:39Z", "%Y-%m-%dT%H:%M:%SZ"),
            "end_time": datetime.datetime.strptime("2025-03-31T08:21:42Z", "%Y-%m-%dT%H:%M:%SZ"),
            "connector_id": 1914,
            "session_id": 20250331082329,
            "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
            "status": "done",
            "total_rows": 2,
            "total_rows_success": 2,
            "total_rows_error": 0,
            "dynamic_event_total_rows": 2,
            "dynamic_event_total_rows_success": 2,
            "dynamic_event_total_rows_error": 0,
            "dynamic_event_total_rows_action_updated": 0,
            "dynamic_event_total_rows_action_add": 1,
            "dynamic_event_total_rows_action_find": 1,
            "profile_total_rows": 2,
            "profile_total_rows_success": 2,
            "profile_total_rows_error": 0,
            "profile_total_rows_action_updated": 0,
            "profile_total_rows_action_add": 0,
            "profile_total_rows_action_find": 2,
        },
        {
            "start_time": datetime.datetime.strptime("2025-03-31T08:12:10Z", "%Y-%m-%dT%H:%M:%SZ"),
            "end_time": datetime.datetime.strptime("2025-03-31T08:17:39Z", "%Y-%m-%dT%H:%M:%SZ"),
            "connector_id": 1914,
            "session_id": 20250331081819,
            "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
            "status": "done",
            "mode": "batch",
            "total_rows": 2,
            "total_rows_success": 1,
            "total_rows_error": 1,
            "dynamic_event_total_rows": 2,
            "dynamic_event_total_rows_success": 1,
            "dynamic_event_total_rows_error": 1,
            "dynamic_event_total_rows_action_updated": 0,
            "dynamic_event_total_rows_action_add": 1,
            "dynamic_event_total_rows_action_find": 0,
            "profile_total_rows": 2,
            "profile_total_rows_success": 2,
            "profile_total_rows_error": 0,
            "profile_total_rows_action_updated": 0,
            "profile_total_rows_action_add": 0,
            "profile_total_rows_action_find": 2,
        },
        {
            "start_time": datetime.datetime.strptime("2025-03-31T07:51:13Z", "%Y-%m-%dT%H:%M:%SZ"),
            "end_time": datetime.datetime.strptime("2025-03-31T07:57:38Z", "%Y-%m-%dT%H:%M:%SZ"),
            "connector_id": 1914,
            "session_id": 20250331075784,
            "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
            "status": "done",
            "mode": "batch",
            "total_rows": 2,
            "total_rows_success": 2,
            "total_rows_error": 0,
            "dynamic_event_total_rows": 2,
            "dynamic_event_total_rows_success": 2,
            "dynamic_event_total_rows_error": 0,
            "dynamic_event_total_rows_action_updated": 0,
            "dynamic_event_total_rows_action_add": 2,
            "dynamic_event_total_rows_action_find": 0,
            "profile_total_rows": 2,
            "profile_total_rows_success": 2,
            "profile_total_rows_error": 0,
            "profile_total_rows_action_updated": 0,
            "profile_total_rows_action_add": 2,
            "profile_total_rows_action_find": 0,
        },
        {
            "start_time": datetime.datetime.strptime("2025-03-30T07:51:13Z", "%Y-%m-%dT%H:%M:%SZ"),
            "end_time": datetime.datetime.strptime("2025-03-30T07:57:38Z", "%Y-%m-%dT%H:%M:%SZ"),
            "connector_id": 1914,
            "session_id": 20250330075760,
            "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
            "status": "done",
            "mode": "batch",
            "total_rows": 2,
            "total_rows_success": 2,
            "total_rows_error": 0,
            "dynamic_event_total_rows": 2,
            "dynamic_event_total_rows_success": 2,
            "dynamic_event_total_rows_error": 0,
            "dynamic_event_total_rows_action_updated": 0,
            "dynamic_event_total_rows_action_add": 2,
            "dynamic_event_total_rows_action_find": 0,
            "profile_total_rows": 2,
            "profile_total_rows_success": 2,
            "profile_total_rows_error": 0,
            "profile_total_rows_action_updated": 0,
            "profile_total_rows_action_add": 2,
            "profile_total_rows_action_find": 0,
        },
    ]
    daily_reports_model.insert_many(lst_data_sample)


def agg_report_by_day(connector_id):
    daily_reports_model = DailyReportsModel()
    start_time = "2025-01-01 17:00:00"
    end_time = "2025-04-01 16:59:00"

    # Convert string dates to datetime objects
    start_dt = datetime.datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
    end_dt = datetime.datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")

    # Create the MongoDB aggregation pipeline
    pipeline = [
        # Match documents with the specified connector_id and within the time range
        {"$match": {"connector_id": connector_id, "start_time": {"$gte": start_dt, "$lte": end_dt}}},
        # Add a field with just the date part (no time)
        {"$addFields": {"date_only": {"$dateToString": {"format": "%Y-%m-%d 00:00:00", "date": "$start_time"}}}},
        # Group by the date and status
        {"$group": {"_id": {"date": "$date_only", "status": "$status"}, "count": {"$sum": 1}}},
        # Reshape for easier processing
        {"$group": {"_id": "$_id.date", "statuses": {"$push": {"status": "$_id.status", "count": "$count"}}}},
        # Sort by date
        {"$sort": {"_id": 1}},
    ]

    # Execute the aggregation
    result = list(daily_reports_model.aggregate(pipeline))

    # Process results into the desired format
    detail_data = []

    for item in result:
        # Initialize counters for each status
        status_counts = {"done": 0, "fail": 0, "processing": 0, "wait_process": 0}

        # Update counters based on aggregation results
        for status_info in item["statuses"]:
            status = status_info["status"]
            if status in status_counts:
                status_counts[status] = status_info["count"]

        # Add to detail_data in the required format
        detail_data.append({"date_group": item["_id"], "total_done_sessions": status_counts["done"]})

    # Construct the final response
    response = {
        "detail_data": detail_data,
        "start_time": start_time,
        "end_time": end_time,
        "status_sync": "wait_process,processing,done,fail",
    }

    return response


if __name__ == "__main__":
    daily_reports_model = DailyReportsModel()

    insert_data_sample(daily_reports_model)

    # Query get total session
    agg_query = [
        {"$match": {"connector_id": 1914}},
        {
            "$group": {
                "_id": "$status",
                "count": {"$sum": 1},
            },
        },
        {
            "$group": {
                "_id": None,
                "statusCounts": {
                    "$push": {
                        "k": "$_id",
                        "v": "$count",
                    },
                },
            },
        },
    ]

    result = daily_reports_model.aggregate(agg_query)
    print([*result])

    print(agg_report_by_day(1914))
