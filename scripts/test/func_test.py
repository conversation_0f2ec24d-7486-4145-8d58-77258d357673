#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 13/09/2024
"""
from src.common.utils import (
    convert_string_datetime_to_datetime,
    convert_string_datetime_to_timezone,
)
from src.controllers.reports.data_flow_controller import DataFlowReportController
from src.models.reports.olap.dataflow.data_flow_model import DataFlowDialect


def build_mapping_state(states):
    mapping_state = {
        "consume": "wait_process",
        "processing": "processing",
        "processed": "done",
        "error": "process_fail",
    }

    reverse_mapping_state = {v: k for k, v in mapping_state.items()}

    return {reverse_mapping_state.get(v): v for v in states.split(",")} if states else mapping_state


if __name__ == "__main__":
    self = DataFlowReportController()
    start_time = "2024-09-13T08:00Z"
    end_time = "2024-09-13T11:00Z"
    merchant_id = "10350f12-1bd7-4369-9750-46d35c416a46"
    connector_id = 88
    time_step = 15
    time_unit = "minute"
    start_time = convert_string_datetime_to_timezone(start_time, self.FROM_FORMAT_DATETIME).strftime(
        self.TO_FORMAT_DATETIME
    )
    end_time = convert_string_datetime_to_timezone(end_time, self.FROM_FORMAT_DATETIME).strftime(
        self.TO_FORMAT_DATETIME
    )
    mapping_state = build_mapping_state(None)

    results = DataFlowDialect().streaming_realtime_result(
        merchant_id, connector_id, mapping_state, start_time, end_time, time_step, time_unit
    )
    print(results)
    return_data = []
    for result in results:
        result_as_dict = result._asdict()
        return_data.append(
            {
                "date": convert_string_datetime_to_datetime(
                    result_as_dict.get("time_query"), "%Y-%m-%d %H:%M"
                ).strftime(self.FROM_FORMAT_DATETIME),
                "total_process_done": result["processed_count"],
                "total_process_fail": result["error_count"],
            }
        )
