#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 30/05/2024
"""

from src.models.reports.olap.dataflow.data_flow_model import DataFlowDialect

if __name__ == "__main__":
    merchant_id = "3895b19b-f877-11ee-8bfc-9b54d42576d5"
    connector_id = 59

    # print(DataFlowDialect().total_session_of_connector(merchant_id, connector_id))
    print(
        DataFlowDialect().list_session_of_connector(
            merchant_id,
            {"consume": 1, "processing": 2, "processed": 3, "error": 4},
            connector_id,
            start_time="2024-05-01 03:01",
            end_time="2024-05-28 03:02",
            per_page=1,
            before_token="eyJwYWdlX2luZGV4IjogMX0=",
        )
    )
