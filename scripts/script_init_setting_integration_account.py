#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 12/09/2024
"""

from src.models.integration_account.setting_integration_account_model import (
    SettingIntegrationAccountModel,
)

if __name__ == "__main__":
    data_upsert = {
        "type": "google_sheet",
        "config": {
            "web": {
                "client_id": "************-2amn77n50abdna56k7tggeuu9am950vo.apps.googleusercontent.com",
                "project_id": "mobiocdp",
                "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                "token_uri": "https://oauth2.googleapis.com/token",
                "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
                "client_secret": "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",
                "redirect_uris": [
                    "https://release.mobio.vn/market-place/api/v1.0/google/authorized-callback",
                    # "http://127.0.0.1:5009/market-place/api/v1.0/google/authorized-callback",
                ],
                "javascript_origins": ["https://release.mobio.vn", "http://127.0.0.1:5009"],
            },
            "scopes": [
                "https://www.googleapis.com/auth/drive",
                "https://www.googleapis.com/auth/drive.file",
                "https://www.googleapis.com/auth/spreadsheets",
                "openid",
                "https://www.googleapis.com/auth/userinfo.profile",
                "https://www.googleapis.com/auth/userinfo.email",
            ],
        },
    }

    SettingIntegrationAccountModel().upsert({"type": data_upsert.get("type")}, data_upsert)
