#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 20/05/2025
"""

import sys

from src.internal_module.admin import AdminInternal
from src.models.data_flow.customize_config_transform_helper_model import (
    CustomizeConfigTransformHelperModel,
)


def add_config_transform_helper_dlvn(merchant_code):
    merchant_id = AdminInternal().get_merchant_id_by_merchant_code(merchant_code)
    if not merchant_id:
        print("Merchant not found")
        sys.exit(0)
    lst_data_config = [
        {
            "merchant_id": merchant_id,
            "source_key": "db2",
            "source_type": "databases",
            "configs": [
                {
                    "field_source_type": "TIMESTAMP",
                    "transform_helpers": [
                        {
                            "name": "",
                            "description": "",
                            "type": "date_helper",
                            "operation": {"version": "0.1", "name": "change_time_zone"},
                            "parameters": {
                                "format_config": {
                                    "type": "object",
                                    "value": {
                                        "format_src": {},
                                        "format_target": {"value": "YYYY-MM-DD HH:MM:SS.000", "type": "string"},
                                    },
                                },
                                "time_zone": {"type": "string", "value": "Asia/Saigon"},
                            },
                            "error_handler": {"default_strategy": {"name": "", "action": "terminate"}},
                        }
                    ],
                }
            ],
        }
    ]

    for data_config in lst_data_config:
        filter_option = {
            "merchant_id": data_config["merchant_id"],
            "source_key": data_config["source_key"],
            "source_type": data_config["source_type"],
        }
        config = CustomizeConfigTransformHelperModel().find_one(filter_option)
        if config:
            config.update({"configs": data_config["configs"]})
            CustomizeConfigTransformHelperModel().update_one_query(filter_option, config)
        else:
            CustomizeConfigTransformHelperModel().insert_document(data_config)
    print("Done")


if __name__ == "__main__":
    merchant_code = sys.argv[1] if len(sys.argv) > 1 else None
    if not merchant_code:
        print("No merchant code")
        sys.exit(0)
    add_config_transform_helper_dlvn(merchant_code)
