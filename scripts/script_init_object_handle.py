#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 23/10/2024
"""

from src.models.data_flow.config_connectors_model import ConfigConnectorsModel
from src.models.data_flow.object_handle_model import SettingObjectHandleModel


def init_object_handle_by_merchant_id():
    merchant_ids = ConfigConnectorsModel().distinct("merchant_id", {})
    for merchant_id in merchant_ids:
        SettingObjectHandleModel().init_list_source_handled(merchant_id)


if __name__ == "__main__":
    init_object_handle_by_merchant_id()
