#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Locdx
    Company: MobioVN
    Date created: 25/04/2025
"""
from src.helpers.caching import CachingHelpers
from src.models.region_ses_model import RegionSesModel

if __name__ == "__main__":
    data_set = {"region_data": [
        {
            "region_code": "us-gov-east-1",
            "region_name": "US-East"
        },
        {
            "region_code": "il-central-1",
            "region_name": "Tel Aviv"
        },
        {
            "region_code": "ap-southeast-3",
            "region_name": "Jakarta"
        },
        {
            "region_code": "ap-northeast-3",
            "region_name": "Osaka"
        },
        {
            "region_code": "af-south-1",
            "region_name": "Cape Town"
        },
        {
            "region_code": "eu-south-1",
            "region_name": "Milan"
        },
        {
            "region_code": "me-south-1",
            "region_name": "Bahrain"
        },
        {
            "region_code": "eu-north-1",
            "region_name": "Stockholm"
        },
        {
            "region_code": "eu-west-3",
            "region_name": "Paris"
        },
        {
            "region_code": "us-west-1",
            "region_name": "N. California"
        },
        {
            "region_code": "ap-northeast-1",
            "region_name": "Tokyo"
        },
        {
            "region_code": "ap-northeast-2",
            "region_name": "Seoul"
        },
        {
            "region_code": "ap-southeast-1",
            "region_name": "Singapore"
        },
        {
            "region_code": "us-east-2",
            "region_name": "Ohio"
        },
        {
            "region_code": "us-gov-west-1",
            "region_name": "US-West"
        },
        {
            "region_code": "ca-central-1",
            "region_name": "Central"
        },
        {
            "region_code": "eu-west-2",
            "region_name": "London"
        },
        {
            "region_code": "sa-east-1",
            "region_name": "Sao Paulo"
        },
        {
            "region_code": "ap-south-1",
            "region_name": "Mumbai"
        },
        {
            "region_code": "ap-southeast-2",
            "region_name": "Sydney"
        },
        {
            "region_code": "eu-central-1",
            "region_name": "Frankfurt"
        },
        {
            "region_code": "eu-west-1",
            "region_name": "Ireland"
        },
        {
            "region_code": "us-east-1",
            "region_name": "N. Virginia"
        },
        {
            "region_code": "us-west-2",
            "region_name": "Oregon"
        },
    ]}
    RegionSesModel().create_region_ses_config({}, data_set)
    CachingHelpers().set_value_by_key_not_hash(
        key="region",
        value=data_set,
        expiration=365*24*60*60,
    )
