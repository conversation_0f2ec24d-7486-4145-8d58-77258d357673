#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 04/11/2024
"""

from src.common.data_flow_constant import ConstantObjectHandle
from src.models.connect_config_model import Connect<PERSON>onfigModel


def script_update_object_attribute():
    """
    Cập nhật object_attribute cho tất cả các connect_config
    """
    print("Start update object_attribute")
    ConnectConfigModel().update_by_set({"object_attribute": {"$exists": False}}, {"object_attribute": ""})
    ConnectConfigModel().update_by_set(
        {"object": ConstantObjectHandle.Object.PROFILES},
        {"object_attribute": ConstantObjectHandle.ObjectAttribute.PROFILE_ATTRIBUTE},
    )
    print("Done")


if __name__ == "__main__":
    script_update_object_attribute()
