#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 06/05/2024
"""

from src.controllers.data_flow_controller import DataFlowController
from src.models.data_flow.config_connectors_model import ConfigConnectorsModel

if __name__ == "__main__":
    connectors = ConfigConnectorsModel().find({})
    for connector in connectors:
        connector_id = connector["_id"]
        connector_name = connector["name"]
        connector_description = connector.get("description")
        keyword = DataFlowController()._build_keyword_search(connector_name, connector_description)
        connector_name_ascii = DataFlowController()._build_connector_name_ascii(connector_name)

        data_update = {"keywords": keyword, "name_ascii": connector_name_ascii}

        status_update = ConfigConnectorsModel().update_by_set({"_id": connector_id}, data_update)
