#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 19/09/2024
"""


from src.common.data_flow_constant import ConstantObjectHandle
from src.models.data_flow.setting_add_on_connector_model import (
    SettingAddOnConnectorModel,
)


def upsert_config_add_on_connector():
    data_config = {
        "merchant_id": "default",
        "source_type": "form",
        "source_key": "contact_form",
        "data_type": "data_in",
        "extract_config": {
            "topic_consume_data_raw": "form-receive-submit-data",
            "include_fields": [{"fields": ["profile_id"], "source": "sdk", "object": "profile"}],
        },
        "extra_config": {
            "topic_consume_data_raw": "form-receive-submit-data",
            "include_fields": [{"fields": ["profile_id"], "source": "sdk", "object": "profile"}],
        },
    }

    SettingAddOnConnectorModel().upsert_setting(
        {
            "merchant_id": "default",
            "source_type": "form",
            "source_key": "contact_form",
            "data_type": "data_in",
        },
        data_config,
    )


def update_multi_config_add_on_connector():
    multi_config = [
        {
            "merchant_id": "default",
            "source_type": "form",
            "source_key": "contact_form",
            "data_type": "data_in",
            "extract_config": {
                "topic_consume_data_raw": "form-receive-submit-data",
                "include_fields": [{"fields": ["profile_id"], "source": "sdk", "object": "profile"}],
            },
            "extra_config": {
                "topic_consume_data_raw": "form-receive-submit-data",
                "include_fields": [{"fields": ["profile_id"], "source": "sdk", "object": "profile"}],
            },
        },
        {
            "merchant_id": "default",
            "source_type": "databases",
            "source_key": "postgres",
            "object_primary": ConstantObjectHandle.Object.PROFILES,
            "object_attribute": ConstantObjectHandle.ObjectAttribute.PROFILE_DYNAMIC_EVENT,
            "data_type": "data_in",
            "extract_config": {},
            "target_push_data_transform": {
                "callback_type": "queue",
                "queue_config": {
                    "target": "profiling-de-unify-find-or-insert",
                },
            },
        },
        {
            "merchant_id": "default",
            "source_type": "databases",
            "source_key": "mysql",
            "object_primary": ConstantObjectHandle.Object.PROFILES,
            "object_attribute": ConstantObjectHandle.ObjectAttribute.PROFILE_DYNAMIC_EVENT,
            "data_type": "data_in",
            "extract_config": {},
            "target_push_data_transform": {
                "callback_type": "queue",
                "queue_config": {
                    "target": "profiling-de-unify-find-or-insert",
                },
            },
        },
        {
            "merchant_id": "default",
            "source_type": "databases",
            "source_key": "sqlserver",
            "object_primary": ConstantObjectHandle.Object.PROFILES,
            "object_attribute": ConstantObjectHandle.ObjectAttribute.PROFILE_DYNAMIC_EVENT,
            "data_type": "data_in",
            "extract_config": {},
            "target_push_data_transform": {
                "callback_type": "queue",
                "queue_config": {
                    "target": "profiling-de-unify-find-or-insert",
                },
            },
        },
        {
            "merchant_id": "default",
            "source_type": "databases",
            "source_key": "oracle",
            "object_primary": ConstantObjectHandle.Object.PROFILES,
            "object_attribute": ConstantObjectHandle.ObjectAttribute.PROFILE_DYNAMIC_EVENT,
            "data_type": "data_in",
            "extract_config": {},
            "target_push_data_transform": {
                "callback_type": "queue",
                "queue_config": {
                    "target": "profiling-de-unify-find-or-insert",
                },
            },
        },
        {
            "merchant_id": "default",
            "source_type": "server",
            "source_key": "api",
            "object_primary": ConstantObjectHandle.Object.PROFILES,
            "object_attribute": ConstantObjectHandle.ObjectAttribute.PROFILE_DYNAMIC_EVENT,
            "data_type": "data_in",
            "extract_config": {},
            "target_push_data_transform": {
                "callback_type": "queue",
                "queue_config": {
                    "target": "profiling-de-unify-find-or-insert",
                },
            },
        },
        {
            "merchant_id": "default",
            "source_type": "databases",
            "source_key": "db2",
            "object_primary": ConstantObjectHandle.Object.PROFILES,
            "object_attribute": ConstantObjectHandle.ObjectAttribute.PROFILE_DYNAMIC_EVENT,
            "data_type": "data_in",
            "extract_config": {},
            "target_push_data_transform": {
                "callback_type": "queue",
                "queue_config": {
                    "target": "profiling-de-unify-find-or-insert",
                },
            },
        },
        {
            "merchant_id": "default",
            "source_type": "databases",
            "source_key": "postgres",
            "object_primary": ConstantObjectHandle.Object.PROFILES,
            "object_attribute": ConstantObjectHandle.ObjectAttribute.PROFILE_PRODUCT_HOLING,
            "data_type": "data_in",
            "extract_config": {},
            "target_push_data_transform": {
                "callback_type": "queue",
                "queue_config": {
                    "target": "profiling-ph-unify-find-or-insert",
                },
            },
        },
        {
            "merchant_id": "default",
            "source_type": "databases",
            "source_key": "mysql",
            "object_primary": ConstantObjectHandle.Object.PROFILES,
            "object_attribute": ConstantObjectHandle.ObjectAttribute.PROFILE_PRODUCT_HOLING,
            "data_type": "data_in",
            "extract_config": {},
            "target_push_data_transform": {
                "callback_type": "queue",
                "queue_config": {
                    "target": "profiling-ph-unify-find-or-insert",
                },
            },
        },
        {
            "merchant_id": "default",
            "source_type": "databases",
            "source_key": "sqlserver",
            "object_primary": ConstantObjectHandle.Object.PROFILES,
            "object_attribute": ConstantObjectHandle.ObjectAttribute.PROFILE_PRODUCT_HOLING,
            "data_type": "data_in",
            "extract_config": {},
            "target_push_data_transform": {
                "callback_type": "queue",
                "queue_config": {
                    "target": "profiling-ph-unify-find-or-insert",
                },
            },
        },
        {
            "merchant_id": "default",
            "source_type": "databases",
            "source_key": "oracle",
            "object_primary": ConstantObjectHandle.Object.PROFILES,
            "object_attribute": ConstantObjectHandle.ObjectAttribute.PROFILE_PRODUCT_HOLING,
            "data_type": "data_in",
            "extract_config": {},
            "target_push_data_transform": {
                "callback_type": "queue",
                "queue_config": {
                    "target": "profiling-ph-unify-find-or-insert",
                },
            },
        },
        {
            "merchant_id": "default",
            "source_type": "server",
            "source_key": "api",
            "object_primary": ConstantObjectHandle.Object.PROFILES,
            "object_attribute": ConstantObjectHandle.ObjectAttribute.PROFILE_PRODUCT_HOLING,
            "data_type": "data_in",
            "extract_config": {},
            "target_push_data_transform": {
                "callback_type": "queue",
                "queue_config": {
                    "target": "profiling-ph-unify-find-or-insert",
                },
            },
        },
        {
            "merchant_id": "default",
            "source_type": "databases",
            "source_key": "db2",
            "object_primary": ConstantObjectHandle.Object.PROFILES,
            "object_attribute": ConstantObjectHandle.ObjectAttribute.PROFILE_PRODUCT_HOLING,
            "data_type": "data_in",
            "extract_config": {},
            "target_push_data_transform": {
                "callback_type": "queue",
                "queue_config": {
                    "target": "profiling-ph-unify-find-or-insert",
                },
            },
        },
    ]

    for data_config in multi_config:
        filter_option = {
            "merchant_id": data_config["merchant_id"],
            "source_type": data_config["source_type"],
            "source_key": data_config["source_key"],
            "data_type": data_config["data_type"],
        }
        if data_config.get("object_primary"):
            filter_option["object_primary"] = data_config["object_primary"]
        if data_config.get("object_attribute"):
            filter_option["object_attribute"] = data_config["object_attribute"]
        SettingAddOnConnectorModel().upsert_setting(
            filter_option,
            data_config,
        )
    print("Done")


if __name__ == "__main__":
    update_multi_config_add_on_connector()
