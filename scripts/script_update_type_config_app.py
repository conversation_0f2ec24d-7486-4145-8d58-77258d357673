#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 27/08/2024
"""
from mobio.libs.logging import MobioLogging

from src.common.data_flow_constant import ConstantTypeConfigApp
from src.models.data_flow.config_app_model import <PERSON>fig<PERSON><PERSON><PERSON><PERSON>, ConfigAppModel


def script_update_type_config_app():
    status_update = ConfigAppModel().update_many(
        {ConfigAppField.TYPE: {"$exists": False}}, {"$set": {ConfigAppField.TYPE: ConstantTypeConfigApp.USER_CREATE}}
    )

    MobioLogging().info("Script update :: %s" % status_update)


if __name__ == "__main__":
    script_update_type_config_app()
