#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 29/03/2024
"""

import sys

from src.models.data_flow.general_configuration_parameters_model import \
    GeneralConfigurationParametersModel

if __name__ == "__main__":
    ip_whitelist = sys.argv[1] if len(sys.argv) > 1 else None
    if not ip_whitelist:
        print("No IP whitelist")
        sys.exit(1)
    ip_whitelist = ip_whitelist.split(",")
    GeneralConfigurationParametersModel().upsert_ip_whitelist(ip_whitelist)
    print("Setting IP whitelist")
