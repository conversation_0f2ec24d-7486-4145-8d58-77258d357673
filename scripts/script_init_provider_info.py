import os
import sys

from mobio.libs.logging import MobioLogging
from mobio.sdks.media.mobio_media_sdk import MobioMediaSDK

from configs import MarketPlaceApplicationConfig, RedisConfig
from src.common.utils import get_time_now
from src.models.provider_model import ProviderModel

MobioMediaSDK().config(
    redis_uri=RedisConfig.REDIS_URI,
    admin_host=MarketPlaceApplicationConfig.ADMIN_HOST,
    cache_prefix=RedisConfig.CACHE_PREFIX,
)

data = [
    {
        "image": "provider_images/vietguys.png",
        "provider": "Vietguys",
        "provider_link_image": "https://t1.mobio.vn/static/provider_images/vietguys.png",
        "provider_type": 105,
        "connect_config_info": [
            {
                "key": "username",
                "field": "auth_name",
                "key_name": {"vi": "<PERSON>ê<PERSON> đăng nhậ<PERSON>", "en": "Username"},
                "description": {
                    "vi": "<PERSON>ã đối tác của doanh nghiệp tại hệ thống cung cấp dịch vụ SMS",
                    "en": "The business's partner code in the SMS service providing system",
                },
                "decrypt": True,
            },
            {
                "key": "password",
                "field": "auth_pass",
                "key_name": {"vi": "Mật khẩu", "en": "Password"},
                "description": {
                    "vi": "Key bảo mật của tài khoản doanh nghiệp tại hệ thống cung cấp dịch vụ SMS",
                    "en": "Security key of the business account at the SMS service provider system",
                },
                "decrypt": True,
            },
            {
                "key": "api",
                "key_name": {"vi": "Provider Api", "en": "Provider Api"},
                "description": {
                    "vi": "Url API do nhà cung cấp dịch vụ SMS cung cấp cho doanh nghiệp",
                    "en": "The API Url is provided by the SMS service provider to the business",
                },
                "decrypt": False,
                "not_display_in_detail": True,
            },
        ],
        "type": ["CSKH"],
        "service": "sms",
    },
    {
        "image": "provider_images/vivas.png",
        "provider": "Vivas",
        "provider_link_image": "https://t1.mobio.vn/static/provider_images/vivas.png",
        "provider_type": 115,
        "connect_config_info": [
            {
                "key": "username",
                "field": "auth_name",
                "key_name": {"vi": "Tên đăng nhập", "en": "Username"},
                "description": {
                    "vi": "Tên tài khoản đăng nhập của doanh nghiệp tại hệ thống cung cấp dịch vụ SMS",
                    "en": "The business's login account name at the SMS service providing system",
                },
                "note": {
                    "vi": "Vui lòng nhập chính xác kỹ tự chữ hoa, chữ thường",
                    "en": "Please enter uppercase and lowercase letters carefully",
                },
                "decrypt": True,
            },
            {
                "key": "password",
                "field": "auth_pass",
                "key_name": {"vi": "Mật khẩu", "en": "Password"},
                "description": {
                    "vi": "Key bảo mật của tài khoản doanh nghiệp tại hệ thống cung cấp dịch vụ SMS",
                    "en": "Security key of the business account at the SMS service provider system",
                },
                "decrypt": True,
            },
            {
                "key": "api",
                "key_name": {"vi": "Provider Api", "en": "Provider Api"},
                "description": {
                    "vi": "Url API do nhà cung cấp dịch vụ SMS cung cấp cho doanh nghiệp",
                    "en": "The API Url is provided by the SMS service provider to the business",
                },
                "decrypt": False,
                "not_display_in_detail": True,
            },
        ],
        "keys_connect_for_type": {"CSKH": {}, "QC": {"block_size": 10}},
        "type": ["CSKH", "QC"],
        "service": "sms",
    },
    {
        "image": "provider_images/onesms.png",
        "provider": "Onesms",
        "provider_link_image": "https://t1.mobio.vn/static/provider_images/onesms.png",
        "provider_type": 119,
        "connect_config_info": [
            {
                "key": "username",
                "field": "auth_name",
                "key_name": {"vi": "Tên đăng nhập", "en": "Username"},
                "description": {
                    "vi": "Tên tài khoản đăng nhập của doanh nghiệp tại hệ thống cung cấp dịch vụ SMS",
                    "en": "The business's login account name at the SMS service providing system",
                },
                "decrypt": True,
            },
            {
                "key": "password",
                "field": "auth_pass",
                "key_name": {"vi": "Mật khẩu", "en": "Password"},
                "description": {
                    "vi": "Key bảo mật của tài khoản doanh nghiệp tại hệ thống cung cấp dịch vụ SMS",
                    "en": "Security key of the business account at the SMS service provider system",
                },
                "decrypt": True,
            },
            {
                "key": "api",
                "key_name": {"vi": "Provider Api", "en": "Provider Api"},
                "description": {
                    "vi": "Url API do nhà cung cấp dịch vụ SMS cung cấp cho doanh nghiệp",
                    "en": "The API Url is provided by the SMS service provider to the business",
                },
                "decrypt": False,
                "not_display_in_detail": True,
            },
        ],
        "keys_connect_for_type": {"CSKH": {}, "QC": {"block_size": 100}},
        "type": ["CSKH", "QC"],
        "service": "sms",
    },
    {
        "image": "provider_images/vnpay.png",
        "provider": "Vnpay",
        "provider_link_image": "https://t1.mobio.vn/static/provider_images/vnpay.png",
        "provider_type": 120,
        "connect_config_info": [
            {
                "key": "PartnerCode",
                "field": "auth_name",
                "key_name": {"vi": "Mã đối tác", "en": "Partner code"},
                "description": {
                    "vi": "Mã đối tác của doanh nghiệp tại hệ thống cung cấp dịch vụ SMS",
                    "en": "The business's partner code in the SMS service providing system",
                },
                "decrypt": True,
            },
            {
                "key": "SecretKey",
                "field": "auth_pass",
                "key_name": {"vi": "Secret key", "en": "Secret key"},
                "description": {
                    "vi": "Key bảo mật của doanh nghiệp tại hệ thống cung cấp dịch vụ SMS",
                    "en": "Enterprise security key in the SMS service provision system",
                },
                "decrypt": True,
            },
            {
                "key": "api",
                "key_name": {"vi": "Provider Api", "en": "Provider Api"},
                "description": {
                    "vi": "Url API do nhà cung cấp dịch vụ SMS cung cấp cho doanh nghiệp",
                    "en": "The API Url is provided by the SMS service provider to the business",
                },
                "decrypt": False,
                "not_display_in_detail": True,
            },
        ],
        "type": ["CSKH"],
        "service": "sms",
    },
    {
        "image": "provider_images/stringee.png",
        "provider": "Stringee",
        "provider_link_image": "https://t1.mobio.vn/static/provider_images/stringee.png",
        "provider_type": 106,
        "connect_config_info": [
            {
                "key": "api_key_sid",
                "field": "auth_name",
                "key_name": {"vi": "Account SID", "en": "Account SID"},
                "description": {
                    "vi": "Account SID của doanh nghiệp tại hệ thống cung cấp dịch vụ SMS",
                    "en": "Account SID of the business at the SMS service providing system",
                },
                "decrypt": True,
            },
            {
                "key": "api_key_secret",
                "field": "auth_pass",
                "key_name": {"vi": "Account key", "en": "Account key"},
                "description": {
                    "vi": "Key bảo mật của tài khoản doanh nghiệp tại hệ thống cung cấp dịch vụ SMS",
                    "en": "Security key of the business account at the SMS service provider system",
                },
                "decrypt": True,
            },
            {
                "key": "api",
                "key_name": {"vi": "Provider Api", "en": "Provider Api"},
                "description": {
                    "vi": "Url API do nhà cung cấp dịch vụ SMS cung cấp cho doanh nghiệp",
                    "en": "The API Url is provided by the SMS service provider to the business",
                },
                "decrypt": False,
                "not_display_in_detail": True,
            },
        ],
        "type": ["CSKH"],
        "service": "sms",
    },
    {
        "image": "provider_images/mobio_mailer.png",
        "token": "Basic " + sys.argv[1],
        "host": sys.argv[2],
        "provider": "Mobio Mailer",
        "provider_type": 212,
        "connect_config_info": [],
        "type": ["message", "notification"],
        "service": "email",
        "connect_config_info": [
            {"field": "auth_name", "value": "momailer"},
            {
                "field": "auth_pass",
                "value": "Basic " + sys.argv[3],
            },
            {"field": "provider_api", "value": sys.argv[4]},
        ],
    },
    {
        "image": "provider_images/aws_ses.png",
        "provider": "SES",
        "provider_name": "Amazon SES",
        "provider_link_image": "https://t1.mobio.vn/static/provider_images/aws_ses.png",
        "provider_type": 203,
        "provider_ses_transaction_type": 204,
        "url_callback": "{}nm/webhook/api/v2.0/webhook/ses".format(str(os.environ.get("PUBLIC_HOST"))),
        "connect_config_info": [
            {
                "key": "AWS_ACCESS_KEY_ID",
                "field": "auth_name",
                "key_name": {"vi": "SMTP username", "en": "SMTP username"},
                "description": {
                    "vi": "",
                    "en": "",
                },
                "decrypt": False,
            },
            {
                "key": "AWS_SECRET_ACCESS_KEY",
                "field": "auth_pass",
                "key_name": {"vi": "SMTP password", "en": "SMTP password"},
                "description": {
                    "vi": "",
                    "en": "",
                },
                "decrypt": True,
                "not_display_in_detail": True,
            },
            {
                "key": "config_set",
                "key_name": {"vi": "Configuration set name", "en": "Configuration set name"},
                "description": {
                    "vi": "",
                    "en": "",
                },
                "decrypt": True,
            },
            {
                "key": "region",
                "key_name": {"vi": "Region SES", "en": "Region SES"},
                "description": {
                    "vi": "",
                    "en": "",
                },
                "decrypt": False,
            },
        ],
        "type": ["message", "notification"],
        "service": "email",
    },
]


class InitProviderInfo:
    def create_provider_info(self):
        func_name = self.create_provider_info.__name__
        provider_model: ProviderModel = ProviderModel()
        for item in data:
            image_url = item.get("image")

            MobioLogging().info("{} :: {} :: image_url :: {}".format(self.__class__.__name__, func_name, image_url))

            file_info = MobioMediaSDK().upload_with_kafka(
                merchant_id="provider_images_for_all_merchant", file_path=image_url, expire=None
            )
            url_public = file_info.get("url")

            item.update({"provider_link_image": url_public})

            provider_model.upsert_provider(item, "admin")


if __name__ == "__main__":
    InitProviderInfo().create_provider_info()
