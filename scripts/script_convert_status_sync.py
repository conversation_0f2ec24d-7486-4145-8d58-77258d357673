#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 10/04/2025
"""


from mobio.libs.logging import MobioLogging

from src.common.data_flow_constant import (
    ConstantStatusConfigConnector,
    ConstantStatusSyncData,
)
from src.models.data_flow.config_connectors_model import (
    ConfigConnectorsModel,
    ConstantConfigConnectorsModel,
)
from src.models.reports.mongodb.daily_reports_model import DailyReportsModel


def script_convert_status_sync():
    config_connectors_not_sync_data = ConfigConnectorsModel().find(
        {
            "$or": [
                {"status_sync": ConstantStatusSyncData.NOT_DATA_SYNC},
                {"status_sync": None},
            ],
            ConstantConfigConnectorsModel.STATUS: ConstantStatusConfigConnector.ACTIVE,
        }
    )
    config_connectors_not_sync_data_ids = [
        config_connector.get("_id") for config_connector in config_connectors_not_sync_data
    ]

    if not config_connectors_not_sync_data_ids:
        MobioLogging().info("No config connectors not sync data")
        return
    pipeline_query = [
        {"$match": {"connector_id": {"$in": config_connectors_not_sync_data_ids}}},
        {"$sort": {"start_time": -1}},
        {
            "$group": {
                "_id": {"connector_id": "$connector_id", "session_id": "$session_id"},
                "process_status": {"$first": "$process_status"},
                "start_time": {"$first": "$start_time"},  # (Tùy chọn) giữ lại start_time để kiểm tra
            }
        },
        {
            "$project": {
                "_id": 0,
                "connector_id": "$_id.connector_id",
                "session_id": "$_id.session_id",
                "process_status": 1,
                "start_time": 1,
            }
        },
    ]

    mapping_status_sync = {
        "sync_error": ConstantStatusSyncData.SYNC_ERROR,
        "finished": ConstantStatusSyncData.DONE,
        "stopped": ConstantStatusSyncData.STOPPED,
        "stop": ConstantStatusSyncData.STOPPED,
        "error": ConstantStatusSyncData.ERROR,
        "init": ConstantStatusSyncData.INIT,
        "running": ConstantStatusSyncData.RUNNING,
        "prepare": ConstantStatusSyncData.PREPARE,
    }

    data_result = list(DailyReportsModel().aggregate(pipeline_query))
    for data in data_result:
        connector_id = data.get("connector_id")
        process_status = data.get("process_status")
        if not mapping_status_sync.get(process_status):
            MobioLogging().info(
                "script_convert_status_sync :: processing connector_id: {}, process_status: {}".format(
                    connector_id, process_status
                )
            )
            continue
        status = ConfigConnectorsModel().update_one_query(
            {"_id": connector_id},
            {"status_sync": mapping_status_sync.get(process_status)},
        )
        MobioLogging().info(
            "script_convert_status_sync :: processing connector_id: {}, status: {}".format(connector_id, status)
        )
        if connector_id in config_connectors_not_sync_data_ids:
            config_connectors_not_sync_data_ids.remove(connector_id)
    if config_connectors_not_sync_data_ids:
        MobioLogging().info(
            "script_convert_status_sync :: processing connector_id: {}".format(config_connectors_not_sync_data_ids)
        )


if __name__ == "__main__":
    script_convert_status_sync()
