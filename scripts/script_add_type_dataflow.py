#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 24/06/2024
"""

from mobio.libs.logging import MobioLogging

from src.models.data_flow.config_connectors_model import ConfigConnectorsModel

if __name__ == "__main__":
    number_update = ConfigConnectorsModel().update_by_set({"data_type": {"$exists": False}}, {"data_type": "data_in"})
    MobioLogging().info("Number update source data in %s" % number_update)
    print("DONE")
