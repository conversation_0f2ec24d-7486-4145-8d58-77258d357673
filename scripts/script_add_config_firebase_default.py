#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 08/10/2024
"""

from src.models.connect_config_model import (
    AppConnectType,
    ConnectConfigField,
    ConnectConfigModel,
    TypeConnectConfig,
)

if __name__ == "__main__":

    # Filter config firebase default
    filter_option = {
        ConnectConfigField.TYPE: TypeConnectConfig.DEFAULT,
        ConnectConfigField.APP_CONNECT_TYPE: AppConnectType.FIREBASE,
    }
    if not ConnectConfigModel().find_one(filter_option):
        ConnectConfigModel().insert_document(
            {
                "server_key": "test",
                "api_key": "test",
                "database_url": "test",
                "project_id": "test",
                "messagingsender_id": "test",
                "app_id": "test",
                "measurement_id": "",
                "type": "DEFAULT",
                "storage_bucket": "test",
                "auth_domain": "test",
                "connect_type": "firebase",
                "merchant_id": "DEFAULT",
            }
        )
    print("DONE")
