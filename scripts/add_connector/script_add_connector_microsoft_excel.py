#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 16/01/2025
"""


from datetime import UTC, datetime

from src.models.data_flow.config_connectors_model import ConfigConnectorsModel


def add_connector_microsoft_excel():

    # Update connector microsoft excel to raw_data
    ConfigConnectorsModel().update_by_set(
        {"source_key": "microsoft_excel", "source_type": "microsoft_excel"},
        {"source_type": "raw_data"},
    )

    merchant_ids = ConfigConnectorsModel().distinct(
        "merchant_id",
        {},
    )
    for merchant_id in merchant_ids:

        is_exit_microsoft_excel = ConfigConnectorsModel().find_one(
            {"merchant_id": merchant_id, "source_key": "microsoft_excel"},
        )
        if is_exit_microsoft_excel:
            continue

        print(f"Add connector Microsoft Excel for merchant_id: {merchant_id}")
        data_add = {
            "created_by": "system",
            "status": "active",
            "status_connect": "on",
            "updated_by": "system",
            "created_time": datetime.now(UTC),
            "updated_time": datetime.now(UTC),
            "source_key": "microsoft_excel",
            "source_type": "raw_data",
            "config_connect": {},
            "name": "Microsoft Excel",
            "keywords": "microsoft excel",
            "name_ascii": "microsoft excel",
            "log_connection_information": {
                "status_connect": "success",
            },
            "data_type": "data_out",
            "connector_identification": "",
            "version": 2,
            "action_time_change_status_connect": None,
            "auto_connection_check_interval": 10,
            "config_attribute": None,
            "config_information_out": [],
            "config_sync_calendar": {},
            "contact_info": [],
        }
        ConfigConnectorsModel().insert_connector(merchant_id, "system", **data_add)

        print("Add connector Microsoft Excel successfully!")


if __name__ == "__main__":
    add_connector_microsoft_excel()
