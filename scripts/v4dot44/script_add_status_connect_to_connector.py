#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 29/04/2025
"""


from src.common.data_flow_constant import ConstantStatusConnect
from src.models.data_flow.config_connectors_model import ConfigConnectorsModel


def add_field_status_connect_to_connector():
    filter_option = {"status_connect": {"$exists": False}}
    token_query = None
    while True:
        connectors, token_query = ConfigConnectorsModel().find_paginate_load_more(
            filter_option, per_page=1000, after_token=token_query
        )
        for connector in connectors:
            if "status_connect" not in connector:
                ConfigConnectorsModel().update_by_set(
                    {"_id": connector["_id"]}, {"status_connect": ConstantStatusConnect.OFF}
                )
        if not connectors:
            break
    print("Done add field status_connect to connector")


if __name__ == "__main__":
    add_field_status_connect_to_connector()
