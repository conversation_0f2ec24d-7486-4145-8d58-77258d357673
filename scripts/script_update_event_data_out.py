import datetime
import json
import re

from mobio.sdks.admin.utils import split_list
from pymongo import UpdateOne

from src.common import WORKING_DIR
from src.models.data_flow.information_event_data_out_model import (
    InformationEventDataOutField,
    InformationEventDataOutModel,
)

event_file = WORKING_DIR + "/resources/information_event_data_out/event.json"


def replace_style_quotes(text):
    # Pattern to find style="any_string"
    pattern = r'="([^"]*)"'
    # Replace with =\\"text\\"
    replacement = r'=\\"\1\\"'
    new_text = re.sub(pattern, replacement, text)
    return new_text


if __name__ == "__main__":
    with open(event_file, "r") as f:
        indexes_data = json.load(f)
    list_event = []
    for item in indexes_data:
        event_key = item.get(InformationEventDataOutField.EVENT_KEY)
        data_test = item.get(InformationEventDataOutField.DATA_TEST)
        event_type = item.get(InformationEventDataOutField.TYPE, InformationEventDataOutField.ConstantType.ALL)
        if data_test:
            data_test = data_test.replace("None", "null").replace("True", "true").replace("False", "false")
        else:
            data_test = "{}"
        obj_update = {
            InformationEventDataOutField.EVENT_NAME: item.get(InformationEventDataOutField.EVENT_NAME),
            InformationEventDataOutField.GROUP_CODE: item.get(InformationEventDataOutField.GROUP_CODE),
            InformationEventDataOutField.EVENT_KEY: event_key,
            InformationEventDataOutField.STATUS: item.get(InformationEventDataOutField.STATUS),
            InformationEventDataOutField.LINK_DOC: item.get(InformationEventDataOutField.LINK_DOC),
            InformationEventDataOutField.DATA_TEST: replace_style_quotes(data_test),
            InformationEventDataOutField.POSITION: item.get(InformationEventDataOutField.POSITION),
            InformationEventDataOutField.TYPE: event_type,
        }
        list_event.append(
            UpdateOne(
                {InformationEventDataOutField.EVENT_KEY: event_key},
                {
                    "$set": obj_update,
                    "$setOnInsert": {
                        InformationEventDataOutField.CREATED_TIME: datetime.datetime.now(datetime.UTC),
                        InformationEventDataOutField.UPDATED_TIME: datetime.datetime.now(datetime.UTC),
                    },
                },
                upsert=True,
            )
        )
    if list_event:
        chunks = split_list(list_event, 50)
        for chunk in chunks:
            result = InformationEventDataOutModel().bulk_write_data(chunk)
            print(result.bulk_api_result)
    print("Update event data out success")
