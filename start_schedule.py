import sys

from mobio.libs.schedule import SchedulerFactory

from configs import RedisConfig

if __name__ == "__main__":
    name_service = sys.argv[1]
    factory = SchedulerFactory()
    redis_uri = RedisConfig.REDIS_URI
    redis_cluster_uri = RedisConfig.REDIS_CLUSTER_URI
    redis_type = RedisConfig.REDIS_TYPE

    if name_service == "verify-dns-active":
        from src.schedules.provider.schedule_verify_dns_active import (
            VerifyDnsActiveScheduler,
        )

        factory.add(VerifyDnsActiveScheduler(redis_uri=redis_uri))
    if name_service == "check-dns-record-is-deleted":
        from src.schedules.provider.schedule_check_dns_record_is_deleted import (
            CheckDnsRecordIsDeletedScheduler,
        )

        factory.add(CheckDnsRecordIsDeletedScheduler(redis_uri=redis_uri))
    if name_service == "marketplace-sch-report-status-sync-data":
        from src.schedule.schedule_report_status_sync_data import (
            ReportStatusSyncDataSchedule,
        )

        factory.add(
            ReportStatusSyncDataSchedule(
                redis_uri=redis_uri, redis_cluster_uri=redis_cluster_uri, redis_type=redis_type
            )
        )
    factory.run()
