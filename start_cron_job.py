#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 06/09/2024
"""

import sys

if __name__ == "__main__":

    name_cronjob = sys.argv[1]
    if name_cronjob == "send-noti-report-result-connector":
        from src.cron_job.cron_job_report_time_frame import (
            cron_job_send_noti_report_result_connector,
        )

        cron_job_send_noti_report_result_connector()
    if name_cronjob == "agg-df-log-to-daily-report":
        from src.cron_job.cron_job_agg_report_df_log_to_daily_report import (
            cron_job_agg_report_df_log_to_daily_report,
        )

        cron_job_agg_report_df_log_to_daily_report()
