#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 03/04/2025
"""


import datetime

from mobio.libs.logging import MobioLogging

from src.common.data_flow_constant import (
    ConstantModeConfigSyncCalendar,
    ConstantStatusSyncData,
)
from src.common.utils import get_list_object_query_starrock
from src.models.data_flow.config_connectors_model import ConfigConnectorsModel
from src.models.reports.mongodb.daily_reports_model import DailyReportsModel
from src.models.reports.olap.dataflow.data_flow_model import DataFlowDialect

mapping_status_sync = {
    "sync_error": ConstantStatusSyncData.SYNC_ERROR,
    "finished": ConstantStatusSyncData.DONE,
    "stopped": ConstantStatusSyncData.STOPPED,
    "stop": ConstantStatusSyncData.STOPPED,
    "error": ConstantStatusSyncData.ERROR,
    "init": ConstantStatusSyncData.INIT,
    "running": ConstantStatusSyncData.RUNNING,
    "prepare": ConstantStatusSyncData.PREPARE,
}


def insert_sessions_data_default(session_ids):
    session_by_ids = DataFlowDialect().process_query(
        "SELECT * FROM data_flow.pipeline_session WHERE session_id IN :session_ids",
        {"session_ids": session_ids},
    )

    lst_data_insert = []
    for session in session_by_ids:
        pipeline_name = session.pipeline_name
        connector_id = pipeline_name.split("_")[1] if "_" in pipeline_name else None
        if not connector_id or connector_id.isdigit() is False:
            MobioLogging().info(f"No connector_id for {session.session_id}, pipeline_name: {pipeline_name}")
            continue
        filter_data = {
            "connector_id": int(connector_id),
            "session_id": session.session_id,
        }
        daily_report = DailyReportsModel().find_one(filter_data)
        process_status = session.process_status
        if daily_report:
            MobioLogging().info(f"Daily report already exists for {session.session_id}")
            if session.mode == "batch":
                data_daily_report_update = {
                    "connector_id": int(connector_id),
                    "session_id": session.session_id,
                    "start_time": session.start_time,
                    "end_time": session.end_time,
                    "connector_name": session.connector_name,
                    "consume_status": session.consume_status,
                    "process_status": process_status,
                    "mode": session.mode,
                }

                DailyReportsModel().update_one_query(filter_data, data_daily_report_update)
                status_update = ConfigConnectorsModel().update_one_query(
                    {"connector_id": int(connector_id)},
                    {"$set": {"status_sync": mapping_status_sync.get(process_status)}},
                )
                MobioLogging().info(f"Update status sync for :: {connector_id} :: {status_update}")
            continue

        data_daily_report_default = {
            "connector_id": int(connector_id),
            "session_id": session.session_id,
            "total_rows": 0,
            "total_rows_success": 0,
            "total_rows_add": 0,
            "total_rows_updated": 0,
            "total_rows_error": 0,
            "total_rows_find": 0,
            "start_time": session.start_time,
            "end_time": session.end_time,
            "connector_name": session.connector_name,
            "consume_status": session.consume_status,
            "process_status": process_status,
            "mode": session.mode,
            "profile_total_rows": 0,
            "profile_total_rows_success": 0,
            "profile_total_rows_add": 0,
            "profile_total_rows_updated": 0,
            "profile_total_rows_error": 0,
            "profile_total_rows_find": 0,
            "dynamic_event_total_rows": 0,
            "dynamic_event_total_rows_success": 0,
            "dynamic_event_total_rows_add": 0,
            "dynamic_event_total_rows_updated": 0,
            "dynamic_event_total_rows_error": 0,
            "dynamic_event_total_rows_find": 0,
            "product_holding_total_rows": 0,
            "product_holding_total_rows_success": 0,
            "product_holding_total_rows_add": 0,
            "product_holding_total_rows_updated": 0,
            "product_holding_total_rows_error": 0,
            "product_holding_total_rows_find": 0,
            "company_total_rows": 0,
            "company_total_rows_success": 0,
            "company_total_rows_add": 0,
            "company_total_rows_updated": 0,
            "company_total_rows_error": 0,
            "company_total_rows_find": 0,
            "ticket_total_rows": 0,
            "ticket_total_rows_success": 0,
            "ticket_total_rows_add": 0,
            "ticket_total_rows_updated": 0,
            "ticket_total_rows_error": 0,
            "ticket_total_rows_find": 0,
            "deal_total_rows": 0,
            "deal_total_rows_success": 0,
            "deal_total_rows_add": 0,
            "deal_total_rows_updated": 0,
            "deal_total_rows_error": 0,
            "deal_total_rows_find": 0,
        }
        lst_data_insert.append(data_daily_report_default)
        MobioLogging().info(f"Insert data daily report default for {session.session_id} {process_status}")
        ConfigConnectorsModel().update_one_query(
            {"connector_id": int(connector_id)},
            {"$set": {"status_sync": mapping_status_sync.get(process_status)}},
        )

    if lst_data_insert:
        MobioLogging().info(f"Insert {len(lst_data_insert)} data")
        status = DailyReportsModel().insert_many(lst_data_insert)
        MobioLogging().info(f"Insert {len(lst_data_insert)} data {status}")


def cron_job_agg_report_df_log_to_daily_report():
    MobioLogging().info("Start cron_job_agg_report_df_log_to_daily_report")
    time_now = datetime.datetime.now(datetime.UTC)
    start_time = (time_now.replace(hour=0, minute=0, second=0, microsecond=0) - datetime.timedelta(hours=7)).strftime(
        "%Y-%m-%d %H:%M:%S"
    )
    end_time = (
        time_now.replace(hour=23, minute=59, second=59, microsecond=999999) - datetime.timedelta(hours=7)
    ).strftime("%Y-%m-%d %H:%M:%S")
    MobioLogging().info(f"Start time: {start_time}, End time: {end_time}")
    # Get all connector_id from data_flow.dataflow_log
    session_ids = DataFlowDialect().process_query(
        "SELECT DISTINCT session_id FROM data_flow.dataflow_log WHERE created_time >= :start_time AND created_time <= :end_time",
        {"start_time": start_time, "end_time": end_time},
    )
    session_ids = [session_id[0] for session_id in session_ids]

    # Get session init in pipeline_session
    session_init_in_pipeline_session = DataFlowDialect().process_query(
        "SELECT DISTINCT session_id FROM data_flow.pipeline_session WHERE created_time >= :start_time AND created_time <= :end_time",
        {"start_time": start_time, "end_time": end_time},
    )
    session_init_in_pipeline_session = [session_id[0] for session_id in session_init_in_pipeline_session]

    MobioLogging().info(f"Session IDs: {session_ids}")

    lst_object = get_list_object_query_starrock()

    connector_session = DataFlowDialect().process_query(
        "SELECT connector_id, session_id FROM data_flow.dataflow_log WHERE session_id IN :session_ids GROUP BY connector_id, session_id",
        {"session_ids": session_ids},
    )
    if connector_session:
        for item in connector_session:
            connector_id = item[0]
            session_id = item[1]
            detail_connector = ConfigConnectorsModel().find_one(
                {"_id": int(connector_id)}, {"object_attribute": 1, "object": 1, "config_sync_calendar": 1}
            )
            if not detail_connector:
                MobioLogging().info(f"No connector_id for {connector_id}")
                continue

            config_sync_calendar = detail_connector.get("config_sync_calendar")
            object_attribute = detail_connector.get("object_attribute")
            object = detail_connector.get("object")
            mode_streaming = config_sync_calendar.get("mode") == ConstantModeConfigSyncCalendar.STREAMING

            where_condition = "ps.session_id IN :session_ids"

            if mode_streaming:
                where_condition = (
                    "ps.session_id IN :session_ids AND sr.created_time >= :start_time AND sr.created_time <= :end_time"
                )

            smt1 = f"""
            WITH deduplicated_data AS (
                SELECT 
                    connector_id,
                    session_id,
                    message_id,
                    state,
                    event_value,
                    result,
                    action,
                    created_time
                FROM (
                    SELECT 
                        connector_id,
                        session_id,
                        message_id,
                        state,
                        event_value,
                        result,
                        action,
                        created_time,
                        ROW_NUMBER() OVER (
                            PARTITION BY session_id, message_id, state
                            ORDER BY created_time ASC
                        ) AS row_num
                    FROM data_flow.dataflow_log
                    WHERE session_id IN :session_ids
                ) AS tmp
                WHERE row_num = 1
            )
            SELECT
                sr.connector_id,
                sr.session_id,
                SUM(CASE WHEN sr.state = 'consume' THEN 1 ELSE 0 END) AS total_rows,
                SUM(CASE WHEN sr.state = 'processed' AND sr.event_value IS NULL THEN 1 ELSE 0 END) AS total_rows_success,
                SUM(CASE WHEN sr.state = 'processed' AND sr.event_value IS NULL AND sr.action = 'add' THEN 1 ELSE 0 END) AS total_rows_add,
                SUM(CASE WHEN sr.state = 'processed' AND sr.event_value IS NULL AND sr.action = 'update' THEN 1 ELSE 0 END) AS total_rows_updated,
                SUM(CASE WHEN sr.state = 'error' THEN 1 ELSE 0 END) AS total_rows_error,
                SUM(CASE WHEN sr.state = 'processed' AND sr.event_value IS NULL AND sr.action = 'find' THEN 1 ELSE 0 END) AS total_rows_find,
                MIN(ps.start_time) AS start_time,
                MAX(ps.end_time) AS end_time,
                MAX(ps.connector_name) AS connector_name,
                MAX(ps.consume_status) AS consume_status,
                MAX(ps.process_status) AS process_status,
                MAX(ps.mode) AS mode,
                SUM(CASE WHEN sr.state = 'consume' AND (get_json_string(sr.event_value, '$.dynamic_event_data') IS NOT NULL OR get_json_string(sr.event_value, '$.product_holding_data') IS NOT NULL) THEN 1 ELSE 0 END) AS profile_total_rows,
                SUM(CASE WHEN sr.state in ('processed') AND IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed' THEN 1 ELSE 0 END) AS profile_total_rows_success,
                SUM(CASE WHEN sr.state in ('processed') AND IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') = 'add' THEN 1 ELSE 0 END) AS profile_total_rows_add,
                SUM(CASE WHEN sr.state in ('processed') AND IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') = 'update' THEN 1 ELSE 0 END) AS profile_total_rows_updated,
                SUM(CASE WHEN sr.state != 'consume' AND IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'error' THEN 1 ELSE 0 END) AS profile_total_rows_error,
                SUM(CASE WHEN sr.state in ('processed') AND IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') = 'find' THEN 1 ELSE 0 END) AS profile_total_rows_find,
                SUM(CASE WHEN sr.state = 'consume' AND get_json_string(sr.event_value, '$.dynamic_event_data') IS NOT NULL THEN 1 ELSE 0 END) AS dynamic_event_total_rows,
                SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.dynamic_event.state'), '') = 'processed' THEN 1 ELSE 0 END) AS dynamic_event_total_rows_success,
                SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.dynamic_event.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.dynamic_event.action'), '') = 'add' THEN 1 ELSE 0 END) AS dynamic_event_total_rows_add,
                SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.dynamic_event.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.dynamic_event.action'), '') = 'update' THEN 1 ELSE 0 END) AS dynamic_event_total_rows_updated,
                SUM(CASE WHEN sr.state != 'consume' AND IFNULL(get_json_string(sr.result, '$.dynamic_event.state'), '') = 'error' THEN 1 ELSE 0 END) AS dynamic_event_total_rows_error,
                SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.dynamic_event.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.dynamic_event.action'), '') = 'find' THEN 1 ELSE 0 END) AS dynamic_event_total_rows_find,
                SUM(CASE WHEN sr.state = 'consume' AND get_json_string(sr.event_value, '$.product_holding_data') IS NOT NULL THEN 1 ELSE 0 END) AS product_holding_total_rows,
                SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.product_holding.state'), '') = 'processed' THEN 1 ELSE 0 END) AS product_holding_total_rows_success,
                SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.product_holding.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.product_holding.action'), '') = 'add' THEN 1 ELSE 0 END) AS product_holding_total_rows_add,
                SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.product_holding.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.product_holding.action'), '') = 'update' THEN 1 ELSE 0 END) AS product_holding_total_rows_updated,
                SUM(CASE WHEN sr.state != 'consume' AND IFNULL(get_json_string(sr.result, '$.product_holding.state'), '') = 'error' THEN 1 ELSE 0 END) AS product_holding_total_rows_error,
                SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.product_holding.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.product_holding.action'), '') = 'find' THEN 1 ELSE 0 END) AS product_holding_total_rows_find,
                SUM(CASE WHEN sr.state = 'consume' AND get_json_string(sr.event_value, '$.company_data') IS NOT NULL THEN 1 ELSE 0 END) AS company_total_rows,
                SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.company.state'), '') = 'processed' THEN 1 ELSE 0 END) AS company_total_rows_success,
                SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.company.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.company.action'), '') = 'add' THEN 1 ELSE 0 END) AS company_total_rows_add,
                SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.company.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.company.action'), '') = 'update' THEN 1 ELSE 0 END) AS company_total_rows_updated,
                SUM(CASE WHEN sr.state != 'consume' AND IFNULL(get_json_string(sr.result, '$.company.state'), '') = 'error' THEN 1 ELSE 0 END) AS company_total_rows_error,
                SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.company.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.company.action'), '') = 'find' THEN 1 ELSE 0 END) AS company_total_rows_find,
                SUM(CASE WHEN sr.state = 'consume' AND get_json_string(sr.event_value, '$.ticket_data') IS NOT NULL THEN 1 ELSE 0 END) AS ticket_total_rows,
                SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.ticket.state'), '') = 'processed' THEN 1 ELSE 0 END) AS ticket_total_rows_success,
                SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.ticket.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.ticket.action'), '') = 'add' THEN 1 ELSE 0 END) AS ticket_total_rows_add,
                SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.ticket.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.ticket.action'), '') = 'update' THEN 1 ELSE 0 END) AS ticket_total_rows_updated,
                SUM(CASE WHEN sr.state != 'consume' AND IFNULL(get_json_string(sr.result, '$.ticket.state'), '') = 'error' THEN 1 ELSE 0 END) AS ticket_total_rows_error,
                SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.ticket.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.ticket.action'), '') = 'find' THEN 1 ELSE 0 END) AS ticket_total_rows_find,
                SUM(CASE WHEN sr.state = 'consume' AND get_json_string(sr.event_value, '$.deal_data') IS NOT NULL THEN 1 ELSE 0 END) AS deal_total_rows,
                SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.deal.state'), '') = 'processed' THEN 1 ELSE 0 END) AS deal_total_rows_success,
                SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.deal.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.deal.action'), '') = 'add' THEN 1 ELSE 0 END) AS deal_total_rows_add,
                SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.deal.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.deal.action'), '') = 'update' THEN 1 ELSE 0 END) AS deal_total_rows_updated,
                SUM(CASE WHEN sr.state != 'consume' AND IFNULL(get_json_string(sr.result, '$.deal.state'), '') = 'error' THEN 1 ELSE 0 END) AS deal_total_rows_error,
                SUM(CASE WHEN sr.state = 'processed' AND IFNULL(get_json_string(sr.result, '$.deal.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.deal.action'), '') = 'find' THEN 1 ELSE 0 END) AS deal_total_rows_find
            FROM
                deduplicated_data sr
            INNER JOIN data_flow.pipeline_session ps ON
                sr.session_id = ps.session_id
            WHERE
                {where_condition}
            GROUP BY
                sr.connector_id,
                sr.session_id
            ORDER BY
                sr.session_id DESC
            """
            results = DataFlowDialect().process_query(
                smt1, {"session_ids": [session_id], "start_time": start_time, "end_time": end_time}
            )
            if not results:
                MobioLogging().info(f"No data to migrate for {start_time} to {end_time} session_id: {session_id}")
                continue

            results = list(results)
            if not results:
                insert_sessions_data_default(session_init_in_pipeline_session)
                MobioLogging().info(f"No data to migrate for {start_time} to {end_time}")
                return

            for result in results:
                MobioLogging().info(f"Update daily report for {result}")

                result_as_dict = result._asdict()

                result_as_dict.update({"date_aggregate_report": time_now})

                process_status_in_query = result_as_dict["process_status"]
                if session_id in session_init_in_pipeline_session:
                    session_init_in_pipeline_session.remove(session_id)
                start_time_query_daily_report = datetime.datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
                end_time_query_daily_report = datetime.datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
                MobioLogging().info(
                    f"Start time query: {start_time_query_daily_report}, End time query: {end_time_query_daily_report}"
                )
                filter_data = {
                    "connector_id": connector_id,
                    "session_id": session_id,
                }
                if mode_streaming:
                    filter_data.update(
                        {
                            "start_time": {
                                "$gte": start_time_query_daily_report,
                            },
                            "end_time": {
                                "$lte": end_time_query_daily_report,
                            },
                        }
                    )
                    result_as_dict.update(
                        {
                            "start_time": start_time_query_daily_report,
                            "end_time": end_time_query_daily_report.replace(second=0, microsecond=0),
                        }
                    )

                daily_report = DailyReportsModel().find_one(filter_data)
                if daily_report:

                    consume_status_in_daily_report = daily_report.get("consume_status")
                    process_status_in_daily_report = daily_report.get("process_status")
                    if consume_status_in_daily_report in ["finished", "stop"]:
                        # result_as_dict.pop("consume_status")
                        # Xóa total_rows nếu nhỏ hơn hoặc bằng total_rows trong daily_report
                        if result_as_dict.get("total_rows", 0) <= daily_report.get("total_rows", 0):
                            result_as_dict.pop("total_rows")
                        # Xóa total_rows của từng object nếu nhỏ hơn hoặc bằng total_rows của từng object trong daily_report
                        for object_item in lst_object:
                            if result_as_dict.get(f"{object_item}_total_rows", 0) <= daily_report.get(
                                f"{object_item}_total_rows", 0
                            ):
                                result_as_dict.pop(f"{object_item}_total_rows")
                    status = DailyReportsModel().update_one_query(filter_data, data=result_as_dict)
                else:
                    status = DailyReportsModel().insert(result_as_dict)
                ConfigConnectorsModel().update_one_query(
                    {"connector_id": int(connector_id)},
                    {"$set": {"status_sync": mapping_status_sync.get(process_status_in_query)}},
                )
                MobioLogging().info(f"Update daily report for {connector_id} {session_id} {status}")
    if session_init_in_pipeline_session:
        insert_sessions_data_default(session_init_in_pipeline_session)
    print("Done")


if __name__ == "__main__":
    cron_job_agg_report_df_log_to_daily_report()
