#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 20/08/2024
"""


import datetime

from mobio.libs.logging import MobioLogging
from mobio.sdks.notify import MobioNotifySDK

from src.common.data_flow_constant import ConstantStatusConfigConnector
from src.models.data_flow.config_connectors_model import (
    ConfigConnectorsModel,
    ConstantConfigConnectorsModel,
)
from src.models.data_flow.object_handle_model import SettingObjectHandleModel
from src.models.data_flow.source_model import SourceModel
from src.models.reports.olap.dataflow.data_flow_model import DataFlowDialect

mobio_notify_sdk = MobioNotifySDK().config(
    source="market-place"  # source module (nguồn gửi từ module nào) (Ex: 'sale', 'admin')
)


def cron_job_send_noti_report_result_connector():
    MobioLogging().info("Start cron_job_send_noti_report_result_connector")
    # Get all connector status connector status on and is_notification true
    time_now = datetime.datetime.now(datetime.UTC)
    hour_time_now = time_now.hour
    start_time_query = time_now - datetime.timedelta(hours=10)
    start_time_query = None

    if hour_time_now == 2:
        start_time_query = time_now - datetime.timedelta(hours=15)

    if hour_time_now == 7:
        start_time_query = time_now - datetime.timedelta(hours=5)

    if hour_time_now == 11:
        start_time_query = time_now - datetime.timedelta(hours=4)
    if not start_time_query:
        return
    process_job_send_noti_report_result_connector(start_time_query, time_now)


def process_job_send_noti_report_result_connector(start_time_query, time_now):
    start_time = start_time_query.strftime("%Y-%m-%d %H:%M:%S")
    end_time = time_now.strftime("%Y-%m-%d %H:%M:%S")

    MobioLogging().info("Start time: {}, end_time: {}".format(start_time, end_time))
    reports = DataFlowDialect().report_connector_in_range_time(start_time, end_time)
    # reports = DataFlowDialect().report_connector_in_ids([1242])
    mapping_data_report_connector = {}
    for report in reports:
        MobioLogging().info("Report: {}".format(report))
        connector_id = report.connector_id
        total_records = report.consume_count
        total_records_fail = report.error_count
        total_records_success = report.processed_count
        total_records_add = report.processed_add_count
        total_records_update = report.processed_update_count
        total_records_add_related_profile = report.profile_processed_add_count
        total_records_update_related_profile = report.profile_processed_update_count
        total_records_add_related_company = report.company_processed_add_count
        total_records_update_related_company = report.company_processed_update_count
        total_records_add_related_ticket = report.ticket_processed_add_count
        total_records_update_related_ticket = report.ticket_processed_update_count
        total_records_add_related_sale = report.sale_processed_add_count
        total_records_update_related_sale = report.sale_processed_update_count

        if (
            total_records_add
            and not total_records_add_related_profile
            and not total_records_add_related_company
            and not total_records_add_related_ticket
            and not total_records_add_related_sale
        ):
            total_records_add_related_profile = total_records_add
        if (
            total_records_update
            and not total_records_update_related_profile
            and not total_records_update_related_company
            and not total_records_update_related_ticket
            and not total_records_update_related_sale
        ):
            total_records_update_related_profile = total_records_update

        mapping_data_report_connector[connector_id] = {
            "start_time": report.start_time.strftime("%d/%m/%Y"),
            "total_records": total_records,
            "total_records_fail": total_records_fail,
            "total_records_success": total_records_success,
            "total_records_add": total_records_add,
            "total_records_update": total_records_update,
            "total_records_add_related_profile": total_records_add_related_profile,
            "total_records_update_related_profile": total_records_update_related_profile,
            "total_records_add_related_company": total_records_add_related_company,
            "total_records_update_related_company": total_records_update_related_company,
            "total_records_add_related_ticket": total_records_add_related_ticket,
            "total_records_update_related_ticket": total_records_update_related_ticket,
            "total_records_add_related_sale": total_records_add_related_sale,
            "total_records_update_related_sale": total_records_update_related_sale,
        }
    MobioLogging().info("Mapping data report connector: {}".format(mapping_data_report_connector))
    mapping_object_key_send_noti = {
        "profile": "market_place_cdp_data_sync_process_result",
        "profiles": "market_place_cdp_data_sync_process_result",
        "company": "market_place_cdp_data_sync_process_result",
        "ticket": "market_place_data_sync_process_result",
        "sale": "market_place_data_sync_process_result",
    }
    mapping_object = {"profiles": "profile"}
    if mapping_data_report_connector:
        connector_ids = list(mapping_data_report_connector.keys())
        connector_details = ConfigConnectorsModel().find(
            {
                "_id": {"$in": connector_ids},
                "data_type": "data_in",
                ConstantConfigConnectorsModel.STATUS: ConstantStatusConfigConnector.ACTIVE,
            }
        )
        for connector_detail in connector_details:
            MobioLogging().debug("Process connector_detail: {}".format(connector_detail))
            merchant_id = connector_detail.get("merchant_id")
            connector_name = connector_detail.get("name")
            connector_id = connector_detail.get("_id")
            lst_account_id_send_email = []
            lst_other_email = []
            config_sync_calendar = connector_detail.get("config_sync_calendar")
            if not config_sync_calendar:
                continue
            if not config_sync_calendar.get("is_notification"):
                continue
            contact_info = config_sync_calendar.get("contact_info")
            if not contact_info:
                continue
            for contact in contact_info:
                contact_type = contact.get("type")
                contact_values = contact.get("values")
                if not contact_values:
                    continue
                if contact_type == "email":
                    lst_other_email.extend(contact_values)
                else:
                    lst_account_id_send_email.extend(contact_values)
            if not lst_account_id_send_email and not lst_other_email:
                continue
            data_type = connector_detail.get("data_type")
            source_key = connector_detail.get("data_type")
            object_primary = connector_detail.get("object")
            source_key = connector_detail.get("source_key")
            connector_type = SourceModel().get_name_source_type(data_type, source_key, "vi")
            key_send_email = mapping_object_key_send_noti.get(object_primary)

            object_primary_name = SettingObjectHandleModel().get_object_handled_name(
                merchant_id, data_type, object_primary, "vi", source_key
            )
            if not key_send_email:
                continue
            report_detail = mapping_data_report_connector[connector_id]
            object_primary_data = mapping_object.get(object_primary, object_primary)
            report_detail.update(
                {
                    "total_records_add": report_detail.get("total_records_add_related_{}".format(object_primary_data)),
                    "total_records_update": report_detail.get(
                        "total_records_update_related_{}".format(object_primary_data)
                    ),
                    "connector_name": connector_name,
                    "connector_type": connector_type,
                    "module_name": object_primary_name,
                }
            )

            mobio_notify_sdk.send_message_notify_email(
                merchant_id=merchant_id,
                key_config=key_send_email,
                account_ids=lst_account_id_send_email,
                other_emails=lst_other_email,
                **report_detail
            )


if __name__ == "__main__":
    cron_job_send_noti_report_result_connector()
