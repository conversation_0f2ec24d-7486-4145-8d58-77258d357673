#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 12/06/2024
"""

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: VuNV
    Company: Mobio
    Date Created: 06/10/2021
"""
from datetime import UTC, datetime

from mobio.libs.logging import MobioLogging
from mobio.libs.schedule import BaseScheduler

import schedule
from configs import RedisConfig
from src.common.data_flow_constant import (
    ConstantDataType,
    ConstantStatusConfigConnector,
    ConstantStatusConnect,
    ConstantStatusSyncData,
    ConstantTimeRunSchedule,
)
from src.models.data_flow.config_connectors_model import (
    ConfigConnectorsModel,
    ConstantConfigConnectorsModel,
)
from src.models.reports.olap.dataflow.data_flow_model import DataFlowDialect

PER_PAGE = 100


class ReportStatusSyncDataSchedule(BaseScheduler):

    def get_schedule(self):
        return schedule.every(ConstantTimeRunSchedule.TIME_REPORT_STATUS_SYNC_MINUTE).minutes

    def owner_do(self):
        datetime_now = datetime.now(UTC)
        MobioLogging().info("ReportStatusSyncDataSchedule :: time run :: {}".format(datetime_now))
        after_token = None
        while True:

            lst_connector_active, after_token = ConfigConnectorsModel().find_paginate_load_more(
                {
                    "status_connect": ConstantStatusConnect.ON,
                    "status_sync": {"$ne": None},
                    ConstantConfigConnectorsModel.STATUS: ConstantStatusConfigConnector.ACTIVE,
                },
                per_page=100,
                after_token=after_token,
                sort_option=[("_id", -1)],
                projection={"_id": 1, "status_sync": 1, "data_type": 1},
            )

            mapping_connector_active_status_sync = {}

            lst_connector_data_in = []
            lst_connector_data_out = []

            for connector_active in lst_connector_active:
                connector_id = connector_active["_id"]
                mapping_connector_active_status_sync[connector_id] = connector_active.get("status_sync")
                data_type = connector_active.get("data_type")
                if data_type == ConstantDataType.DATA_IN:
                    lst_connector_data_in.append(connector_id)
                if data_type == ConstantDataType.DATA_OUT:
                    lst_connector_data_out.append(connector_id)
            if lst_connector_data_in:

                last_time_connector_in = DataFlowDialect().select_last_time_process_by_connector_ids(
                    lst_connector_data_in
                )
                self.update_last_time_connector(last_time_connector_in, mapping_connector_active_status_sync)

            if lst_connector_data_out:
                last_time_connector_out = DataFlowDialect().select_last_time_process_by_connector_ids_data_out(
                    lst_connector_data_out
                )
                self.update_last_time_connector(last_time_connector_out, mapping_connector_active_status_sync)

            if len(lst_connector_active) < 100:
                break

    def update_last_time_connector(self, last_time_connector, mapping_status_sync):
        if not last_time_connector:
            return
        datetime_now = datetime.now(UTC)
        for item in last_time_connector:
            connector_id = item.connector_id
            last_datetime_process = item.max_datetime

            status_sync_by_connector_id = mapping_status_sync.get(connector_id)

            time_delta = datetime_now.astimezone() - last_datetime_process.astimezone()
            hour_time_delta = time_delta.total_seconds() / 3600  # Tính ra số giờ đã không đồng bộ
            MobioLogging().info(
                "ReportStatusSyncDataSchedule :: processing connector_id: {}, hour_time_delta :: {}".format(
                    connector_id, hour_time_delta
                )
            )

            data_update = {"last_datetime_sync_data": last_datetime_process, "sort_time": last_datetime_process}
            # if (
            #     status_sync_by_connector_id
            #     in [ConstantStatusSyncData.RUNNING, ConstantStatusSyncData.DONE, ConstantStatusSyncData.FINISHED]
            #     and hour_time_delta > 12
            # ):
            #     data_update = {
            #         "status_sync": ConstantStatusSyncData.NOT_DATA_SYNC,
            #     }
            if (
                status_sync_by_connector_id in [ConstantStatusSyncData.NOT_DATA_SYNC, ConstantStatusSyncData.SYNC_ERROR]
                and hour_time_delta < 12
            ):
                data_update = {"status_sync": ConstantStatusSyncData.RUNNING}

            if data_update:

                status_update_status_sync = ConfigConnectorsModel().update_one_query({"_id": connector_id}, data_update)
                MobioLogging().info(
                    "ReportStatusSyncDataSchedule :: processing data_update: {}, status_update_status_sync :: {}".format(
                        data_update, status_update_status_sync
                    )
                )


if __name__ == "__main__":
    ReportStatusSyncDataSchedule(redis_uri=RedisConfig.REDIS_URI).owner_do()
