#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 08/05/2024
"""
from cryptography.fernet import Fernet

class EncryptionConfigConnectDataFlow(object):
    
    def generate_key(self):
        return Fernet.generate_key()

    def encrypt_message(self, message, key):
        cipher_suite = Fernet(key)
        encrypted_message = cipher_suite.encrypt(message.encode())
        return encrypted_message

    def decrypt_message(self, encrypted_message, key):
        cipher_suite = Fernet(key)
        decrypted_message = cipher_suite.decrypt(encrypted_message).decode()
        return decrypted_message