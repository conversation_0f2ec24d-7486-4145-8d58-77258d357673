#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 26/06/2024
"""

from abc import abstractmethod

from mobio.libs.validator import In, InstanceOf, Length, Required
from mobio.sdks.base.controllers import ParamInvalidError

from src.common.data_flow_constant import (
    ConstantObjectHandle,
    ConstantParamApi,
    ConstantUnificationMatchType,
    ConstantUnificationNormalizedType,
    ConstantUnificationStructure,
    ConstantUnificationSupportRule,
)
from src.helpers.validators import BaseValidator


class TemplateSettingValidator(BaseValidator):

    def _validate_rule_param_base_create_connector(self):
        return {
            ConstantParamApi.BodyCreateConnectors.NAME: [
                Required,
                InstanceOf(str),
                Length(1),
            ],
            ConstantParamApi.BodyCreateConnectors.DESCRIPTION: [InstanceOf(str)],
            ConstantParamApi.BodyCreateConnectors.SOURCE_KEY: [
                Required,
                InstanceOf(str),
                Length(1),
            ],
            ConstantParamApi.BodyCreateConnectors.SOURCE_TYPE: [
                Required,
                InstanceOf(str),
                Length(1),
            ],
            ConstantParamApi.BodyCreateConnectors.CONFIG_CONNECT: [
                Required,
                InstanceOf(dict),
                Length(1),
            ],
        }

    def _validate_operators_profiles_recording_rules(self, operators):
        for operator in operators:
            fields = operator.get(ConstantUnificationStructure.FIELDS)
            for field_name, rule in fields.items():
                if field_name not in ConstantUnificationSupportRule.get_all_attribute():
                    raise ParamInvalidError(
                        {
                            "code": 1010,
                            "detail": "field_name: {} is not support".format(
                                field_name
                            ),
                        }
                    )

                if field_name == ConstantUnificationSupportRule.NAME:
                    rule_validate = {
                        "match_type": [
                            Required,
                            InstanceOf(str),
                            Length(1),
                            In(
                                [
                                    ConstantUnificationMatchType.FUZZY,
                                    ConstantUnificationMatchType.EXACT,
                                ]
                            ),
                        ],
                        "normalized_type": [
                            Required,
                            InstanceOf(str),
                            Length(1),
                            In([ConstantUnificationNormalizedType.STRING]),
                        ],
                    }
                    self.abort_if_validate_error(rule_validate, rule)
                elif field_name in [
                    ConstantUnificationSupportRule.PRIMARY_EMAIL,
                    ConstantUnificationSupportRule.EMAIL,
                ]:
                    rule_validate = {
                        "match_type": [
                            Required,
                            InstanceOf(str),
                            Length(1),
                            In(
                                collection=[
                                    ConstantUnificationMatchType.EXACT_NORMALIZED
                                ]
                            ),
                        ],
                        "normalized_type": [
                            Required,
                            InstanceOf(str),
                            Length(1),
                            In([ConstantUnificationNormalizedType.EMAIL]),
                        ],
                    }
                    self.abort_if_validate_error(rule_validate, rule)
                elif field_name in [
                    ConstantUnificationSupportRule.PHONE_NUMBER,
                    ConstantUnificationSupportRule.PRIMARY_PHONE,
                ]:
                    rule_validate = {
                        "match_type": [
                            Required,
                            InstanceOf(str),
                            Length(1),
                            In([ConstantUnificationMatchType.EXACT_NORMALIZED]),
                        ],
                        "normalized_type": [
                            Required,
                            InstanceOf(str),
                            Length(1),
                            In([ConstantUnificationNormalizedType.PHONE_NUMBER]),
                        ],
                    }
                    self.abort_if_validate_error(rule_validate, rule)
                else:
                    rule_validate = {
                        "match_type": [
                            Required,
                            InstanceOf(str),
                            Length(1),
                            In([ConstantUnificationMatchType.EXACT]),
                        ],
                        "normalized_type": [
                            Required,
                            InstanceOf(str),
                            Length(1),
                            In([ConstantUnificationNormalizedType.STRING]),
                        ],
                    }
                    self.abort_if_validate_error(rule_validate, rule)

    def _validate_operators_profiles_profile_recording_rules(self, operators):
        self._validate_operators_profiles_recording_rules(operators)

    def _validate_operators_profiles_dynamic_event_recording_rules(self, operators):
        return

    def _validate_operators_profiles_product_holding_recording_rules(self, operators):
        return

    def _validate_operators_sale_recording_rules(self, operators):
        return
    def _validate_operators_sale_sale_recording_rules(self, operators):
        return
    def _validate_operators_sale_deal_recording_rules(self, operators):
        return
    def _validate_operators_ticket_recording_rules(self, operators):
        return
    def _validate_operators_ticket_ticket_recording_rules(self, operators):
        return

    def _base_validate_and_smooth_update_connectors_data_in_database(
        self, data_validate
    ):
        is_trust_source = data_validate.get(
            ConstantParamApi.BodyUpdateConnectors.IS_TRUST_SOURCE
        )
        rule_validate = {
            ConstantParamApi.BodyUpdateConnectors.OBJECT: [
                InstanceOf(str),
                # In(SettingObjectHandleModel().get_list_key_source_handled_active(ConstantDataType.DATA_IN)),
            ],
            ConstantParamApi.BodyUpdateConnectors.IS_TRUST_SOURCE: [
                InstanceOf(bool),
                In([True, False]),
            ],
            ConstantParamApi.BodyUpdateConnectors.CONFIG_MAPPING_DATA: [
                InstanceOf(dict)
            ],
            ConstantParamApi.BodyUpdateConnectors.CONFIG_RULE_UNIFICATION: [
                InstanceOf(dict)
            ],
            ConstantParamApi.BodyUpdateConnectors.CONFIG_SYNC_CALENDAR: [
                InstanceOf(dict)
            ],
            ConstantParamApi.BodyUpdateConnectors.OBJECT_ATTRIBUTE: [],
        }
        if is_trust_source:
            rule_validate.update(
                {
                    ConstantParamApi.BodyUpdateConnectors.LIST_FIELD_VERIFY: [
                        Required,
                        InstanceOf(list),
                        Length(1),
                    ]
                }
            )

        object_primary = data_validate.get(ConstantParamApi.BodyUpdateConnectors.OBJECT)
        object_attribute = data_validate.get(
            ConstantParamApi.BodyUpdateConnectors.OBJECT_ATTRIBUTE
        )
        if object_attribute:
            rule_validate.update(
                {
                    ConstantParamApi.BodyUpdateConnectors.OBJECT_ATTRIBUTE: [
                        InstanceOf(str),
                        Length(1),
                    ]
                }
            )

        if (
            object_primary == ConstantObjectHandle.Object.PROFILES
            and object_attribute
            in [
                ConstantObjectHandle.ObjectAttribute.PROFILE_DYNAMIC_EVENT,
                ConstantObjectHandle.ObjectAttribute.PROFILE_PRODUCT_HOLING,
            ]
        ):
            rule_validate.update(
                {
                    ConstantParamApi.BodyUpdateConnectors.CONFIG_DATA_DEFAULT_OF_PRIMARY_OBJECT: [
                        InstanceOf(list)
                    ]
                }
            )
        self.validate_optional_err(rule_validate, data_validate)

    @abstractmethod
    def validate_create_connectors(self, data_validate):
        pass

    @abstractmethod
    def validate_config_connect(self, data_validate):
        pass

    @abstractmethod
    def validate_update_connects(self, data_validate):
        pass

    @abstractmethod
    def validate_check_connection(self, data_validate):
        pass

    @abstractmethod
    def validate_modified_content_connectors(self, data_validate):
        pass
