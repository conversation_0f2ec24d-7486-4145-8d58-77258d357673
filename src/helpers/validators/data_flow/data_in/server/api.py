#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 06/07/2024
"""

import copy

from mobio.libs.logging import MobioLogging
from mobio.libs.validator import Each, Equals, In, InstanceOf, Length, Range, Required
from mobio.sdks.base.common.mobio_exception import CustomError

from src.common import LANG
from src.common.data_flow_constant import (
    ConstantContentType,
    ConstantDataType,
    ConstantMethod,
    ConstantModeConfigSyncCalendar,
    ConstantObjectHandle,
    ConstantParamApi,
    ConstantParamHeaderDefault,
    ConstantUnificationStructure,
    ConstantValueTypeMappingData,
)
from src.helpers.validators.data_flow.data_in.server import BaseServer
from src.models.data_flow.object_handle_model import SettingObjectHandleModel


class Validate(BaseServer):

    def validate_config_connect(self, data_validate):
        MobioLogging().info(f"BaseServer :: validate_config_connect :: data_validate :: {data_validate}")

        rule_validate_connect_config = {
            ConstantParamApi.BodyApiCheckConnection.CONFIG_APP_ID: [
                InstanceOf(str),
                Required,
                Length(1),
            ],
            ConstantParamApi.BodyApiCheckConnection.CONFIG_CONNECT_CONTENT_TYPE: [
                InstanceOf(str),
                Required,
                In(ConstantContentType.get_all_attribute()),
            ],
            ConstantParamApi.BodyApiCheckConnection.CONFIG_CONNECT_METHODS: [
                InstanceOf(list),
                Required,
                Length(1),
                Equals([ConstantMethod.POST]),
            ],
            ConstantParamApi.BodyApiCheckConnection.CONFIG_URL: [
                InstanceOf(str),
                Required,
                Length(1),
            ],
            ConstantParamApi.BodyApiCheckConnection.CONFIG_CONNECT_PARAM_HEADERS: [
                InstanceOf(list),
                Required,
                Length(1),
            ],
            ConstantParamApi.BodyApiCheckConnection.CONFIG_CONNECT_PARAM_QUERY: [
                InstanceOf(str),
                Required,
                Length(1),
            ],
        }

        config_connect = data_validate.get(ConstantParamApi.BodyApiCheckConnection.CONFIG_CONNECT)
        if config_connect:
            self.abort_if_validate_error(rule_validate_connect_config, config_connect)
            app_id = config_connect.get(ConstantParamApi.BodyApiCheckConnection.CONFIG_APP_ID)
            headers = {
                header["key"]: header["value"]
                for header in config_connect.get(ConstantParamApi.BodyApiCheckConnection.CONFIG_CONNECT_PARAM_HEADERS)
            }
            merchant_id = headers.get(ConstantParamHeaderDefault.X_MERCHANT_ID)
            connector_identifier = headers.get(ConstantParamHeaderDefault.MOBIO_CONNECTOR_IDENTIFIER)
            access_token = headers.get(ConstantParamHeaderDefault.MOBIO_ACCESS_TOKEN)
            app_secret_key = headers.get(ConstantParamHeaderDefault.MOBIO_CONNECTOR_APPKEY)

            if not all([merchant_id, connector_identifier, app_secret_key, access_token]):
                raise CustomError("Param header fail")
            access_token_new = self.gen_secret_key(merchant_id, app_secret_key, app_id, connector_identifier)
            if access_token_new != access_token:
                raise CustomError("Param header fail")

            param_query = config_connect.get(ConstantParamApi.BodyApiCheckConnection.CONFIG_CONNECT_PARAM_QUERY)
            required_params = [
                f"{ConstantParamHeaderDefault.X_MERCHANT_ID.lower()}={merchant_id}",
                f"{ConstantParamHeaderDefault.MOBIO_CONNECTOR_IDENTIFIER.lower()}={connector_identifier}",
                f"{ConstantParamHeaderDefault.MOBIO_ACCESS_TOKEN.lower()}={access_token}",
                f"{ConstantParamHeaderDefault.MOBIO_CONNECTOR_APPKEY.lower()}={app_secret_key}",
            ]

            if not all([param in param_query for param in required_params]):
                raise CustomError("Param query fail")

    def validate_create_connectors(self, data_validate):
        rule_base = self._validate_rule_param_base_create_connector()
        self.abort_if_validate_error(rule_base, data_validate)
        return self.validate_config_connect(data_validate)

    def handle_build_rule_update_connectors(self, smooth_data, detail_connector):
        merchant_id = detail_connector.get("merchant_id")
        is_trust_source = smooth_data.get(ConstantParamApi.BodyUpdateConnectors.IS_TRUST_SOURCE)
        rule_validate = {
            ConstantParamApi.BodyUpdateConnectors.OBJECT: [
                InstanceOf(str),
                In(
                    SettingObjectHandleModel().get_list_key_source_handled_active(
                        merchant_id, ConstantDataType.DATA_IN, "api"
                    )
                ),
            ],
            ConstantParamApi.BodyUpdateConnectors.IS_TRUST_SOURCE: [
                InstanceOf(bool),
                In([True, False]),
            ],
            ConstantParamApi.BodyUpdateConnectors.CONFIG_MAPPING_DATA: [InstanceOf(dict)],
            ConstantParamApi.BodyUpdateConnectors.CONFIG_RULE_UNIFICATION: [InstanceOf(dict)],
            ConstantParamApi.BodyUpdateConnectors.CONFIG_SYNC_CALENDAR: [InstanceOf(dict)],
            ConstantParamApi.BodyUpdateConnectors.SCHEMA_JSON_UPLOAD: [],
        }
        object_primary = smooth_data.get(ConstantParamApi.BodyUpdateConnectors.OBJECT)
        object_attribute = smooth_data.get(ConstantParamApi.BodyUpdateConnectors.OBJECT_ATTRIBUTE)
        if object_attribute:
            rule_validate.update(
                {
                    ConstantParamApi.BodyUpdateConnectors.OBJECT_ATTRIBUTE: [
                        InstanceOf(str),
                        Length(1),
                    ]
                }
            )
        if object_primary == ConstantObjectHandle.Object.PROFILES and object_attribute in [
            ConstantObjectHandle.ObjectAttribute.PROFILE_DYNAMIC_EVENT,
            ConstantObjectHandle.ObjectAttribute.PROFILE_PRODUCT_HOLING,
        ]:
            rule_validate.update(
                {
                    ConstantParamApi.BodyUpdateConnectors.CONFIG_DATA_DEFAULT_OF_PRIMARY_OBJECT: [
                        Required,
                        InstanceOf(list),
                        Length(1),
                    ]
                }
            )
        if is_trust_source:
            rule_validate.update(
                {
                    ConstantParamApi.BodyUpdateConnectors.LIST_FIELD_VERIFY: [
                        Required,
                        InstanceOf(list),
                        Length(1),
                    ]
                }
            )

        return rule_validate

    def validate_update_connectors(self, data_validate, detail_connector):
        smooth_data = copy.deepcopy(data_validate)
        object_handled = smooth_data.get(ConstantParamApi.BodyUpdateConnectors.OBJECT)
        object_attribute = smooth_data.get(ConstantParamApi.BodyUpdateConnectors.OBJECT_ATTRIBUTE)
        is_trust_source = smooth_data.get(ConstantParamApi.BodyUpdateConnectors.IS_TRUST_SOURCE)
        rule_validate = self.handle_build_rule_update_connectors(smooth_data, detail_connector)

        self.validate_optional_err(rule_validate, data_validate)

        # self._base_validate_and_smooth_update_connectors_data_in_database(smooth_data)
        config_rule_unification = smooth_data.get(ConstantParamApi.BodyUpdateConnectors.CONFIG_RULE_UNIFICATION)
        config_mapping_data = smooth_data.get(ConstantParamApi.BodyUpdateConnectors.CONFIG_MAPPING_DATA)
        config_sync_calendar = smooth_data.get(ConstantParamApi.BodyUpdateConnectors.CONFIG_SYNC_CALENDAR)

        if config_mapping_data:
            self.validate_and_smooth_update_connectors_config_rule_mapping_data(config_mapping_data)
        if config_sync_calendar:
            smooth_data[ConstantParamApi.BodyUpdateConnectors.CONFIG_SYNC_CALENDAR] = (
                self.validate_and_smooth_update_connectors_config_sync_calendar(config_sync_calendar)
            )
        if config_rule_unification:
            self.validate_and_smooth_update_connectors_config_rule_unification(
                config_rule_unification, object_handled, object_attribute
            )

        if is_trust_source:
            config_mapping_data = data_validate.get(ConstantParamApi.BodyUpdateConnectors.CONFIG_MAPPING_DATA)
            if is_trust_source:
                list_field_verify = data_validate.get(ConstantParamApi.BodyUpdateConnectors.LIST_FIELD_VERIFY)
                is_mapping_field_verify = False
                config_mapping_data_fields = config_mapping_data.get("fields")
                for field in config_mapping_data_fields:
                    field_target = field.get("field_target")
                    if field_target in list_field_verify:
                        is_mapping_field_verify = True
                if not is_mapping_field_verify:
                    raise CustomError(self.lang.get(LANG.FIELD_VERIFY_NOT_MAPPING_ONE).get("message"))
        return smooth_data

    def validate_modified_content_connectors(self, data_validate):
        smooth_data = copy.deepcopy(data_validate)
        is_trust_source = smooth_data.get(ConstantParamApi.BodyUpdateConnectors.IS_TRUST_SOURCE)
        object_handled = smooth_data.get(ConstantParamApi.BodyUpdateConnectors.OBJECT)
        object_attribute = smooth_data.get(ConstantParamApi.BodyUpdateConnectors.OBJECT_ATTRIBUTE)
        rule_validate = {
            ConstantParamApi.BodyCreateConnectors.NAME: [InstanceOf(str), Length(1)],
            ConstantParamApi.BodyCreateConnectors.DESCRIPTION: [InstanceOf(str)],
            ConstantParamApi.BodyCreateConnectors.SOURCE_KEY: [
                InstanceOf(str),
                Length(1),
            ],
            ConstantParamApi.BodyCreateConnectors.SOURCE_TYPE: [
                InstanceOf(str),
                Length(1),
            ],
            ConstantParamApi.BodyCreateConnectors.CONFIG_CONNECT: [
                InstanceOf(dict),
                Length(1),
            ],
        }
        self.validate_optional_err(rule_validate, data_validate)
        self.validate_config_connect(data_validate[ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT])

        self._base_validate_and_smooth_update_connectors_data_in_database(data_validate)
        config_rule_unification = smooth_data.get(ConstantParamApi.BodyUpdateConnectors.CONFIG_RULE_UNIFICATION)
        config_mapping_data = smooth_data.get(ConstantParamApi.BodyUpdateConnectors.CONFIG_MAPPING_DATA)
        config_sync_calendar = smooth_data.get(ConstantParamApi.BodyUpdateConnectors.CONFIG_SYNC_CALENDAR)

        if config_mapping_data:
            self.validate_and_smooth_update_connectors_config_rule_mapping_data(config_mapping_data)
        if config_sync_calendar:
            smooth_data[ConstantParamApi.BodyUpdateConnectors.CONFIG_SYNC_CALENDAR] = (
                self.validate_and_smooth_update_connectors_config_sync_calendar(config_sync_calendar)
            )
        if config_rule_unification:
            self.validate_and_smooth_update_connectors_config_rule_unification(
                config_rule_unification, object_handled, object_attribute
            )

        if is_trust_source:
            config_mapping_data = data_validate.get(ConstantParamApi.BodyUpdateConnectors.CONFIG_MAPPING_DATA)
            if is_trust_source:
                list_field_verify = data_validate.get(ConstantParamApi.BodyUpdateConnectors.LIST_FIELD_VERIFY)
                is_mapping_field_verify = False
                config_mapping_data_fields = config_mapping_data.get("fields")
                for field in config_mapping_data_fields:
                    field_target = field.get("field_target")
                    if field_target in list_field_verify:
                        is_mapping_field_verify = True
                if not is_mapping_field_verify:
                    raise CustomError(self.lang.get(LANG.FIELD_VERIFY_NOT_MAPPING_ONE).get("message"))
        return smooth_data

    def validate_and_smooth_update_connectors_config_rule_unification(
        self, data_validate, object_handled, object_attribute
    ):
        rule_validate = {
            "data_recording_rules": [
                Required,
                InstanceOf(dict),
                Length(1),
            ],
            "data_update_rules": [
                Required,
                InstanceOf(dict),
                Length(1),
            ],
        }

        if object_handled == ConstantObjectHandle.Object.PROFILES and (
            object_attribute == ConstantObjectHandle.ObjectAttribute.PROFILE_ATTRIBUTE or not object_attribute
        ):
            rule_validate.update(
                {
                    "consent": [
                        Required,
                        InstanceOf(dict),
                        Length(1),
                    ],
                }
            )
        if object_handled in [ConstantObjectHandle.Object.SALE, ConstantObjectHandle.Object.TICKET] or object_attribute in [
            ConstantObjectHandle.ObjectAttribute.PROFILE_DYNAMIC_EVENT,
            ConstantObjectHandle.ObjectAttribute.PROFILE_PRODUCT_HOLING,
        ]:
            rule_validate["data_update_rules"] = [
                InstanceOf(dict),
            ]
            rule_validate["data_recording_rules"] = [
                InstanceOf(dict),
            ]

        self.abort_if_validate_error(rule_validate, data_validate)

        data_recording_rules = data_validate.get("data_recording_rules")
        data_update_rules = data_validate.get("data_update_rules")
        if data_recording_rules and (
            object_handled == ConstantObjectHandle.Object.PROFILES
            and object_attribute == ConstantObjectHandle.ObjectAttribute.PROFILE_ATTRIBUTE
        ):
            rule_validate_data_recording = {
                "operators": [
                    Required,
                    InstanceOf(list),
                    Length(1),
                    Each(
                        {
                            ConstantUnificationStructure.PRIORITY: [
                                Required,
                                InstanceOf(int),
                            ],
                            ConstantUnificationStructure.FIELDS: [
                                Required,
                                InstanceOf(dict),
                                Length(1),
                            ],
                        }
                    ),
                ],
            }
            self.abort_if_validate_error(rule_validate_data_recording, data_recording_rules)

        if data_update_rules and (
            object_handled == ConstantObjectHandle.Object.PROFILES
            and object_attribute == ConstantObjectHandle.ObjectAttribute.PROFILE_ATTRIBUTE
        ):
            rule_validate_data_update = {
                "operators": [
                    Required,
                    InstanceOf(list),
                    Length(1),
                    Each(
                        {
                            ConstantUnificationStructure.PRIORITY: [
                                Required,
                                InstanceOf(int),
                            ],
                            ConstantUnificationStructure.FIELDS: [
                                Required,
                                InstanceOf(dict),
                                Length(1),
                            ],
                        }
                    ),
                ],
            }
            self.abort_if_validate_error(rule_validate_data_update, data_update_rules)

        rule_validate_consent = {
            "analytics_consent": [
                InstanceOf(str),
                Length(1),
                In(["Có", "có", "Không", "không"]),
            ],
            "tracking_consent": [
                InstanceOf(str),
                Length(1),
                In(["Có", "có", "Không", "không"]),
            ],
            "mkt_consent": [
                InstanceOf(str),
                Length(1),
                In(["Có", "có", "Không", "không"]),
            ],
        }
        if (
            object_handled == ConstantObjectHandle.Object.PROFILES
            and object_attribute == ConstantObjectHandle.ObjectAttribute.PROFILE_ATTRIBUTE
        ):
            self.abort_if_validate_error(rule_validate_consent, data_validate["consent"])
        if data_recording_rules:

            operators_data_recording_rules = data_recording_rules["operators"]
            func_name = (
                "_validate_operators_{}_recording_rules".format(object_handled)
                if not object_attribute
                else "_validate_operators_{}_{}_recording_rules".format(object_handled, object_attribute)
            )
            try:
                getattr(
                    self,
                    func_name,
                )(operators_data_recording_rules)
            except Exception as ex:
                raise CustomError(ex)
        if data_update_rules:

            operators_data_update_rules = data_update_rules["operators"]
            func_name = (
                "_validate_operators_{}_recording_rules".format(object_handled)
                if not object_attribute
                else "_validate_operators_{}_{}_recording_rules".format(object_handled, object_attribute)
            )
            try:
                getattr(self, func_name)(operators_data_update_rules)
            except Exception:
                raise CustomError("Not exist validate operators, object {}".format(object_handled))

    def validate_and_smooth_update_connectors_config_sync_calendar(self, data_validate):
        rule_validate_config_sync_calendar = {
            "mode": [
                Required,
                InstanceOf(str),
                Length(1),
                In([ConstantModeConfigSyncCalendar.STREAMING]),
            ],
            "auto_retry_w_error": [Required, InstanceOf(dict), Length(1)],
        }
        self.validate_optional_err(rule_validate_config_sync_calendar, data_validate)

        auto_retry_w_error = data_validate.get("auto_retry_w_error")
        mode_sync = data_validate.get("mode")
        rule_validate_auto_sync_w_error = {"status": [Required, InstanceOf(int), In([0, 1])]}
        status_auto_retry_w_error = auto_retry_w_error.get("status")
        if status_auto_retry_w_error == 1:
            rule_validate_auto_sync_w_error.update(
                {
                    "number_max_retry": [Required, InstanceOf(int), Range(1, 10)],
                    "retry_interval_sec": [
                        Required,
                        InstanceOf(int),
                        Range(start=900, end=86400),
                    ],
                }
            )

        self.validate_optional_err(rule_validate_auto_sync_w_error, auto_retry_w_error)
        if status_auto_retry_w_error == 0:
            if "number_max_retry" in auto_retry_w_error:
                auto_retry_w_error.pop("number_max_retry")
            if "retry_interval_sec" in auto_retry_w_error:
                auto_retry_w_error.pop("retry_interval_sec")
        data_validate["auto_retry_w_error"] = auto_retry_w_error
        schedule = data_validate.get("schedule", {})
        if mode_sync == ConstantModeConfigSyncCalendar.STREAMING:
            schedule = {}
        data_validate["schedule"] = schedule
        return data_validate

    def validate_and_smooth_update_connectors_config_rule_mapping_data(self, data_validate):
        rule_validate_config_mapping_data = {
            "config_mapping_field_with_fe": [],
            "fields": [
                Required,
                InstanceOf(list),
                Length(1),
                Each(
                    {
                        "field_source": [Required, InstanceOf(str), Length(1)],
                        "field_source_type": [InstanceOf(str)],
                        "field_source_schema_type": [InstanceOf(str)],
                        "field_target": [],
                        "field_target_type": [InstanceOf(str)],
                        "object": [],
                        "value_type": [
                            InstanceOf(str),
                            In(ConstantValueTypeMappingData.get_all_attribute()),
                        ],
                        "value_by_type_fixed": [
                            InstanceOf(str),
                        ],
                        "required": [InstanceOf(bool)],
                        "format_value": [],
                        "display_type": [],
                        "field_property": [],
                        "format": [],
                        "action": [],
                        "group": [],
                    }
                ),
            ],
        }
        self.validate_optional_err(rule_validate_config_mapping_data, data_validate)

        field_targets = []
        group_index = 0
        for field in data_validate["fields"]:
            field_target = field.get("field_target")
            field_object = field.get("object")
            if not field_target or not field_object:
                continue
            item = field_target + field_object
            if item in field_targets:
                raise CustomError("Field {} not duplicate mapping".format(field_target))
            group = field.get("group")
            if not group:
                group_index += 1
                field["group"] = group_index
            field_targets.append(item)
