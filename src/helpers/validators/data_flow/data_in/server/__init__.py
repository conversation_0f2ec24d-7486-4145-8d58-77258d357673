#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 06/07/2024
"""

from abc import abstractmethod

from mobio.libs.validator import Each, In, InstanceOf, Length, Range, Required
from mobio.sdks.base.common.mobio_exception import CustomError

from src.common.data_flow_constant import (
    ConstantModeConfigSyncCalendar,
    ConstantParamApi,
    ConstantScheduleConfigType,
    ConstantScheduleType,
    ConstantUnificationStructure,
)
from src.helpers.validators.data_flow.base import TemplateSettingValidator


class BaseServer(TemplateSettingValidator):

    def __init__(self) -> None:
        super().__init__()

    @abstractmethod
    def validate_config_connect(self, data_validate):
        pass

    @abstractmethod
    def validate_create_connectors(self, data_validate):
        pass

    @abstractmethod
    def validate_check_connection(self, data_validate):
        pass

    @abstractmethod
    def validate_update_connectors(self, data_validate, detail_connector):
        pass

    def validate_modified_content_connectors(self, data_validate):
        rule_validate = {
            ConstantParamApi.BodyCreateConnectors.NAME: [InstanceOf(str), Length(1)],
            ConstantParamApi.BodyCreateConnectors.DESCRIPTION: [InstanceOf(str)],
            ConstantParamApi.BodyCreateConnectors.SOURCE_KEY: [InstanceOf(str), Length(1)],
            ConstantParamApi.BodyCreateConnectors.SOURCE_TYPE: [InstanceOf(str), Length(1)],
            ConstantParamApi.BodyCreateConnectors.CONFIG_CONNECT: [
                InstanceOf(dict),
                Length(1),
            ],
        }
        self.validate_optional_err(rule_validate, data_validate)
        self.validate_config_connect(data_validate[ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT])

        return self._base_validate_and_smooth_update_connectors_data_in_database(data_validate)

    def validate_and_smooth_update_connectors_config_rule_unification(self, data_validate):
        object_handle = data_validate.get(ConstantParamApi.BodyUpdateConnectors.OBJECT)
        rule_validate = {
            "data_recording_rules": [
                Required,
                InstanceOf(dict),
                Length(1),
            ],
            "data_update_rules": [
                Required,
                InstanceOf(dict),
                Length(1),
            ],
        }
        if object_handle == "profiles":
            rule_validate.update(
                {
                    "consent": [
                        Required,
                        InstanceOf(dict),
                        Length(1),
                    ],
                }
            )

        self.abort_if_validate_error(rule_validate, data_validate)

        data_recording_rules = data_validate.get("data_recording_rules")
        data_update_rules = data_validate.get("data_recording_rules")

        rule_validate_data_recording = {
            "operators": [
                Required,
                InstanceOf(list),
                Length(1),
                Each(
                    {
                        ConstantUnificationStructure.PRIORITY: [Required, InstanceOf(int)],
                        ConstantUnificationStructure.FIELDS: [Required, InstanceOf(dict), Length(1)],
                    }
                ),
            ],
        }
        self.abort_if_validate_error(rule_validate_data_recording, data_recording_rules)

        rule_validate_data_update = {
            "operators": [
                Required,
                InstanceOf(list),
                Length(1),
                Each(
                    {
                        ConstantUnificationStructure.PRIORITY: [Required, InstanceOf(int)],
                        ConstantUnificationStructure.FIELDS: [Required, InstanceOf(dict), Length(1)],
                    }
                ),
            ],
        }
        self.abort_if_validate_error(rule_validate_data_update, data_update_rules)
        if object_handle == "profiles":
            rule_validate_consent = {
                "analytics_consent": [InstanceOf(str), Length(1), In(["Có", "có", "Không", "không"])],
                "tracking_consent": [InstanceOf(str), Length(1), In(["Có", "có", "Không", "không"])],
                "mkt_consent": [InstanceOf(str), Length(1), In(["Có", "có", "Không", "không"])],
            }
            self.abort_if_validate_error(rule_validate_consent, data_validate["consent"])

        operators_data_recording_rules = data_recording_rules["operators"]
        try:
            getattr(self, "_validate_operators_{}_recording_rules".format(object_handle))(
                operators_data_recording_rules
            )
        except Exception:
            raise CustomError("Not exist validate operators, object {}".format(object_handle))

        operators_data_update_rules = data_update_rules["operators"]
        try:
            getattr(self, "_validate_operators_{}_recording_rules".format(object_handle))(operators_data_update_rules)
        except Exception:
            raise CustomError("Not exist validate operators, object {}".format(object_handle))

    def validate_and_smooth_update_connectors_config_sync_calendar(self, data_validate):
        rule_validate_config_sync_calendar = {
            "mode": [Required, InstanceOf(str), Length(1), In(ConstantModeConfigSyncCalendar.get_all_attribute())],
            "auto_retry_w_error": [Required, InstanceOf(dict), Length(1)],
            "schedule": [InstanceOf(dict)],
        }

        mode_sync = data_validate.get("mode")
        if mode_sync == ConstantModeConfigSyncCalendar.SNAPSHOT:
            rule_validate_config_sync_calendar["schedule"] = [Required, InstanceOf(dict), Length(1)]

        self.validate_optional_err(rule_validate_config_sync_calendar, data_validate)

        auto_retry_w_error = data_validate.get("auto_retry_w_error")
        rule_validate_auto_sync_w_error = {"status": [Required, InstanceOf(int), In([0, 1])]}
        status_auto_retry_w_error = auto_retry_w_error.get("status")
        if status_auto_retry_w_error == 1:
            rule_validate_auto_sync_w_error.update(
                {
                    "number_max_retry": [Required, InstanceOf(int), Range(1, 10)],
                    "retry_interval_sec": [
                        Required,
                        InstanceOf(int),
                        Range(start=900, end=86400),
                    ],
                }
            )

        self.validate_optional_err(rule_validate_auto_sync_w_error, auto_retry_w_error)
        if status_auto_retry_w_error == 0:
            if "number_max_retry" in auto_retry_w_error:
                auto_retry_w_error.pop("number_max_retry")
            if "retry_interval_sec" in auto_retry_w_error:
                auto_retry_w_error.pop("retry_interval_sec")
        data_validate["auto_retry_w_error"] = auto_retry_w_error
        schedule = data_validate.get("schedule", {})
        if mode_sync == ConstantModeConfigSyncCalendar.STREAMING:
            schedule = {}
        if schedule and mode_sync == ConstantModeConfigSyncCalendar.SNAPSHOT:
            rule_validate_schedule = {
                "type": [Required, InstanceOf(str), Length(1), In(ConstantScheduleType.get_all_attribute())]
            }
            schedule_type = schedule.get("type")
            if schedule_type and schedule_type == ConstantScheduleType.INTERVAL:
                rule_validate_schedule["config"] = [Required, InstanceOf(dict), Length(1)]
            self.abort_if_validate_error(rule_validate_schedule, schedule)

            if schedule_type == ConstantScheduleType.INTERVAL:
                schedule_config = schedule.get("config")
                rule_validate_schedule_config = {
                    "hour": [Required, InstanceOf(str), Length(1)],
                    "type": [Required, InstanceOf(str), Length(1), In(ConstantScheduleConfigType.get_all_attribute())],
                }
                schedule_config_type = schedule_config.get("type")
                if schedule_config_type:
                    if schedule_config_type == ConstantScheduleConfigType.DAY:
                        schedule_config["values"] = []
                        if "type_select_day_in_month" in schedule_config:
                            schedule_config.pop("type_select_day_in_month")
                    if schedule_config_type == ConstantScheduleConfigType.WEEK:
                        if "type_select_day_in_month" in schedule_config:
                            schedule_config.pop("type_select_day_in_month")
                        rule_validate_schedule_config["values"] = [
                            Required,
                            InstanceOf(list),
                            Length(1),
                            Each(
                                [
                                    InstanceOf(str),
                                    Length(1),
                                    In(ConstantScheduleConfigType.DayInWeek.get_all_attribute()),
                                ]
                            ),
                        ]
                    if schedule_config_type == ConstantScheduleConfigType.MONTH:
                        rule_validate_schedule_config["type_select_day_in_month"] = (
                            Required,
                            InstanceOf(str),
                            Length(1),
                            In(ConstantScheduleConfigType.TypeSelectDayInMonth.get_all_attribute()),
                        )
                        type_select_day_in_month = schedule_config.get("type_select_day_in_month")
                        if type_select_day_in_month:
                            if type_select_day_in_month == ConstantScheduleConfigType.TypeSelectDayInMonth.EXACT_DAY:
                                rule_validate_schedule_config["values"] = [
                                    InstanceOf(list),
                                    Length(1, 31),
                                    Each([InstanceOf(int), In([i for i in range(1, 32)])]),
                                ]
                            if type_select_day_in_month == ConstantScheduleConfigType.TypeSelectDayInMonth.FLEX_DAY:
                                rule_validate_schedule_config["values"] = [
                                    InstanceOf(list),
                                    Length(1),
                                    Each(
                                        [
                                            InstanceOf(str),
                                            Length(1),
                                            In(
                                                ConstantScheduleConfigType.TypeSelectDayInMonth.ConstantValueDaySelectInMonth.get_all_attribute()
                                            ),
                                        ]
                                    ),
                                ]
                self.validate_optional_err(rule_validate_schedule_config, schedule_config)
                schedule["config"] = schedule_config
        data_validate["schedule"] = schedule
        return data_validate

    def gen_secret_key(self, merchant_id, app_secret_key, app_id, connector_identifier):
        from src.helpers.general_param_default.data_flow.base import (
            TemplateSettingGeneral,
        )

        access_token = TemplateSettingGeneral().gen_secret_key_by_type_api(
            merchant_id, app_secret_key, connector_identifier
        )
        return access_token
