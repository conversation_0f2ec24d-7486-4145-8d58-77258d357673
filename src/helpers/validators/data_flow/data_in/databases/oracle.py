#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 29/11/2024
"""
from src.helpers.validators.data_flow.data_in.databases import BaseDatabases


class Validate(BaseDatabases):

    def validate_check_connection(self, data_validate):
        rule_base = self._validate_rule_param_base_create_connector()
        self.abort_if_validate_error(rule_base, data_validate)

    def validate_update_connectors(self, data_validate, detail_connector):
        return super().validate_update_connectors(data_validate, detail_connector)

    def validate_modified_content_connectors(self, data_validate, detail_connector):
        return super().validate_modified_content_connectors(data_validate, detail_connector)

    def validate_create_connectors(self, data_validate):
        return super().validate_create_connectors(data_validate)
