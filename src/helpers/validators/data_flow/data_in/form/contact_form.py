#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 27/08/2024
"""


import copy

from mobio.libs.logging import MobioLogging
from mobio.libs.validator import Each, In, InstanceOf, Length, Range, Required
from mobio.sdks.base.common.mobio_exception import CustomError

from src.common import LANG
from src.common.data_flow_constant import (
    ConstantDataType,
    ConstantModeConfigSyncCalendar,
    ConstantObjectHandle,
    ConstantParamApi,
    ConstantUnificationStructure,
    ConstantValueTypeMappingData,
)
from src.helpers.validators.data_flow.data_in.form import BaseForm
from src.models.data_flow.object_handle_model import SettingObjectHandleModel


class Validate(BaseForm):

    def validate_config_connect(self, data_validate):
        MobioLogging().info(f"BaseForm :: validate_config_connect :: data_validate :: {data_validate}")

        rule_validate_connect_config = {
            "form_id": [InstanceOf(str), Required, Length(1)],
        }

        config_connect = data_validate.get(ConstantParamApi.BodyApiCheckConnection.CONFIG_CONNECT)
        if config_connect:
            self.abort_if_validate_error(rule_validate_connect_config, config_connect)

    def validate_create_connectors(self, data_validate):
        rule_base = self._validate_rule_param_base_create_connector()
        self.abort_if_validate_error(rule_base, data_validate)
        return self.validate_config_connect(data_validate)

    def handle_build_rule_update_connectors(self, smooth_data, detail_connector):
        merchant_id = detail_connector.get("merchant_id")
        is_trust_source = smooth_data.get(ConstantParamApi.BodyUpdateConnectors.IS_TRUST_SOURCE)
        rule_validate = {
            ConstantParamApi.BodyUpdateConnectors.OBJECT: [
                InstanceOf(str),
                In(
                    SettingObjectHandleModel().get_list_key_source_handled_active(
                        merchant_id, ConstantDataType.DATA_IN, "contact_form"
                    )
                ),
            ],
            ConstantParamApi.BodyUpdateConnectors.IS_TRUST_SOURCE: [InstanceOf(bool), In([True, False])],
            ConstantParamApi.BodyUpdateConnectors.CONFIG_MAPPING_DATA: [InstanceOf(dict)],
            ConstantParamApi.BodyUpdateConnectors.CONFIG_RULE_UNIFICATION: [InstanceOf(dict)],
            ConstantParamApi.BodyUpdateConnectors.CONFIG_SYNC_CALENDAR: [InstanceOf(dict)],
            ConstantParamApi.BodyUpdateConnectors.SCHEMA_JSON_UPLOAD: [],
        }
        if is_trust_source:
            rule_validate.update(
                {ConstantParamApi.BodyUpdateConnectors.LIST_FIELD_VERIFY: [Required, InstanceOf(list), Length(1)]}
            )

        return rule_validate

    def validate_update_connectors(self, data_validate, detail_connector):
        smooth_data = copy.deepcopy(data_validate)
        object_handled = smooth_data.get(ConstantParamApi.BodyUpdateConnectors.OBJECT)
        is_trust_source = smooth_data.get(ConstantParamApi.BodyUpdateConnectors.IS_TRUST_SOURCE)
        rule_validate = self.handle_build_rule_update_connectors(smooth_data, detail_connector)

        self.validate_optional_err(rule_validate, data_validate)

        self._base_validate_and_smooth_update_connectors_data_in_database(smooth_data)
        config_rule_unification = smooth_data.get(ConstantParamApi.BodyUpdateConnectors.CONFIG_RULE_UNIFICATION)
        config_mapping_data = smooth_data.get(ConstantParamApi.BodyUpdateConnectors.CONFIG_MAPPING_DATA)
        config_sync_calendar = smooth_data.get(ConstantParamApi.BodyUpdateConnectors.CONFIG_SYNC_CALENDAR)

        config_data_default_of_primary_object = smooth_data.get(
            ConstantParamApi.BodyUpdateConnectors.CONFIG_DATA_DEFAULT_OF_PRIMARY_OBJECT
        )

        if config_data_default_of_primary_object:
            self.validate_and_smooth_update_config_data_default_of_primary_object(config_data_default_of_primary_object)

        if config_mapping_data:
            self.validate_and_smooth_update_connectors_config_rule_mapping_data(config_mapping_data)
        if config_sync_calendar:
            smooth_data[ConstantParamApi.BodyUpdateConnectors.CONFIG_SYNC_CALENDAR] = (
                self.validate_and_smooth_update_connectors_config_sync_calendar(config_sync_calendar)
            )
        if config_rule_unification:
            self.validate_and_smooth_update_connectors_config_rule_unification(config_rule_unification, object_handled)

        if is_trust_source:
            config_mapping_data = data_validate.get(ConstantParamApi.BodyUpdateConnectors.CONFIG_MAPPING_DATA)
            if is_trust_source:
                list_field_verify = data_validate.get(ConstantParamApi.BodyUpdateConnectors.LIST_FIELD_VERIFY)
                is_mapping_field_verify = False
                config_mapping_data_fields = config_mapping_data.get("fields")
                for field in config_mapping_data_fields:
                    field_target = field.get("field_target")
                    if field_target in list_field_verify:
                        is_mapping_field_verify = True
                if not is_mapping_field_verify:
                    raise CustomError(self.lang.get(LANG.FIELD_VERIFY_NOT_MAPPING_ONE).get("message"))
        return smooth_data

    def validate_and_smooth_update_config_data_default_of_primary_object(self, data_validate):

        for data in data_validate:
            rule_validate = {
                "field_key": [
                    Required,
                    InstanceOf(str),
                    Length(1),
                ],
                "value": [
                    Required,
                    InstanceOf(str),
                    Length(1),
                ],
                "is_personalization": [
                    Required,
                    InstanceOf(bool),
                    In([True, False]),
                ],
            }

            is_personalization = data.get("is_personalization")
            if is_personalization:
                rule_validate.update({"config_field_personalization": [Required, InstanceOf(list), Length(1)]})

            self.abort_if_validate_error(rule_validate, data)

    def validate_modified_content_connectors(self, data_validate):
        smooth_data = copy.deepcopy(data_validate)
        is_trust_source = smooth_data.get(ConstantParamApi.BodyUpdateConnectors.IS_TRUST_SOURCE)
        object_handled = smooth_data.get(ConstantParamApi.BodyUpdateConnectors.OBJECT)
        rule_validate = {
            ConstantParamApi.BodyCreateConnectors.NAME: [InstanceOf(str), Length(1)],
            ConstantParamApi.BodyCreateConnectors.DESCRIPTION: [InstanceOf(str)],
            ConstantParamApi.BodyCreateConnectors.SOURCE_KEY: [InstanceOf(str), Length(1)],
            ConstantParamApi.BodyCreateConnectors.SOURCE_TYPE: [InstanceOf(str), Length(1)],
            ConstantParamApi.BodyCreateConnectors.CONFIG_CONNECT: [
                InstanceOf(dict),
                Length(1),
            ],
        }
        self.validate_optional_err(rule_validate, data_validate)
        self.validate_config_connect(data_validate[ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT])

        self._base_validate_and_smooth_update_connectors_data_in_database(data_validate)
        config_rule_unification = smooth_data.get(ConstantParamApi.BodyUpdateConnectors.CONFIG_RULE_UNIFICATION)
        config_mapping_data = smooth_data.get(ConstantParamApi.BodyUpdateConnectors.CONFIG_MAPPING_DATA)
        config_sync_calendar = smooth_data.get(ConstantParamApi.BodyUpdateConnectors.CONFIG_SYNC_CALENDAR)

        if config_mapping_data:
            self.validate_and_smooth_update_connectors_config_rule_mapping_data(config_mapping_data)
        if config_sync_calendar:
            smooth_data[ConstantParamApi.BodyUpdateConnectors.CONFIG_SYNC_CALENDAR] = (
                self.validate_and_smooth_update_connectors_config_sync_calendar(config_sync_calendar)
            )
        if config_rule_unification:
            self.validate_and_smooth_update_connectors_config_rule_unification(config_rule_unification, object_handled)

        if is_trust_source:
            config_mapping_data = data_validate.get(ConstantParamApi.BodyUpdateConnectors.CONFIG_MAPPING_DATA)
            if is_trust_source:
                list_field_verify = data_validate.get(ConstantParamApi.BodyUpdateConnectors.LIST_FIELD_VERIFY)
                is_mapping_field_verify = False
                config_mapping_data_fields = config_mapping_data.get("fields")
                for field in config_mapping_data_fields:
                    field_target = field.get("field_target")
                    if field_target in list_field_verify:
                        is_mapping_field_verify = True
                if not is_mapping_field_verify:
                    raise CustomError(self.lang.get(LANG.FIELD_VERIFY_NOT_MAPPING_ONE).get("message"))
        return smooth_data

    def validate_and_smooth_update_connectors_config_rule_unification(self, data_validate, object_handled):
        rule_validate = {
            "data_recording_rules": [
                Required,
                InstanceOf(dict),
                Length(1),
            ],
            "data_update_rules": [
                Required,
                InstanceOf(dict),
                Length(1),
            ],
        }

        if object_handled == ConstantObjectHandle.Object.PROFILES:
            rule_validate.update(
                {
                    "consent": [
                        Required,
                        InstanceOf(dict),
                        Length(1),
                    ],
                }
            )
        if object_handled in [ConstantObjectHandle.Object.SALE, ConstantObjectHandle.Object.TICKET]:
            rule_validate["data_update_rules"] = [
                InstanceOf(dict),
                Length(1),
            ]

        self.abort_if_validate_error(rule_validate, data_validate)

        data_recording_rules = data_validate.get("data_recording_rules")
        data_update_rules = data_validate.get("data_recording_rules")

        rule_validate_data_recording = {
            "operators": [
                Required,
                InstanceOf(list),
                Length(1),
                Each(
                    {
                        ConstantUnificationStructure.PRIORITY: [Required, InstanceOf(int)],
                        ConstantUnificationStructure.FIELDS: [Required, InstanceOf(dict), Length(1)],
                    }
                ),
            ],
        }
        self.abort_if_validate_error(rule_validate_data_recording, data_recording_rules)

        rule_validate_data_update = {
            "operators": [
                Required,
                InstanceOf(list),
                Length(1),
                Each(
                    {
                        ConstantUnificationStructure.PRIORITY: [Required, InstanceOf(int)],
                        ConstantUnificationStructure.FIELDS: [Required, InstanceOf(dict), Length(1)],
                    }
                ),
            ],
        }
        self.abort_if_validate_error(rule_validate_data_update, data_update_rules)

        rule_validate_consent = {
            "analytics_consent": [InstanceOf(str), Length(1), In(["Có", "có", "Không", "không"])],
            "tracking_consent": [InstanceOf(str), Length(1), In(["Có", "có", "Không", "không"])],
            "mkt_consent": [InstanceOf(str), Length(1), In(["Có", "có", "Không", "không"])],
        }
        if object_handled == "profiles":
            self.abort_if_validate_error(rule_validate_consent, data_validate["consent"])

        operators_data_recording_rules = data_recording_rules["operators"]
        try:
            getattr(self, "_validate_operators_{}_recording_rules".format(object_handled))(
                operators_data_recording_rules
            )
        except Exception:
            raise CustomError("Not exist validate operators, object {}".format(object_handled))

        operators_data_update_rules = data_update_rules["operators"]
        try:
            getattr(self, "_validate_operators_{}_recording_rules".format(object_handled))(operators_data_update_rules)
        except Exception:
            raise CustomError("Not exist validate operators, object {}".format(object_handled))

    def validate_and_smooth_update_connectors_config_sync_calendar(self, data_validate):
        rule_validate_config_sync_calendar = {
            "mode": [Required, InstanceOf(str), Length(1), In([ConstantModeConfigSyncCalendar.STREAMING])],
            "auto_retry_w_error": [Required, InstanceOf(dict), Length(1)],
        }
        self.validate_optional_err(rule_validate_config_sync_calendar, data_validate)

        auto_retry_w_error = data_validate.get("auto_retry_w_error")
        mode_sync = data_validate.get("mode")
        rule_validate_auto_sync_w_error = {"status": [Required, InstanceOf(int), In([0, 1])]}
        status_auto_retry_w_error = auto_retry_w_error.get("status")
        if status_auto_retry_w_error == 1:
            rule_validate_auto_sync_w_error.update(
                {
                    "number_max_retry": [Required, InstanceOf(int), Range(1, 10)],
                    "retry_interval_sec": [
                        Required,
                        InstanceOf(int),
                        Range(start=900, end=86400),
                    ],
                }
            )

        self.validate_optional_err(rule_validate_auto_sync_w_error, auto_retry_w_error)
        if status_auto_retry_w_error == 0:
            if "number_max_retry" in auto_retry_w_error:
                auto_retry_w_error.pop("number_max_retry")
            if "retry_interval_sec" in auto_retry_w_error:
                auto_retry_w_error.pop("retry_interval_sec")
        data_validate["auto_retry_w_error"] = auto_retry_w_error
        schedule = data_validate.get("schedule", {})
        if mode_sync == ConstantModeConfigSyncCalendar.STREAMING:
            schedule = {}
        data_validate["schedule"] = schedule
        return data_validate

    def validate_and_smooth_update_connectors_config_rule_mapping_data(self, data_validate):
        rule_validate_config_mapping_data = {
            "config_mapping_field_with_fe": [],
            "fields": [
                Required,
                InstanceOf(list),
                Length(1),
                Each(
                    {
                        "field_source": [Required, InstanceOf(str), Length(1)],
                        "field_source_type": [InstanceOf(str)],
                        "field_source_schema_type": [InstanceOf(str)],
                        "field_target": [Required, InstanceOf(str), Length(1)],
                        "field_target_type": [InstanceOf(str)],
                        "object": [Required, InstanceOf(str), Length(1)],
                        "value_type": [
                            Required,
                            InstanceOf(str),
                            Length(1),
                            In(ConstantValueTypeMappingData.get_all_attribute()),
                        ],
                        "value_by_type_fixed": [
                            InstanceOf(str),
                        ],
                        "required": [InstanceOf(bool)],
                        "format_value": [],
                        "display_type": [],
                        "field_property": [],
                        "format": [],
                        "action": [],
                    }
                ),
            ],
        }
        self.abort_if_validate_error(rule_validate_config_mapping_data, data_validate)

        field_targets = []
        for field in data_validate["fields"]:
            field_target = field.get("field_target")
            field_object = field.get("object")
            item = field_target + field_object
            if item in field_targets:
                raise CustomError("Field {} not duplicate mapping".format(field_target))
            field_targets.append(item)
