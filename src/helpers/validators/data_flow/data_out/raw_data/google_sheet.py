#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 17/09/2024
"""

import string

from bson import ObjectId
from mobio.libs.logging import MobioLogging
from mobio.libs.validator import Each, Equals, In, InstanceOf, Length, Required
from mobio.sdks.base.common.mobio_exception import CustomError

from src.common.data_flow_constant import (
    ConstantDataType,
    ConstantModeConfigSyncCalendar,
    ConstantParamApi,
    ConstantTypeContactInfo,
    ConstantTypeUseSpreadsheet,
)
from src.common.utils import convert_isoformat_to_date
from src.helpers.integration_account.google_hepler import IntegrationAccountGoogleHelper
from src.helpers.validators.data_flow.data_out.raw_data import BaseRawData
from src.models.data_flow.object_handle_model import SettingObjectHandleModel
from src.models.integration_account.integration_account_model import (
    IntegrationAccountModel,
)


class Validate(BaseRawData):

    def validate_config_connect(self, data_validate):
        config_connect = data_validate.get(ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT)
        MobioLogging().info("ValidateGoogle :: validate_config_connect :: data_validate :: {}".format(data_validate))
        rule_validate_connect_config = {
            ConstantParamApi.BodyGoogleSheetConfigConnector.INTEGRATION_ACCOUNT_ID: [
                InstanceOf(str),
                Required,
                Length(1),
            ],
            ConstantParamApi.BodyGoogleSheetConfigConnector.TYPE_USE_SPREADSHEET: [
                InstanceOf(base_class=str),
                Required,
                In(ConstantTypeUseSpreadsheet().get_all_attribute()),
            ],
            ConstantParamApi.BodyGoogleSheetConfigConnector.SPREADSHEET_ID: [
                InstanceOf(str),
                Required,
                Length(1),
            ],
            ConstantParamApi.BodyGoogleSheetConfigConnector.SPREADSHEET_NAME: [],
            ConstantParamApi.BodyGoogleSheetConfigConnector.SHEET_ID: [InstanceOf(int), Required],
            ConstantParamApi.BodyGoogleSheetConfigConnector.SHEET_NAME: [InstanceOf(str), Required],
        }
        if config_connect:
            type_use_spreadsheet = config_connect.get(
                ConstantParamApi.BodyGoogleSheetConfigConnector.TYPE_USE_SPREADSHEET
            )
            if type_use_spreadsheet == ConstantTypeUseSpreadsheet.NEW_SPREADSHEET:
                rule_validate_connect_config.update(
                    {
                        ConstantParamApi.BodyGoogleSheetConfigConnector.FOLDER_ID: [
                            InstanceOf(str),
                            Required,
                            Length(1),
                        ],
                        ConstantParamApi.BodyGoogleSheetConfigConnector.SPREADSHEET_NAME: [
                            InstanceOf(str),
                            Required,
                            Length(1),
                        ],
                    },
                )
            self.abort_if_validate_error(
                rule_validate_connect_config,
                config_connect,
            )

    def validate_create_connectors(self, data_validate):
        rule_base = self._validate_rule_param_base_create_connector()
        self.abort_if_validate_error(rule_base, data_validate)
        return self.validate_config_connect(data_validate)

    def validate_update_connectors(self, data_validate, detail_connector):
        merchant_id = detail_connector.get("merchant_id")
        lst_module_handle = SettingObjectHandleModel().get_list_key_source_handled_active(
            merchant_id, ConstantDataType.DATA_OUT, "google_sheet"
        )

        rule_validate_base = {
            ConstantParamApi.BodyCreateConnectors.NAME: [InstanceOf(str), Length(1)],
            ConstantParamApi.BodyCreateConnectors.DESCRIPTION: [InstanceOf(str)],
            ConstantParamApi.BodyCreateConnectors.SOURCE_KEY: [InstanceOf(str), Length(1)],
            ConstantParamApi.BodyCreateConnectors.SOURCE_TYPE: [InstanceOf(str), Length(1)],
            ConstantParamApi.BodyCreateConnectors.CONFIG_CONNECT: [
                InstanceOf(dict),
                Length(1),
            ],
        }
        self.validate_optional_err(rule_validate_base, data_validate)
        self.validate_config_connect(data_validate)
        rule_validate = {
            ConstantParamApi.BodyUpdateConnectors.CONFIG_SYNC_CALENDAR: [InstanceOf(dict)],
            ConstantParamApi.BodyUpdateConnectors.CONFIG_INFORMATION_OUT: [
                Required,
                InstanceOf(list),
                Each(
                    {
                        "object_type": [Required, InstanceOf(str), In(lst_module_handle)],
                        "object_id": [Required, InstanceOf(str), Length(1)],
                        "mapping_fields": [
                            Required,
                            InstanceOf(list),
                            Each(
                                {
                                    "field_source": [Required, InstanceOf(str), Length(1)],
                                    "field_target": [Required, InstanceOf(str), Length(1)],
                                    "title": [Required, InstanceOf(str)],
                                    "disable_edit": [Required, InstanceOf(bool)],
                                }
                            ),
                        ],
                        "column_list_mapping": [],
                    }
                ),
            ],
            ConstantParamApi.BodyUpdateConnectors.AUTO_CONNECTION_CHECK_INTERVAL: [
                InstanceOf(int),
                Equals(10),
            ],
            ConstantParamApi.BodyUpdateConnectors.MAX_NOTIFICATION_COUNT: [InstanceOf(int), Equals(6)],
            ConstantParamApi.BodyUpdateConnectors.SYNC_LIMIT: [InstanceOf(dict), Length(1)],
        }
        if data_validate.get(ConstantParamApi.BodyUpdateConnectors.CONTACT_INFO):
            rule_validate.update(
                {
                    ConstantParamApi.BodyUpdateConnectors.CONTACT_INFO: [
                        InstanceOf(list),
                        Length(1),
                        Each(
                            {
                                "type": [
                                    Required,
                                    InstanceOf(str),
                                    Length(1),
                                    In(ConstantTypeContactInfo.get_all_attribute()),
                                ],
                                "values": [Required, InstanceOf(list), Length(1)],
                            }
                        ),
                    ],
                }
            )

        self.validate_optional_err(rule_validate, data_validate)

        sync_limit = data_validate.get(ConstantParamApi.BodyUpdateConnectors.SYNC_LIMIT)

        rule_validate_sync_limit = {
            ConstantParamApi.BodyUpdateConnectors.SYNC_LIMIT_HTTP_CONNECTION_TIMEOUT: [
                InstanceOf(int),
                Equals(100),
            ]
        }
        if sync_limit:
            self.validate_optional_err(rule_validate_sync_limit, sync_limit)
        config_sync_calendar = data_validate.get(ConstantParamApi.BodyUpdateConnectors.CONFIG_SYNC_CALENDAR)
        if config_sync_calendar:
            data_validate[ConstantParamApi.BodyUpdateConnectors.CONFIG_SYNC_CALENDAR] = (
                self.validate_and_smooth_update_connectors_config_sync_calendar(config_sync_calendar)
            )

        # Thêm danh sách các cột hiện tại trên gg sheet
        config_information_out = data_validate.get(ConstantParamApi.BodyUpdateConnectors.CONFIG_INFORMATION_OUT)
        config_connect = (
            data_validate.get(ConstantParamApi.BodyCreateConnectors.CONFIG_CONNECT)
            if data_validate.get(ConstantParamApi.BodyCreateConnectors.CONFIG_CONNECT)
            else detail_connector.get(ConstantParamApi.BodyCreateConnectors.CONFIG_CONNECT)
        )
        column_list_mapping = self._get_column_list_mapping(merchant_id, config_connect, config_information_out)
        for item in config_information_out:
            item["column_list_mapping"] = column_list_mapping
        return data_validate

    def _get_column_list_mapping(self, merchant_id, config_connect, config_information_out):
        integration_account_id = config_connect.get(
            ConstantParamApi.BodyGoogleSheetConfigConnector.INTEGRATION_ACCOUNT_ID
        )
        spreadsheet_id = config_connect.get(ConstantParamApi.BodyGoogleSheetConfigConnector.SPREADSHEET_ID)
        sheet_id = config_connect.get(ConstantParamApi.BodyGoogleSheetConfigConnector.SHEET_ID)
        integration_account = IntegrationAccountModel().get_by_id(merchant_id, integration_account_id)
        if not integration_account:
            raise CustomError("Integration account not found")

        mapping_column_add = {}
        for config_information in config_information_out:
            mapping_fields = config_information.get("mapping_fields", [])
            for mapping_field in mapping_fields:
                disable_edit = mapping_field.get("disable_edit", False)
                title = mapping_field.get("title", "")
                field_target = mapping_field.get("field_target")
                if title and not disable_edit:
                    mapping_column_add[field_target] = title
        # mapping_column_add_sort = sorted(mapping_column_add)

        integration_account_config = integration_account.get("config", {})

        sheets, new_token, _ = IntegrationAccountGoogleHelper().get_sheets_in_spreadsheet(
            spreadsheet_id, integration_account_config
        )
        sheet_name = None
        for sheet in sheets:
            properties = sheet.get("properties", {})
            if properties.get("sheetId") == int(sheet_id):
                sheet_name = properties.get("title")
                break
        if not sheet_name:
            return []

        if mapping_column_add:

            body_add_column = []
            for key, value in mapping_column_add.items():
                body_add_column.append({"range": "{}!{}".format(sheet_name, key), "values": [[value]]})

            IntegrationAccountGoogleHelper().add_first_row_to_range_column(
                body_add_column, spreadsheet_id, integration_account_config
            )

        first_row, new_token = IntegrationAccountGoogleHelper().get_first_row_in_sheet_within_spreadsheet(
            spreadsheet_id, integration_account_config, sheet_name
        )

        if new_token:
            expiry = convert_isoformat_to_date(new_token.get("expiry"))
            new_token["expiry"] = expiry
            integration_account_config.update(new_token)
            # Log and update the integration account config
            MobioLogging().info("integration_account_config :: {}".format(integration_account_config))
            IntegrationAccountModel().update_by_set(
                {"_id": ObjectId(integration_account_id)}, {"config": integration_account_config}
            )
        results = []
        if first_row:
            value_first_row = first_row.get("values", [])

            def generate_column_letters(n):
                letters = []
                while n > 0:
                    n, remainder = divmod(n - 1, 26)
                    letters.append(string.ascii_uppercase[remainder])
                return "".join(reversed(letters))

            # Print each column header in the first row with column letter and index

            if value_first_row:
                for index, _ in enumerate(value_first_row[0], start=1):
                    column_letter = generate_column_letters(index)
                    results.append("{}{}".format(column_letter, str(1)))

        return results

    def validate_modified_content_connectors(self, data_validate, detail_connector):
        merchant_id = detail_connector.get("merchant_id")
        list_module_handle = SettingObjectHandleModel().get_list_key_source_handled_active(
            merchant_id, ConstantDataType.DATA_OUT, "google_sheet"
        )
        rule_validate_base = {
            ConstantParamApi.BodyCreateConnectors.NAME: [InstanceOf(str), Length(1)],
            ConstantParamApi.BodyCreateConnectors.DESCRIPTION: [InstanceOf(str)],
            ConstantParamApi.BodyCreateConnectors.SOURCE_KEY: [InstanceOf(str), Length(1)],
            ConstantParamApi.BodyCreateConnectors.SOURCE_TYPE: [InstanceOf(str), Length(1)],
            ConstantParamApi.BodyCreateConnectors.CONFIG_CONNECT: [
                InstanceOf(dict),
                Length(1),
            ],
        }
        self.validate_optional_err(rule_validate_base, data_validate)
        self.validate_config_connect(data_validate)
        rule_validate = {
            ConstantParamApi.BodyUpdateConnectors.CONFIG_SYNC_CALENDAR: [InstanceOf(dict)],
            ConstantParamApi.BodyUpdateConnectors.CONFIG_INFORMATION_OUT: [
                InstanceOf(list),
                Each(
                    {
                        "object_type": [InstanceOf(str), In(list_module_handle)],
                        "object_id": [InstanceOf(str), Length(1)],
                        "mapping_fields": [
                            InstanceOf(list),
                            Each(
                                {
                                    "field_source": [InstanceOf(str), Length(1)],
                                    "field_target": [InstanceOf(str), Length(1)],
                                }
                            ),
                        ],
                    }
                ),
            ],
            ConstantParamApi.BodyUpdateConnectors.AUTO_CONNECTION_CHECK_INTERVAL: [
                InstanceOf(int),
                Equals(10),
            ],
            ConstantParamApi.BodyUpdateConnectors.MAX_NOTIFICATION_COUNT: [InstanceOf(int), Equals(6)],
            ConstantParamApi.BodyUpdateConnectors.SYNC_LIMIT: [InstanceOf(dict), Length(1)],
        }
        if data_validate.get(ConstantParamApi.BodyUpdateConnectors.CONTACT_INFO):
            rule_validate.update(
                {
                    ConstantParamApi.BodyUpdateConnectors.CONTACT_INFO: [
                        InstanceOf(list),
                        Length(1),
                        Each(
                            {
                                "type": [
                                    Required,
                                    InstanceOf(str),
                                    Length(1),
                                    In(ConstantTypeContactInfo.get_all_attribute()),
                                ],
                                "values": [Required, InstanceOf(list), Length(1)],
                            }
                        ),
                    ],
                }
            )

        self.validate_optional_err(rule_validate, data_validate)

        sync_limit = data_validate.get(ConstantParamApi.BodyUpdateConnectors.SYNC_LIMIT)

        rule_validate_sync_limit = {
            ConstantParamApi.BodyUpdateConnectors.SYNC_LIMIT_HTTP_CONNECTION_TIMEOUT: [
                InstanceOf(int),
                Equals(100),
            ]
        }
        if sync_limit:
            self.validate_optional_err(rule_validate_sync_limit, sync_limit)

        config_sync_calendar = data_validate.get(ConstantParamApi.BodyUpdateConnectors.CONFIG_SYNC_CALENDAR)
        if config_sync_calendar:
            data_validate[ConstantParamApi.BodyUpdateConnectors.CONFIG_SYNC_CALENDAR] = (
                self.validate_and_smooth_update_connectors_config_sync_calendar(config_sync_calendar)
            )
        return data_validate

    def validate_and_smooth_update_connectors_config_sync_calendar(self, data_validate):
        rule_validate_config_sync_calendar = {
            "mode": [Required, InstanceOf(str), Length(1), In([ConstantModeConfigSyncCalendar.STREAMING])],
            "auto_retry_w_error": [Required, InstanceOf(dict), Length(1)],
        }

        self.validate_optional_err(rule_validate_config_sync_calendar, data_validate)
        return data_validate
