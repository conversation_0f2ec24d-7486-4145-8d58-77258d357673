#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 06/07/2024
"""


from mobio.libs.logging import MobioLogging
from mobio.libs.validator import Each, Equals, In, InstanceOf, Length, Required
from mobio.sdks.base.common.mobio_exception import CustomError

from src.common.data_flow_constant import (
    ConstantDataType,
    ConstantModeConfigSyncCalendar,
    ConstantParamApi,
    ConstantTypeContactInfo,
)
from src.helpers.validators.data_flow.data_out.server import BaseServer
from src.models.data_flow.event_config_model import InformationEventDataOutModel
from src.models.data_flow.object_handle_model import SettingObjectHandleModel


class Validate(BaseServer):

    def validate_config_connect(self, data_validate):
        MobioLogging().info("BaseServer :: validate_config_connect :: data_validate :: {}".format(data_validate))
        rule_validate_connect_config = rule_validate_connect_config = {
            ConstantParamApi.BodyWebhookCheckConnection.CONFIG_APP_ID: [
                InstanceOf(str),
                Required,
                Length(1),
            ],
            ConstantParamApi.BodyWebhookCheckConnection.CONFIG_CONNECT_CONTENT_TYPE: [
                InstanceOf(base_class=str),
                Required,
                In(["application/json"]),
            ],
            ConstantParamApi.BodyWebhookCheckConnection.CONFIG_CONNECT_METHODS: [
                InstanceOf(list),
                Required,
                Length(1),
            ],
            ConstantParamApi.BodyWebhookCheckConnection.CONFIG_URL: [
                InstanceOf(str),
                Required,
                Length(1),
            ],
            ConstantParamApi.BodyWebhookCheckConnection.CONFIG_CONNECT_PARAM_HEADERS: [
                InstanceOf(list),
                Required,
                Length(1),
            ],
        }
        if data_validate.get(ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT):
            self.abort_if_validate_error(
                rule_validate_connect_config,
                data_validate.get(ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT),
            )

    def validate_create_connectors(self, data_validate):
        rule_base = self._validate_rule_param_base_create_connector()
        self.abort_if_validate_error(rule_base, data_validate)
        return self.validate_config_connect(data_validate)

    def validate_update_connectors(self, data_validate, detail_connector):
        merchant_id = detail_connector.get("merchant_id")

        rule_validate_base = {
            ConstantParamApi.BodyCreateConnectors.NAME: [InstanceOf(str), Length(1)],
            ConstantParamApi.BodyCreateConnectors.DESCRIPTION: [InstanceOf(str)],
            ConstantParamApi.BodyCreateConnectors.SOURCE_KEY: [InstanceOf(str), Length(1)],
            ConstantParamApi.BodyCreateConnectors.SOURCE_TYPE: [InstanceOf(str), Length(1)],
            ConstantParamApi.BodyCreateConnectors.CONFIG_CONNECT: [
                InstanceOf(dict),
                Length(1),
            ],
        }
        self.validate_optional_err(rule_validate_base, data_validate)
        self.validate_config_connect(data_validate)
        rule_validate = {
            ConstantParamApi.BodyUpdateConnectors.CONFIG_SYNC_CALENDAR: [InstanceOf(dict)],
            ConstantParamApi.BodyUpdateConnectors.CONFIG_INFORMATION_OUT: [
                Required,
                InstanceOf(list),
            ],
            ConstantParamApi.BodyUpdateConnectors.CONTACT_INFO: [
                InstanceOf(list),
                Length(1),
                Each(
                    {
                        "type": [Required, InstanceOf(str), Length(1), In(ConstantTypeContactInfo.get_all_attribute())],
                        "values": [Required, InstanceOf(list), Length(1)],
                    }
                ),
            ],
            ConstantParamApi.BodyUpdateConnectors.AUTO_CONNECTION_CHECK_INTERVAL: [
                InstanceOf(int),
                Equals(10),
            ],
            ConstantParamApi.BodyUpdateConnectors.MAX_NOTIFICATION_COUNT: [InstanceOf(int), Equals(6)],
            ConstantParamApi.BodyUpdateConnectors.SYNC_LIMIT: [InstanceOf(dict), Length(1)],
        }

        self.validate_optional_err(rule_validate, data_validate)

        sync_limit = data_validate.get(ConstantParamApi.BodyUpdateConnectors.SYNC_LIMIT)

        rule_validate_sync_limit = {
            ConstantParamApi.BodyUpdateConnectors.SYNC_LIMIT_HTTP_CONNECTION_TIMEOUT: [
                InstanceOf(int),
                Equals(100),
            ]
        }
        if sync_limit:
            self.validate_optional_err(rule_validate_sync_limit, sync_limit)

        config_information_out = data_validate.get(ConstantParamApi.BodyUpdateConnectors.CONFIG_INFORMATION_OUT)

        list_module_handle = SettingObjectHandleModel().get_list_key_source_handled_active(
            merchant_id, ConstantDataType.DATA_OUT, "webhooks"
        )
        for config in config_information_out:
            module = config.get("module")
            if module not in list_module_handle:
                raise CustomError("Module {} không được xử lý!".format(module))
            event_keys = config.get("event_keys")
            if event_keys:
                lst_event_active_by_module = InformationEventDataOutModel().get_list_event_key_active_by_group_key(
                    module
                )

                event_key_error = set(event_keys) - set(lst_event_active_by_module)
                if event_key_error:
                    raise CustomError(
                        "Event key {} không được hỗ trợ xử lý trong module {}!".format(
                            ", ".join(event_key_error), module
                        )
                    )
        config_sync_calendar = data_validate.get(ConstantParamApi.BodyUpdateConnectors.CONFIG_SYNC_CALENDAR)
        if config_sync_calendar:
            data_validate[ConstantParamApi.BodyUpdateConnectors.CONFIG_SYNC_CALENDAR] = (
                self.validate_and_smooth_update_connectors_config_sync_calendar(config_sync_calendar)
            )
        return data_validate

    def validate_modified_content_connectors(self, data_validate, detail_connector):
        merchant_id = detail_connector.get("merchant_id")
        rule_validate_base = {
            ConstantParamApi.BodyCreateConnectors.NAME: [InstanceOf(str), Length(1)],
            ConstantParamApi.BodyCreateConnectors.DESCRIPTION: [InstanceOf(str)],
            ConstantParamApi.BodyCreateConnectors.SOURCE_KEY: [InstanceOf(str), Length(1)],
            ConstantParamApi.BodyCreateConnectors.SOURCE_TYPE: [InstanceOf(str), Length(1)],
            ConstantParamApi.BodyCreateConnectors.CONFIG_CONNECT: [
                InstanceOf(dict),
                Length(1),
            ],
        }
        self.validate_optional_err(rule_validate_base, data_validate)
        self.validate_config_connect(data_validate)
        rule_validate = {
            ConstantParamApi.BodyUpdateConnectors.CONFIG_SYNC_CALENDAR: [InstanceOf(dict)],
            ConstantParamApi.BodyUpdateConnectors.CONFIG_INFORMATION_OUT: [
                InstanceOf(list),
            ],
            ConstantParamApi.BodyUpdateConnectors.CONTACT_INFO: [
                InstanceOf(list),
                Length(1),
                Each(
                    {
                        "type": [Required, InstanceOf(str), Length(1), In(ConstantTypeContactInfo.get_all_attribute())],
                        "values": [Required, InstanceOf(list), Length(1)],
                    }
                ),
            ],
            ConstantParamApi.BodyUpdateConnectors.AUTO_CONNECTION_CHECK_INTERVAL: [
                InstanceOf(int),
                Equals(10),
            ],
            ConstantParamApi.BodyUpdateConnectors.MAX_NOTIFICATION_COUNT: [InstanceOf(int), Equals(6)],
            ConstantParamApi.BodyUpdateConnectors.SYNC_LIMIT: [InstanceOf(dict), Length(1)],
        }

        self.validate_optional_err(rule_validate, data_validate)

        sync_limit = data_validate.get(ConstantParamApi.BodyUpdateConnectors.SYNC_LIMIT)

        rule_validate_sync_limit = {
            ConstantParamApi.BodyUpdateConnectors.SYNC_LIMIT_HTTP_CONNECTION_TIMEOUT: [
                InstanceOf(int),
                Equals(100),
            ]
        }
        if sync_limit:
            self.validate_optional_err(rule_validate_sync_limit, sync_limit)

        config_information_out = data_validate.get(ConstantParamApi.BodyUpdateConnectors.CONFIG_INFORMATION_OUT)

        list_module_handle = SettingObjectHandleModel().get_list_key_source_handled_active(
            merchant_id, ConstantDataType.DATA_OUT, "webhooks"
        )
        if config_information_out:
            for config in config_information_out:
                module = config.get("module")
                if module not in list_module_handle:
                    raise CustomError("Module {} không được xử lý!".format(module))
                event_keys = config.get("event_keys")
                if event_keys:
                    lst_event_active_by_module = InformationEventDataOutModel().get_list_event_key_active_by_group_key(
                        module
                    )

                    event_key_error = set(event_keys) - set(lst_event_active_by_module)
                    if event_key_error:
                        raise CustomError(
                            "Event key {} không được hỗ trợ xử lý trong module {}!".format(
                                ", ".join(event_key_error), module
                            )
                        )
        config_sync_calendar = data_validate.get(ConstantParamApi.BodyUpdateConnectors.CONFIG_SYNC_CALENDAR)
        if config_sync_calendar:
            data_validate[ConstantParamApi.BodyUpdateConnectors.CONFIG_SYNC_CALENDAR] = (
                self.validate_and_smooth_update_connectors_config_sync_calendar(config_sync_calendar)
            )
        return data_validate

    def validate_and_smooth_update_connectors_config_sync_calendar(self, data_validate):
        rule_validate_config_sync_calendar = {
            "mode": [Required, InstanceOf(str), Length(1), In([ConstantModeConfigSyncCalendar.STREAMING])],
            "auto_retry_w_error": [Required, InstanceOf(dict), Length(1)],
        }

        self.validate_optional_err(rule_validate_config_sync_calendar, data_validate)
        return data_validate
