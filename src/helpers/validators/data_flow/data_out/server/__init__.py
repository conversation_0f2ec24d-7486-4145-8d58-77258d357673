#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 06/07/2024
"""

from abc import abstractmethod

from src.helpers.validators.data_flow.base import TemplateSettingValidator


class BaseServer(TemplateSettingValidator):
    @abstractmethod
    def validate_config_connect(self, data_validate):
        pass

    @abstractmethod
    def validate_create_connectors(self, data_validate):
        pass

    @abstractmethod
    def validate_check_connection(self, data_validate):
        pass

    @abstractmethod
    def validate_update_connectors(self, data_validate, detail_connector):
        pass

    @abstractmethod
    def validate_modified_content_connectors(self, data_validate):
        pass
