#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 26/06/2024
"""

from flask import request
from mobio.libs.validator import VALIDATION_RESULT, HttpValidator
from mobio.sdks.base.controllers import LangError, ParamInvalidError

from src.common.lang_config import LANG_VI, LangConfig


class BaseValidator(object):

    def abort_if_validate_error(self, rules, data):
        valid = HttpValidator(rules)
        val_result = valid.validate_object(data)
        if not val_result[VALIDATION_RESULT.VALID]:
            errors = val_result[VALIDATION_RESULT.ERRORS]
            raise ParamInvalidError(LangError.VALIDATE_ERROR, errors)

    def validate_optional_err(self, rules, data):
        valid = HttpValidator(rules)
        val_result = valid.validate_optional(data)
        if not val_result[VALIDATION_RESULT.VALID]:
            errors = val_result[VALIDATION_RESULT.ERRORS]
            raise ParamInvalidError(LangError.VALIDATE_ERROR, errors)

    def __init__(self) -> None:
        try:
            param = request.args.get("lang", LANG_VI)
            if param and param not in LangConfig().languages:
                raise ParamInvalidError(LangError.LANG_NOT_SUPPORT_ERROR)
            self.lang = LangConfig().lang_map(param)
        except:
            param = None
        self.language = param or LANG_VI
