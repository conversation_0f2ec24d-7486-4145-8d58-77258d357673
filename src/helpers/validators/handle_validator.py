#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 27/06/2024
"""


from src.helpers.dynamic_importer import dynamic_import_and_call


class HandlerValidator:

    def __init__(self) -> None:
        self.class_name = "Validate"

    def get_module_path(self, data_type, source_type, source_key):
        return f"src.helpers.validators.data_flow.{data_type}.{source_type}.{source_key}"

    def get_func_name(self, action):
        return f"validate_{action}"

    def call(self, data_type, source_type, source_key, action, *args, **kwargs):
        module_path = self.get_module_path(data_type, source_type, source_key)
        func_name = self.get_func_name(action)
        result = dynamic_import_and_call(module_path, self.class_name, func_name, *args, **kwargs)
        return result
