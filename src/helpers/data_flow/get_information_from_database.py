#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 28/06/2024
"""

import datetime

from mobio.libs.logging import MobioLogging
from mobio.sdks.base.common.mobio_exception import CustomError

from src.common.data_flow_constant import (
    ConstantDataType,
    ConstantSourceKey,
    ConstantSourceType,
    ConstantStatusConnectionDatabases,
)
from src.common.get_func_name import GetFuncNameHandler
from src.helpers.data_flow.check_connect_to_config_connect import HelperCheckConnection
from src.helpers.data_flow.connect_config_database import HelperConnectConfigDatabase


class HelperGetInformationFromDatabase:
    def get_func_get_information_from_database(self, action, source_key, source_type, data_type):
        return getattr(GetFuncNameHandler(), "get_func_name_information_databases")(
            action, source_key, source_type, data_type=data_type
        )

    def call_get(self, func_name, language, config_connect, *args):
        MobioLogging().info(f"Calling Check Connection: {func_name}")
        try:
            return getattr(self, func_name)(language, config_connect, *args)
        except AttributeError:
            raise CustomError(f"Func Check Connection {func_name} not found")

    def get_list_table_data_in_databases_postgres(self, language, config_connect, *args):
        status_connect, _ = HelperCheckConnection().call_check_connection(
            ConstantSourceKey.DataTypeIn.POSTGRES,
            ConstantSourceType.DATABASES,
            ConstantDataType.DATA_IN,
            language,
            config_connect,
            args[0],
        )
        if status_connect == ConstantStatusConnectionDatabases.FAIL:
            raise CustomError("Không thể connect tới database!")
        tables = HelperConnectConfigDatabase().postgres_tables(config_connect)
        return tables

    def get_list_table_data_in_databases_mysql(self, language, config_connect, *args):
        status_connect, _ = HelperCheckConnection().call_check_connection(
            ConstantSourceKey.DataTypeIn.MYSQL,
            ConstantSourceType.DATABASES,
            ConstantDataType.DATA_IN,
            language,
            config_connect,
            args[0],
        )
        if status_connect == ConstantStatusConnectionDatabases.FAIL:
            raise CustomError("Không thể connect tới database!")
        tables = HelperConnectConfigDatabase().mysql_tables(config_connect)
        return tables

    def get_list_table_data_in_databases_oracle(self, language, config_connect, *args):
        status_connect, _ = HelperCheckConnection().call_check_connection(
            ConstantSourceKey.DataTypeIn.ORACLE,
            ConstantSourceType.DATABASES,
            ConstantDataType.DATA_IN,
            language,
            config_connect,
            args[0],
        )
        if status_connect == ConstantStatusConnectionDatabases.FAIL:
            raise CustomError("Không thể connect tới database!")
        tables = HelperConnectConfigDatabase().oracle_tables(config_connect)
        return tables

    def get_list_table_data_in_databases_db2(self, language, config_connect, *args):
        status_connect, _ = HelperCheckConnection().call_check_connection(
            ConstantSourceKey.DataTypeIn.DB2,
            ConstantSourceType.DATABASES,
            ConstantDataType.DATA_IN,
            language,
            config_connect,
            args[0],
        )
        if status_connect == ConstantStatusConnectionDatabases.FAIL:
            raise CustomError("Không thể connect tới database!")
        tables = HelperConnectConfigDatabase().db2_tables(config_connect)
        return tables

    def get_list_table_data_in_databases_sqlserver(self, language, config_connect, *args):
        status_connect, _ = HelperCheckConnection().call_check_connection(
            ConstantSourceKey.DataTypeIn.SQLSERVER,
            ConstantSourceType.DATABASES,
            ConstantDataType.DATA_IN,
            language,
            config_connect,
            args[0],
        )
        if status_connect == ConstantStatusConnectionDatabases.FAIL:
            raise CustomError("Không thể connect tới database!")
        tables = HelperConnectConfigDatabase().sqlserver_tables(config_connect)
        return tables

    def get_data_sample_data_in_databases_postgres(self, language, config_connect, *args):
        status_connect, _ = HelperCheckConnection().call_check_connection(
            source_key=ConstantSourceKey.DataTypeIn.POSTGRES,
            source_type=ConstantSourceType.DATABASES,
            data_type=ConstantDataType.DATA_IN,
            language=language,
            config_connect=config_connect,
            merchant_id=args[0],
        )
        if status_connect == ConstantStatusConnectionDatabases.FAIL:
            raise CustomError("Không thể connect tới database!")
        column_names, rows = HelperConnectConfigDatabase().postgres_get_data_sample(args[1], args[2], args[3], language, config_connect)
        columns = [c["column_name"] for c in column_names]
        results = [
            {
                column_name: (
                    v.isoformat().replace("T", " ")
                    if isinstance(v, (datetime.datetime, datetime.date, datetime.time))
                    else v
                )
                for column_name, v in zip(columns, row)
            }
            for row in rows
        ]
        return column_names, results, len(results)

    def get_data_sample_data_in_databases_mysql(self, language, config_connect, *args):
        status_connect, _ = HelperCheckConnection().call_check_connection(
            source_key=ConstantSourceKey.DataTypeIn.MYSQL,
            source_type=ConstantSourceType.DATABASES,
            data_type=ConstantDataType.DATA_IN,
            language=language,
            config_connect=config_connect,
            merchant_id=args[0],
        )
        if status_connect == ConstantStatusConnectionDatabases.FAIL:
            raise CustomError("Không thể connect tới database!")
        column_names, rows = HelperConnectConfigDatabase().mysql_get_data_sample(args[1], args[2], language, config_connect)
        columns = [c["column_name"] for c in column_names]
        results = [
            {
                column_name: (
                    v.isoformat().replace("T", " ") if isinstance(v, (datetime.datetime, datetime.date)) else v
                )
                for column_name, v in zip(columns, row)
            }
            for row in rows
        ]
        return column_names, results, len(results)

    def get_data_sample_data_in_databases_oracle(self, language, config_connect, *args):
        status_connect, _ = HelperCheckConnection().call_check_connection(
            source_key=ConstantSourceKey.DataTypeIn.ORACLE,
            source_type=ConstantSourceType.DATABASES,
            data_type=ConstantDataType.DATA_IN,
            language=language,
            config_connect=config_connect,
            merchant_id=args[0],
        )
        if status_connect == ConstantStatusConnectionDatabases.FAIL:
            raise CustomError("Không thể connect tới database!")
        column_names, rows = HelperConnectConfigDatabase().oracle_get_data_sample(args[1], args[2], language, config_connect)
        columns = [c["column_name"] for c in column_names]
        results = [
            {
                column_name: (
                    v.isoformat().replace("T", " ") if isinstance(v, (datetime.datetime, datetime.date)) else v
                )
                for column_name, v in zip(columns, row)
            }
            for row in rows
        ]
        return column_names, results, len(results)

    def get_data_sample_data_in_databases_db2(self, language, config_connect, *args):
        status_connect, _ = HelperCheckConnection().call_check_connection(
            source_key=ConstantSourceKey.DataTypeIn.DB2,
            source_type=ConstantSourceType.DATABASES,
            data_type=ConstantDataType.DATA_IN,
            language=language,
            config_connect=config_connect,
            merchant_id=args[0],
        )
        if status_connect == ConstantStatusConnectionDatabases.FAIL:
            raise CustomError("Không thể connect tới database!")
        column_names, rows = HelperConnectConfigDatabase().db2_get_data_sample(
            args[1], args[2], args[3], language, config_connect
        )
        columns = [c["column_name"] for c in column_names]
        results = [
            {
                column_name: (
                    v.isoformat().replace("T", " ")
                    if isinstance(v, (datetime.datetime, datetime.date, datetime.time))
                    else v
                )
                for column_name, v in zip(columns, row)
            }
            for row in rows
        ]
        return column_names, results, len(results)

    def get_data_sample_data_in_databases_sqlserver(self, language, config_connect, *args):
        status_connect, _ = HelperCheckConnection().call_check_connection(
            ConstantSourceKey.DataTypeIn.SQLSERVER,
            ConstantSourceType.DATABASES,
            ConstantDataType.DATA_IN,
            language,
            config_connect,
            args[0],
        )
        if status_connect == ConstantStatusConnectionDatabases.FAIL:
            raise CustomError("Không thể connect tới database!")
        column_names, rows = HelperConnectConfigDatabase().sqlserver_get_data_sample(args[1], args[2], args[3], language, config_connect)
        columns = [c["column_name"] for c in column_names]
        results = [
            {
                column_name: (
                    v.isoformat().replace("T", " ") if isinstance(v, (datetime.datetime, datetime.date)) else v
                )
                for column_name, v in zip(columns, row)
            }
            for row in rows
        ]
        return column_names, results, len(results)

    def get_schema_of_table_data_in_databases_postgres(self, language, config_connect, *args):
        status_connect, _ = HelperCheckConnection().call_check_connection(
            ConstantSourceKey.DataTypeIn.POSTGRES,
            ConstantSourceType.DATABASES,
            data_type=ConstantDataType.DATA_IN,
            language=language,
            config_connect=config_connect,
            merchant_id=args[0],
        )
        if status_connect == ConstantStatusConnectionDatabases.FAIL:
            raise CustomError("Không thể connect tới database!")
        schemas = HelperConnectConfigDatabase().postgres_get_schema(args[1], args[2], config_connect)
        return schemas

    def get_information_column_table_data_in_databases_postgres(self, language, config_connect, *args):
        status_connect, _ = HelperCheckConnection().call_check_connection(
            ConstantSourceKey.DataTypeIn.POSTGRES,
            ConstantSourceType.DATABASES,
            data_type=ConstantDataType.DATA_IN,
            language=language,
            config_connect=config_connect,
            merchant_id=args[0],
        )
        if status_connect == ConstantStatusConnectionDatabases.FAIL:
            raise CustomError("Không thể connect tới database!")
        results = HelperConnectConfigDatabase().postgres_get_information_column_table(args[1], args[2], config_connect)
        return results

    def get_schema_of_table_data_in_databases_mysql(self, language, config_connect, *args):
        status_connect, _ = HelperCheckConnection().call_check_connection(
            source_key=ConstantSourceKey.DataTypeIn.MYSQL,
            source_type=ConstantSourceType.DATABASES,
            data_type=ConstantDataType.DATA_IN,
            language=language,
            config_connect=config_connect,
            merchant_id=args[0],
        )
        if status_connect == ConstantStatusConnectionDatabases.FAIL:
            raise CustomError("Không thể connect tới database!")
        schemas = HelperConnectConfigDatabase().mysql_get_schema(args[1], config_connect)
        return schemas

    def get_information_column_table_data_in_databases_mysql(self, language, config_connect, *args):
        status_connect, _ = HelperCheckConnection().call_check_connection(
            ConstantSourceKey.DataTypeIn.MYSQL,
            ConstantSourceType.DATABASES,
            data_type=ConstantDataType.DATA_IN,
            language=language,
            config_connect=config_connect,
            merchant_id=args[0],
        )
        if status_connect == ConstantStatusConnectionDatabases.FAIL:
            raise CustomError("Không thể connect tới database!")
        results = HelperConnectConfigDatabase().mysql_get_information_column_table(args[1], config_connect)
        return results

    def get_schema_of_table_data_in_databases_oracle(self, language, config_connect, *args):
        status_connect, _ = HelperCheckConnection().call_check_connection(
            source_key=ConstantSourceKey.DataTypeIn.ORACLE,
            source_type=ConstantSourceType.DATABASES,
            data_type=ConstantDataType.DATA_IN,
            language=language,
            config_connect=config_connect,
            merchant_id=args[0],
        )
        if status_connect == ConstantStatusConnectionDatabases.FAIL:
            raise CustomError("Không thể connect tới database!")
        schemas = HelperConnectConfigDatabase().oracle_get_schema(args[1], config_connect)
        return schemas

    def get_information_column_table_data_in_databases_oracle(self, language, config_connect, *args):
        status_connect, _ = HelperCheckConnection().call_check_connection(
            ConstantSourceKey.DataTypeIn.ORACLE,
            ConstantSourceType.DATABASES,
            data_type=ConstantDataType.DATA_IN,
            language=language,
            config_connect=config_connect,
            merchant_id=args[0],
        )
        if status_connect == ConstantStatusConnectionDatabases.FAIL:
            raise CustomError("Không thể connect tới database!")
        results = HelperConnectConfigDatabase().oracle_get_information_column_table(args[1], config_connect)
        return results

    def get_information_column_table_data_in_databases_db2(self, language, config_connect, *args):
        status_connect, _ = HelperCheckConnection().call_check_connection(
            ConstantSourceKey.DataTypeIn.DB2,
            ConstantSourceType.DATABASES,
            data_type=ConstantDataType.DATA_IN,
            language=language,
            config_connect=config_connect,
            merchant_id=args[0],
        )
        if status_connect == ConstantStatusConnectionDatabases.FAIL:
            raise CustomError("Không thể connect tới database!")
        results = HelperConnectConfigDatabase().db2_get_information_column_table(args[1], args[2], config_connect)
        return results

    def get_information_column_table_data_in_databases_sqlserver(self, language, config_connect, *args):
        status_connect, _ = HelperCheckConnection().call_check_connection(
            ConstantSourceKey.DataTypeIn.SQLSERVER,
            ConstantSourceType.DATABASES,
            data_type=ConstantDataType.DATA_IN,
            language=language,
            config_connect=config_connect,
            merchant_id=args[0],
        )
        if status_connect == ConstantStatusConnectionDatabases.FAIL:
            raise CustomError("Không thể connect tới database!")
        results = HelperConnectConfigDatabase().sqlserver_get_information_column_table(args[1], args[2], config_connect)
        return results

    def get_list_table_view_data_in_databases_postgres(self, language, config_connect, *args):
        status_connect, _ = HelperCheckConnection().call_check_connection(
            ConstantSourceKey.DataTypeIn.POSTGRES,
            ConstantSourceType.DATABASES,
            ConstantDataType.DATA_IN,
            language,
            config_connect,
            args[0],
        )
        if status_connect == ConstantStatusConnectionDatabases.FAIL:
            raise CustomError("Không thể connect tới database!")
        tables = HelperConnectConfigDatabase().postgres_tables_views(config_connect, args[1])
        return tables

    def get_list_table_view_data_in_databases_mysql(self, language, config_connect, *args):
        status_connect, _ = HelperCheckConnection().call_check_connection(
            ConstantSourceKey.DataTypeIn.MYSQL,
            ConstantSourceType.DATABASES,
            ConstantDataType.DATA_IN,
            language,
            config_connect,
            args[0],
        )
        if status_connect == ConstantStatusConnectionDatabases.FAIL:
            raise CustomError("Không thể connect tới database!")
        tables = HelperConnectConfigDatabase().mysql_tables_views(config_connect, args[1])
        return tables

    def get_list_table_view_data_in_databases_oracle(self, language, config_connect, *args):
        status_connect, _ = HelperCheckConnection().call_check_connection(
            ConstantSourceKey.DataTypeIn.ORACLE,
            ConstantSourceType.DATABASES,
            ConstantDataType.DATA_IN,
            language,
            config_connect,
            args[0],
        )
        if status_connect == ConstantStatusConnectionDatabases.FAIL:
            raise CustomError("Không thể connect tới database!")
        tables = HelperConnectConfigDatabase().oracle_tables_views(config_connect, args[1])
        return tables

    def get_list_table_view_data_in_databases_db2(self, language, config_connect, *args):
        status_connect, _ = HelperCheckConnection().call_check_connection(
            ConstantSourceKey.DataTypeIn.DB2,
            ConstantSourceType.DATABASES,
            ConstantDataType.DATA_IN,
            language,
            config_connect,
            args[0],
        )
        if status_connect == ConstantStatusConnectionDatabases.FAIL:
            raise CustomError("Không thể connect tới database!")
        tables = HelperConnectConfigDatabase().db2_tables_views(config_connect, args[1])
        return tables

    def get_list_table_view_data_in_databases_sqlserver(self, language, config_connect, *args):
        status_connect, _ = HelperCheckConnection().call_check_connection(
            ConstantSourceKey.DataTypeIn.SQLSERVER,
            ConstantSourceType.DATABASES,
            ConstantDataType.DATA_IN,
            language,
            config_connect,
            args[0],
        )
        if status_connect == ConstantStatusConnectionDatabases.FAIL:
            raise CustomError("Không thể connect tới database!")
        tables = HelperConnectConfigDatabase().sqlserver_tables_views(config_connect, args[1])
        return tables

    def get_schema_of_table_view_data_in_databases_sqlserver(self, language, config_connect, *args):
        status_connect, _ = HelperCheckConnection().call_check_connection(
            ConstantSourceKey.DataTypeIn.SQLSERVER,
            ConstantSourceType.DATABASES,
            ConstantDataType.DATA_IN,
            language,
            config_connect,
            args[0],
        )
        if status_connect == ConstantStatusConnectionDatabases.FAIL:
            raise CustomError("Không thể connect tới database!")
        schemas = HelperConnectConfigDatabase().sqlserver_get_schema(args[1], args[2], config_connect)
        return schemas

    def get_schema_of_table_data_in_databases_db2(self, language, config_connect, *args):
        status_connect, _ = HelperCheckConnection().call_check_connection(
            ConstantSourceKey.DataTypeIn.DB2,
            ConstantSourceType.DATABASES,
            data_type=ConstantDataType.DATA_IN,
            language=language,
            config_connect=config_connect,
            merchant_id=args[0],
        )
        if status_connect == ConstantStatusConnectionDatabases.FAIL:
            raise CustomError("Không thể connect tới database!")
        schemas = HelperConnectConfigDatabase().db2_get_schema(args[1], args[2], config_connect)
        return schemas

    def get_schema_of_table_data_in_databases_sqlserver(self, language, config_connect, *args):
        status_connect, _ = HelperCheckConnection().call_check_connection(
            ConstantSourceKey.DataTypeIn.SQLSERVER,
            ConstantSourceType.DATABASES,
            ConstantDataType.DATA_IN,
            language,
            config_connect,
            args[0],
        )
        if status_connect == ConstantStatusConnectionDatabases.FAIL:
            raise CustomError("Không thể connect tới database!")
        schemas = HelperConnectConfigDatabase().sqlserver_get_schema(args[1], args[2], config_connect)
        return schemas
