#!/usr/bin/env python
# -*- coding: utf-8 -*-
""" Author: ChungNT
    Company: MobioVN
    Date created: 24/08/2023
"""
import datetime
import socket
import urllib.parse

import requests
from googleapiclient.discovery import build
from mobio.libs.logging import MobioLogging

from src.helpers.integration_account.utils.memory_cache import MemoryCache

# from src.common.utils import generate_expiry


class GoogleUtil(object):

    def create_service_oauth2_api(self, credentials):
        service = None
        try:
            socket.setdefaulttimeout(20)
            service = build(serviceName="oauth2", version="v2", credentials=credentials, cache=MemoryCache())
        except Exception as er:
            err_msg = "create_service_oauth2_api:: err: {}".format(er)
            MobioLogging().error(err_msg)
        return service

    def get_user_info(self, credentials):
        user_info = None
        try:
            user_info_service = self.create_service_oauth2_api(credentials)
            if user_info_service:
                user_info = user_info_service.userinfo().get().execute()
        except Exception as er:
            return user_info
        return user_info

    @staticmethod
    def request_get_user_info(access_token):
        uri = "https://www.googleapis.com/oauth2/v1/userinfo"

        params = {"access_token": access_token, "alt": "json"}

        headers = {"Content-Type": "application/json"}
        response = requests.get(uri, params=params, headers=headers)
        return response

    @staticmethod
    def request_get_token_info(access_token):
        uri = "https://www.googleapis.com/oauth2/v1/tokeninfo"

        params = {"access_token": access_token}

        headers = {"Content-Type": "application/json"}
        response = requests.get(uri, params=params, headers=headers)
        return response

    @staticmethod
    def request_get_refresh_token(client_id, client_secret, authorization_code, redirect_uri):
        uri = "https://oauth2.googleapis.com/token"

        body = {
            "client_id": client_id,
            "client_secret": client_secret,
            "code": authorization_code,
            "grant_type": "authorization_code",
            "redirect_uri": redirect_uri,
        }

        headers = {"Content-Type": "application/x-www-form-urlencoded"}

        response = requests.post(uri, data=body, headers=headers)
        return response

    @staticmethod
    def request_renew_access_token(client_id, client_secret, refresh_token):
        uri = "https://oauth2.googleapis.com/token"

        body = {
            "client_id": client_id,
            "client_secret": client_secret,
            "grant_type": "refresh_token",
            "refresh_token": refresh_token,
        }

        headers = {"Content-Type": "application/x-www-form-urlencoded"}

        response = requests.post(uri, data=body, headers=headers)
        return response

    @staticmethod
    def request_revoke_access_token(token):
        uri = "https://oauth2.googleapis.com/revoke"

        body = {
            "token": token,
        }

        headers = {"Content-Type": "application/x-www-form-urlencoded"}

        response = requests.post(uri, data=body, headers=headers)
        return response


class BaseGoogleService(object):
    def __init__(self, token_type, access_token, client_id, client_secret, expiry=None, refresh_token=None):
        self.token_type = token_type
        self.access_token = access_token
        self.client_id = client_id
        self.client_secret = client_secret
        self.expiry = expiry
        self.refresh_token = refresh_token
        self.authorization = None
        self.valid = True
        self.__validate()
        self.generate_authorization()

    def __validate(self):
        # if not expiry or expiry < time_now:
        if not self.expiry or self.expiry < int(datetime.datetime.utcnow().timestamp()):
            self.valid = False

        if not self.valid:
            self.__generate_new_access_token()

    def __generate_new_access_token(self):
        response = GoogleUtil.request_renew_access_token(self.client_id, self.client_secret, self.refresh_token)
        js_response = response.json()
        if response.status_code == 200:

            # js_response = generate_expiry(js_response)
            # update to database
            # ConnectionModel().update_new_access_token({"refresh_token": self.refresh_token}, js_response)

            # Update data to instance connect
            self.token_type = js_response.get("token_type")
            self.access_token = js_response.get("access_token")

        else:
            self.token_type = None
            self.access_token = None
            data_log = {"end_point": "BaseGoogleService::__generate_new_access_token", "error": js_response}
            # LogSyncGoogleSheetModel().create_data(data_log)

    def generate_authorization(self):
        if self.token_type and self.access_token:
            self.authorization = self.token_type + " " + self.access_token


class GoogleDriveUtil(BaseGoogleService):
    def __init__(self, token_type, access_token, expiry, refresh_token, client_id, client_secret):
        super().__init__(
            token_type=token_type,
            access_token=access_token,
            client_id=client_id,
            client_secret=client_secret,
            expiry=expiry,
            refresh_token=refresh_token,
        )

    def get_list_folder(self, page_size):
        uri = "https://www.googleapis.com/drive/v3/files"

        params = {
            "pageSize": page_size,
            "q": "mimeType='application/vnd.google-apps.folder' and 'me' in owners and trashed=false and 'root' in parents and name contains 'Mopage - Lưu trữ dữ liệu từ biểu mẫu'",
            "supportsAllDrives": False,
            "orderBy": "modifiedTime desc",
        }

        headers = {
            "Content-Type": "application/json",
            "Authorization": self.authorization,
        }

        response = requests.get(uri, params=params, headers=headers)
        return response

    def create_folder(
        self,
    ):
        uri = "https://www.googleapis.com/drive/v3/files"
        body = {"name": "Mopage - Lưu trữ dữ liệu từ biểu mẫu", "mimeType": "application/vnd.google-apps.folder"}

        headers = {
            "Content-Type": "application/json",
            "Authorization": self.authorization,
        }

        response = requests.post(uri, json=body, headers=headers)
        return response

    def move_file_to_folder(self, file_id, folder_id):
        uri = "https://www.googleapis.com/drive/v3/files/{}".format(file_id)
        params = {"addParents": folder_id}

        headers = {
            "Content-Type": "application/json",
            "Authorization": self.authorization,
        }

        response = requests.patch(uri, params=params, headers=headers)
        return response

    def get_list_spreadsheet(self, page_size, page_token, folder_id):
        uri = "https://www.googleapis.com/drive/v3/files"

        params = {
            "pageSize": page_size,
            "q": "mimeType='application/vnd.google-apps.spreadsheet' and 'me' in owners and trashed=false and '{}' in parents".format(
                folder_id
            ),
            "supportsAllDrives": False,
            "orderBy": "modifiedTime desc",
            "pageToken": page_token if page_token else None,
        }

        headers = {
            "Content-Type": "application/json",
            "Authorization": self.authorization,
        }

        response = requests.get(uri, params=params, headers=headers)
        if response.status_code == 200:
            return response.json()

        return {}


class GoogleSpreadSheetUtil(BaseGoogleService):

    def __init__(self, token_type, access_token, expiry, refresh_token, client_id, client_secret):
        super().__init__(
            token_type=token_type,
            access_token=access_token,
            client_id=client_id,
            client_secret=client_secret,
            expiry=expiry,
            refresh_token=refresh_token,
        )

    def create_spreadsheet(self, body=None):
        uri = "https://sheets.googleapis.com/v4/spreadsheets"
        if not body:
            body = {}

        headers = {
            "Content-Type": "application/json",
            "Authorization": self.authorization,
        }

        response = requests.post(uri, json=body, headers=headers)
        return response.json()

    def get_detail_spreadsheet(self, spreadsheets_id):
        uri = "https://sheets.googleapis.com/v4/spreadsheets/{}".format(spreadsheets_id)

        headers = {
            "Content-Type": "application/json",
            "Authorization": self.authorization,
        }

        response = requests.get(uri, headers=headers)
        return response.json()

    def append_data(self, spreadsheet_id, sheet_title, data_update):
        uri = "https://sheets.googleapis.com/v4/spreadsheets/{}/values/{}:append".format(spreadsheet_id, sheet_title)

        params = {"valueInputOption": "RAW"}
        body = {"range": "", "values": data_update}

        headers = {
            "Content-Type": "application/json",
            "Authorization": self.authorization,
        }

        response = requests.post(uri, params=params, json=body, headers=headers)
        return response

    def update_data_row_one(self, spreadsheet_id, sheet_title, data_update):
        uri = "https://sheets.googleapis.com/v4/spreadsheets/{}/values/{}".format(spreadsheet_id, sheet_title)

        params = {"valueInputOption": "RAW"}
        body = {"range": "", "values": [data_update]}

        headers = {
            "Content-Type": "application/json",
            "Authorization": self.authorization,
        }

        response = requests.put(uri, params=params, json=body, headers=headers)
        if response.status_code == 200:
            return response.json()

        return {}

    def get_first_row(self, spreadsheet_id, sheet_title):

        # Phải parse vì nếu có dấu + sẽ lỗi, khi call google hiểu là dấu spase
        sheet_title_param = urllib.parse.quote(sheet_title)

        uri = "https://sheets.googleapis.com/v4/spreadsheets/{}/values/{}!A1:Z1".format(
            spreadsheet_id, sheet_title_param
        )

        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "Authorization": self.authorization,
        }

        response = requests.get(uri, headers=headers)
        if response.status_code == 200:
            return response.json()

        return {}


if __name__ == "__main__":
    pass
