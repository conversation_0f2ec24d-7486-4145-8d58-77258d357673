#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 12/09/2024
"""


from google_auth_oauthlib.flow import Flow
from mobio.libs.logging import MobioLogging

from src.common.integration_account_constant import TypeIntegrationAccount
from src.models.integration_account.setting_integration_account_model import (
    SettingIntegrationAccountModel,
)


class GetInformationConfigApp(object):

    def get_config_integration_by_type(self, merchant_id, integration_by_type):
        # TODO: Implement logic to get config integration by type
        setting = SettingIntegrationAccountModel().find_one({"type": integration_by_type})
        if setting:
            return setting.get("config")
        return {}

    def google_sheet(self, merchant_id, **kwargs):
        # TODO: Implement logic to get google sheet config
        state_encrypt = kwargs.get("state_encrypt")
        client_config, redirect_uri, scopes = self.generate_config_google_sheet(merchant_id)
        flow = Flow.from_client_config(client_config=client_config, scopes=scopes)
        flow.redirect_uri = redirect_uri
        (authorization_url, state) = flow.authorization_url(
            access_type="offline", include_granted_scopes="true", prompt="consent", state=state_encrypt
        )
        MobioLogging().info(
            "GetInformationConfigApp :: google_sheet :: authorization_url :: {}, state :: {}".format(
                authorization_url, state
            )
        )
        return authorization_url

    def generate_config_google_sheet(self, merchant_id):
        config_integration = self.get_config_integration_by_type(merchant_id, TypeIntegrationAccount.GOOGLE_SHEET)
        if not config_integration:
            raise Exception("Could not get google sheets")

        redirect_uri = config_integration.get("web").get("redirect_uris")[0]

        scopes = config_integration.get("scopes")

        client_config = {"web": config_integration.get("web")}
        return client_config, redirect_uri, scopes
