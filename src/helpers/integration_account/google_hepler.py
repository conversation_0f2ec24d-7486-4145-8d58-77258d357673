#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 13/09/2024
"""
import json

from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import Flow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from mobio.libs.logging import MobioLogging

from src.common import lru_redis_cache
from src.helpers.integration_account.get_information_config_app import (
    GetInformationConfigApp,
)
from src.helpers.integration_account.utils.google_util import GoogleUtil
from src.helpers.integration_account.utils.memory_cache import MemoryCache


class IntegrationAccountGoogleHelper:

    def get_user_info(self, merchant_id, request_url, config_type):
        param_error = {"connect_type": 1}
        try:
            client_config, redirect_uri, scopes = getattr(
                GetInformationConfigApp(), "generate_config_{}".format(config_type)
            )(merchant_id)

        except Exception as e:
            MobioLogging().error("get_user_info error: {}".format(e))
            return None, {"error_mess": "user_info_not_found", "connect_type": 1}
        # TODO: Implement logic to get google sheet config
        flow = Flow.from_client_config(client_config=client_config, scopes=scopes)
        flow.redirect_uri = redirect_uri
        authorization_response = request_url
        if authorization_response.startswith("http://"):
            authorization_response = authorization_response.replace("http://", "https://", 1)
        try:
            flow.fetch_token(authorization_response=authorization_response)
        except Exception as e:
            MobioLogging().error("fetch_token error: {}".format(e))
            return None, {"error_mess": "user_not_access_scope", "connect_type": 1}
        credentials = flow.credentials
        user_info = GoogleUtil().get_user_info(credentials)
        user_data = None
        if user_info:
            user_data = self.__credentials_to_dict(credentials)
            email = user_info.get("email")
            name = user_info.get("name")
            user_data.update({"user_id": user_info.get("id"), "email": email, "name": name})
            user_data["profile"] = user_info
        else:
            param_error = {"error_mess": "user_info_not_found", "connect_type": 1}
        return user_data, param_error

    @classmethod
    def __credentials_to_dict(cls, credentials):
        return {
            "token": credentials.token,
            "refresh_token": credentials.refresh_token,
            "token_uri": credentials.token_uri,
            "client_id": credentials.client_id,
            "client_secret": credentials.client_secret,
            "scopes": credentials.scopes,
            "expiry": credentials.expiry,
        }

    @classmethod
    def _generate_data_creds_google_for_config(cls, account_config):
        config_gg = {
            "token": account_config.get("token"),
            "refresh_token": account_config.get("refresh_token"),
            "token_uri": account_config.get("token_uri"),
            "client_id": account_config.get("client_id"),
            "client_secret": account_config.get("client_secret"),
            "expiry": account_config.get("expiry"),
            "scopes": account_config.get("scopes"),
        }
        return config_gg

    def get_credentials_valid_by_account_info(self, account_config):
        creds = None
        data_token = None
        try:
            data_creds = self._generate_data_creds_google_for_config(account_config)
            creds = Credentials(**data_creds)
            if not creds or not creds.valid:
                if creds and creds.expired and creds.refresh_token:
                    try:
                        creds.refresh(Request())
                        data_token = json.loads(creds.to_json())
                        # expiry trả về là string, cần convert sang date

                    except Exception as er:
                        _, info_error = er.args
                        error = info_error.get("error", "")
        except Exception as er:
            MobioLogging().error("{}:: err: {}".format("get_credentials_valid_by_account_info", er))
        return creds, data_token

    @lru_redis_cache.add_for_class(expiration=600)
    def _create_service_drive(self, credentials):
        service = build("drive", "v3", credentials=credentials, cache=MemoryCache())
        return service

    @lru_redis_cache.add_for_class(expiration=600)
    def _create_service_sheets(self, credentials):
        service = build("sheets", "v4", credentials=credentials, cache=MemoryCache())
        return service

    def create_folder_drive_google_sheet(self, merchant_id, account_config, folder_name):

        credentials, data_token = self.get_credentials_valid_by_account_info(account_config)
        if credentials:

            try:
                # create drive api client
                service = self._create_service_drive(credentials)
                file_metadata = {
                    "name": folder_name,
                    "mimeType": "application/vnd.google-apps.folder",
                }

                file = service.files().create(body=file_metadata, fields="id").execute()
                MobioLogging().info("create_folder_drive_google_sheet :: file :: {}".format(file))
                return file.get("id"), data_token

            except HttpError as error:
                MobioLogging().error("create_folder_drive_google_sheet :: error :: {}".format(error))
        return None, data_token

    def get_folder_drive_google_sheet_by_id(self, folder_id, account_config):

        credentials, data_token = self.get_credentials_valid_by_account_info(account_config)
        if credentials:

            try:
                # create drive api client
                service = self._create_service_drive(credentials)

                res = service.files().get(fileId=folder_id, fields="id, name").execute()
                MobioLogging().info("get_folder_drive_google_sheet_by_id :: response :: {}".format(res))
                return res, data_token

            except HttpError as error:
                MobioLogging().error("get_folder_drive_google_sheet_by_id :: error :: {}".format(error))
        return None, data_token

    def get_list_spreadsheet_in_folder(self, folder_id, account_config, page_token, per_page, search):
        credentials, data_token = self.get_credentials_valid_by_account_info(account_config)
        if credentials:
            try:
                # create drive api client
                service = self._create_service_drive(credentials)
                query = (
                    "mimeType='application/vnd.google-apps.spreadsheet' and trashed = false and parents in '{}'".format(
                        folder_id
                    )
                )
                if search:
                    query += " and name contains '{}'".format(search)
                res = (
                    service.files()
                    .list(
                        q=query,
                        spaces="drive",
                        fields="nextPageToken, files(id, name)",
                        pageToken=page_token,
                        pageSize=per_page,
                    )
                    .execute()
                )
                MobioLogging().info("get_list_spreadsheet_in_folder :: response :: {}".format(res))
                return res, data_token

            except HttpError as error:
                MobioLogging().error("get_list_spreadsheet_in_folder :: error :: {}".format(error))
        return None, data_token

    def add_spreadsheet_in_folder(self, folder_id, account_config, file_name):
        credentials, data_token = self.get_credentials_valid_by_account_info(account_config)
        if credentials:
            try:
                # create drive api client
                service = self._create_service_drive(credentials)
                file_metadata = {
                    "name": file_name,
                    "parents": [folder_id],
                    "mimeType": "application/vnd.google-apps.spreadsheet",
                }
                spreadsheet = service.files().create(body=file_metadata, fields="id").execute()
                MobioLogging().info("add_spreadsheet_in_folder :: response :: {}".format(spreadsheet))
                return spreadsheet.get("id"), data_token

            except HttpError as error:
                MobioLogging().error("add_spreadsheet_in_folder :: error :: {}".format(error))
        return None, data_token

    def get_sheets_in_spreadsheet(self, spreadsheet_id, account_config):
        credentials, data_token = self.get_credentials_valid_by_account_info(account_config)
        if credentials:
            try:
                # create drive api client
                service = self._create_service_sheets(credentials)
                spreadsheet = service.spreadsheets().get(spreadsheetId=spreadsheet_id).execute()
                status_trashed = self.get_status_trashed_spreadsheet(spreadsheet_id, credentials)
                # Extract sheet names
                sheets = spreadsheet.get("sheets", [])
                MobioLogging().info("get_sheets_in_spreadsheet :: response :: {}".format(sheets))
                return sheets, data_token, status_trashed

            except HttpError as error:
                MobioLogging().error("get_sheets_in_spreadsheet :: error :: {}".format(error))
        return None, data_token, None
    
    # @lru_redis_cache.add_for_class(expiration=300)
    def get_status_trashed_spreadsheet(self, spreadsheet_id, credentials):
        service_drive = self._create_service_drive(credentials)
        # Kiểm tra trạng thái của tệp dựa trên spreadsheet_id (Google Drive API)
        file_metadata = service_drive.files().get(fileId=spreadsheet_id, fields="trashed").execute()

        status_trashed = False
        # Kiểm tra nếu file nằm trong thùng rác
        if file_metadata.get("trashed"):
            MobioLogging().info("detail_sheets_in_spreadsheet :: spreadsheet is in the trash")
            status_trashed = True
        return status_trashed

    def detail_sheets_in_spreadsheet(self, spreadsheet_id, account_config):
        credentials, data_token = self.get_credentials_valid_by_account_info(account_config)
        if credentials:
            try:
                # create drive api client
                service = self._create_service_sheets(credentials)
                status_trashed = self.get_status_trashed_spreadsheet(spreadsheet_id, credentials)
                spreadsheet = service.spreadsheets().get(spreadsheetId=spreadsheet_id).execute()
                MobioLogging().info("detail_sheets_in_spreadsheet :: response :: {}".format(spreadsheet))
                return spreadsheet, data_token, status_trashed

            except HttpError as error:
                MobioLogging().error("detail_sheets_in_spreadsheet :: error :: {}".format(error))
        return None, data_token, None

    def add_sheets_in_spreadsheet(self, spreadsheet_id, account_config, sheet_name):
        credentials, data_token = self.get_credentials_valid_by_account_info(account_config)
        if credentials:
            try:
                # create drive api client
                service = self._create_service_sheets(credentials)
                body = {"requests": {"addSheet": {"properties": {"title": sheet_name}}}}

                res = service.spreadsheets().batchUpdate(spreadsheetId=spreadsheet_id, body=body).execute()

                MobioLogging().info("add_sheets_in_spreadsheet :: response :: {}".format(res))
                return res, data_token

            except HttpError as error:
                MobioLogging().error("add_sheets_in_spreadsheet :: error :: {}".format(error))
        return None, data_token

    def get_first_row_in_sheet_within_spreadsheet(self, spreadsheet_id, account_config, sheet_name):
        FIRST_ROW_RANGE = "1:1"
        credentials, data_token = self.get_credentials_valid_by_account_info(account_config)
        if credentials:
            try:
                # create drive api client
                service = self._create_service_sheets(credentials)
                range_name = f"{sheet_name}!{FIRST_ROW_RANGE}"

                # Make the API request to get the first row
                result = service.spreadsheets().values().get(spreadsheetId=spreadsheet_id, range=range_name).execute()

                MobioLogging().info("get_first_row_in_sheet_within_spreadsheet :: response :: {}".format(result))
                return result, data_token

            except HttpError as error:
                MobioLogging().error("get_first_row_in_sheet_within_spreadsheet :: error :: {}".format(error))
        return None, data_token

    def add_first_row_to_range_column(self, data, spreadsheet_id, account_config):
        credentials, data_token = self.get_credentials_valid_by_account_info(account_config)
        if credentials:
            try:
                # create drive api client
                service = self._create_service_sheets(credentials)
                # range_name = f"{sheet_name}!{range_column}"
                # body = {"values": first_values}
                body = {"valueInputOption": "RAW", "data": data}
                # Make the API request to get the first row
                result = service.spreadsheets().values().batchUpdate(spreadsheetId=spreadsheet_id, body=body).execute()

                MobioLogging().info("add_first_row_to_range_column :: response :: {}".format(result))
                return result, data_token

            except HttpError as error:
                MobioLogging().error("add_first_row_to_range_column :: error :: {}".format(error))
        return None, data_token
