#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 28/03/2024
"""
from src.common import lru_redis_cache


class CachingHelpers:
    EXPIRATION_DEFAULT = 60 * 60

    @classmethod
    def hash_key(cls, *args):
        return lru_redis_cache.cache_prefix + "#" + "#".join([str(a) for a in args])

    @staticmethod
    def get_value_by_key_not_hash(key):
        try:
            return lru_redis_cache.cache[key]
        except:
            return None

    def get_value_by_key(self, *args):
        has_key = lru_redis_cache.cache_prefix + "#" + self.hash_key(*args)

        try:
            return lru_redis_cache.cache[has_key]
        except:
            return None

    def set_value_by_key_not_hash(self, key, value, expiration=None):
        if not expiration:
            expiration = self.EXPIRATION_DEFAULT
        # Set cache
        lru_redis_cache.cache.set_item(key, value, expiration)
        return value

    def set_value_by_key(self, *args, value, expiration=None):
        if not expiration:
            expiration = self.EXPIRATION_DEFAULT
        has_key = self.hash_key(*args)
        # Set cache
        lru_redis_cache.cache.set_item(has_key, value, expiration)
        return value

    @classmethod
    def delete_cache_by_key(cls, key):
        return lru_redis_cache.delete_cache_by_key(key)
