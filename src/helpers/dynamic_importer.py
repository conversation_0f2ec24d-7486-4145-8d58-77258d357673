#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 06/07/2024
"""

import importlib

from mobio.libs.logging import MobioLogging
from mobio.sdks.base.common.mobio_exception import CustomError


def dynamic_import_and_call(module_path, class_name, func_name, *args, **kwargs):
    """
    Import module, lấy class và gọi một hàm xác định trong class đó một cách động.

    :param module_path: Đường dẫn tới module (dùng dấu chấm, ví dụ: 'validators.data_flow.data_in.database.mysql')
    :param class_name: Tên của class cần lấy trong module
    :param func_name: Tên của hàm cần gọi trong class
    :param args: Cá<PERSON> tham số truyền vào hàm (nếu có)
    :param kwargs: <PERSON><PERSON><PERSON> tham số từ khóa truyền và<PERSON> hàm (nếu có)
    :return: <PERSON><PERSON><PERSON> qu<PERSON> của việc gọi hàm
    """
    try:
        module = importlib.import_module(module_path)
        cls = getattr(module, class_name)
        instance = cls()
        func = getattr(instance, func_name)
        return func(*args, **kwargs)
    except ImportError as e:
        MobioLogging().error(str(e))
        raise CustomError(f"Func not support")
    except AttributeError as e:
        MobioLogging().error(str(e))
        raise CustomError(f"Func not support!")
