#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 09/07/2024
"""

from src.helpers.dynamic_importer import dynamic_import_and_call


class HandlerGeneralParamDefault:

    def __init__(self) -> None:
        self.class_name = "General"

    def get_module_path(self, data_type, source_type, source_key, type_handle="data_flow"):
        return f"src.helpers.general_param_default.{type_handle}.{data_type}.{source_type}.{source_key}"

    def get_func_name(self, action):
        return f"general_{action}"

    def call(self, data_type, source_type, source_key, action, *args, **kwargs):
        module_path = self.get_module_path(data_type, source_type, source_key)
        func_name = self.get_func_name(action)
        result = dynamic_import_and_call(module_path, self.class_name, func_name, *args, **kwargs)
        return result
