#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 26/06/2024
"""


import hashlib
import hmac


class TemplateSettingGeneral:
    def gen_secret_key_by_type_api(self, merchant_id, app_secret_key, connector_identifier):
        secret_key_gen = f"{merchant_id}{app_secret_key}"
        msg = f"{merchant_id}{app_secret_key}{connector_identifier}"
        access_token = hmac.new(secret_key_gen.encode(), msg.encode(), hashlib.sha512).hexdigest()
        return hashlib.md5(access_token.encode()).hexdigest()
