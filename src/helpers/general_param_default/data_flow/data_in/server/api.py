#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 06/07/2024
"""

import hashlib
import json
from urllib.parse import urljoin
from uuid import uuid4

from src.apis import MobioAdminSDK
from src.common.data_flow_constant import (
    ConstantContentType,
    ConstantMethod,
    ConstantParamHeaderDefault,
    ConstantTypeConfigurationGeneral,
)
from src.helpers.general_param_default.data_flow.data_in.server import BaseServer
from src.models.data_flow.config_app_model import ConfigAppField
from src.models.data_flow.general_configuration_parameters_model import (
    GeneralConfigurationParametersModel,
)


class General(BaseServer):

    def _get_url_ingest_by_merchant_id(self, merchant_id):
        return f"https://ingest-test.mobio.vn/market-place/external/api/v1.0/bulk-data/{merchant_id}"

    def _get_query_header_defaults(self, merchant_id, app_detail):
        app_id = str(app_detail.get(ConfigAppField.ID))
        app_secret_key = app_detail.get(ConfigAppField.SECRET_KEY)
        connector_iden = str(uuid4())
        connector_identifier = hashlib.md5(f"{app_id}_{connector_iden}_data_in_server_api".encode()).hexdigest()

        access_token = self.gen_secret_key_by_type_api(merchant_id, app_secret_key, connector_identifier)

        headers = [
            {
                "key": ConstantParamHeaderDefault.X_MERCHANT_ID,
                "value": merchant_id,
                "type": "string",
                "created_by": "system",
            },
            {
                "key": ConstantParamHeaderDefault.MOBIO_CONNECTOR_IDENTIFIER,
                "value": connector_identifier,
                "type": "string",
                "created_by": "system",
            },
            {
                "key": ConstantParamHeaderDefault.MOBIO_ACCESS_TOKEN,
                "value": access_token,
                "type": "string",
                "created_by": "system",
            },
            {
                "key": ConstantParamHeaderDefault.MOBIO_CONNECTOR_APPKEY,
                "value": app_secret_key,
                "type": "string",
                "created_by": "system",
            },
        ]

        param_query = "?" + "&".join(f"{header['key'].lower()}={header['value']}" for header in headers)

        return param_query, headers

    def general_information_config_connect(self, merchant_id, app_detail):
        url_ingest = self._get_url_ingest_by_merchant_id(merchant_id)
        param_query, param_headers = self._get_query_header_defaults(merchant_id, app_detail)

        return {
            "url": url_ingest,
            "methods": [ConstantMethod.POST],
            "param_headers": param_headers,
            "param_query": param_query,
            "content_type": ConstantContentType.JSON,
        }

    def general_get_url_request(self, merchant_id):
        public_host = MobioAdminSDK().request_get_merchant_config_host(merchant_id=merchant_id, key="public-host")
        return urljoin(public_host, "guest/market-place/external/api/v1.0/bulk-data")

    def general_get_integration_guide(self, merchant_id, object_get_sample, object_attribute, connector_detail):
        url_request = self.general_get_url_request(merchant_id)

        config_connect = connector_detail.get("config_connect", {})
        param_headers = config_connect.get("param_headers", [])
        param_query = config_connect.get("param_query", "")

        # Tạo chuỗi headers từ param_headers
        headers = " ".join([f"--header '{item['key']}: {item['value']}'" for item in param_headers])

        data_sample = connector_detail.get("schema_json_upload")
        if not data_sample:
            general_data_sample = GeneralConfigurationParametersModel().find_one(
                {"type": ConstantTypeConfigurationGeneral.DATA_SAMPLE_REQUEST_API}
            )
            key_get_data_sample = (
                "{}_{}".format(object_get_sample, object_attribute) if object_attribute else object_get_sample
            )

            data_sample = general_data_sample.get("data", {}).get(key_get_data_sample)

        body_sample = data_sample
        if isinstance(body_sample, dict):
            if not body_sample.get("data"):
                body_sample = {"data": [data_sample]}
        elif isinstance(body_sample, list):
            body_sample = {"data": data_sample}

        data_sample_curl = json.dumps(body_sample, ensure_ascii=False)

        # Giả sử param_query là một chuỗi đã được định dạng đúng
        curl_example = (
            f"curl -X POST {url_request}{param_query} "
            f"{headers}"
            f" --header 'Content-Type: application/json' --data-raw '{data_sample_curl}'"
        )

        return {
            "curl_example": curl_example,
            "body_example": body_sample,
        }

    def general_get_api_sample_response(self, merchant_id, object_get_sample, object_attribute, connector_detail):
        return {
            "success": [
                {
                    "tab": "",
                    "description": "Thông tin {}".format(object_get_sample),
                    "code": "HTTP 200-OK",
                    "response": {"data": {}},
                }
            ],
            "error": [
                {
                    "tab": "401-Unauthorized",
                    "description": "token/api-key hết hạn hoặc sai định dạng. Yêu cầu login lại.",
                    "code": "HTTP 1.1 401",
                    "response": {
                        "code": 401,
                        "message": "Token is invalid or is expired. Please login again.",
                    },
                }
            ],
        }


if __name__ == "__main__":
    print(General().general_get_url_request("02c4f3ea-b9cd-11ee-a31b-218d8c3ec921"))
