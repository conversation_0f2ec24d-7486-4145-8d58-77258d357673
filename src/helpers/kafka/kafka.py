import os
import random

from confluent_kafka.cimpl import NewTopic
from mobio.libs.kafka_lib import MobioEnvironment
from mobio.libs.kafka_lib.helpers.ensure_kafka_topic import (
    AdminClient,
    EnsureKafkaTopic,
    deepcopy,
)
from mobio.libs.logging import MobioLogging


class KafkaHelper(EnsureKafkaTopic):
    def create_kafka_topic(self, topic):
        all_property = self.get_all_property()
        admin_client = AdminClient({"bootstrap.servers": self.bootstrap_server})
        existing_topics = list(admin_client.list_topics().topics.keys())
        topic_name = topic.get(self.TOPIC_NAME)
        if topic_name in existing_topics:
            MobioLogging().info("Topic existed :: {}".format(topic_name))
            return True
        if not topic.get(self.REPLICATION_FACTOR):
            topic[self.REPLICATION_FACTOR] = int(os.getenv(MobioEnvironment.KAFKA_REPLICATION_FACTOR))
        if not topic.get(self.NUM_PARTITIONS):
            topic[self.NUM_PARTITIONS] = 8
        if not topic.get(self.REPLICATION_ASSIGNMENT) and os.getenv(MobioEnvironment.DEFAULT_BROKER_ID_ASSIGN):
            topic[self.REPLICATION_ASSIGNMENT] = os.getenv(MobioEnvironment.DEFAULT_BROKER_ID_ASSIGN)
        if topic.get(self.REPLICATION_ASSIGNMENT):
            lst_assignment = []
            lst_broker = [int(x.split(":")[0]) for x in topic.get(self.REPLICATION_ASSIGNMENT).split(",")]
            for i in range(topic.get(self.NUM_PARTITIONS)):
                random.shuffle(lst_broker)
                lst_assignment.append(deepcopy(lst_broker)[: topic.get(self.REPLICATION_FACTOR)])
            topic[self.REPLICATION_ASSIGNMENT] = lst_assignment
            topic[self.REPLICATION_FACTOR] = None
        conf = {x: topic.get(x) for x in all_property if topic.get(x)}
        fs = admin_client.create_topics([NewTopic(**conf)], operation_timeout=30)

        # Wait for each operation to finish.
        for topic, f in fs.items():
            try:
                f.result()  # The result itself is None
                return True
            except Exception as e:
                MobioLogging().error("Failed create topic, err: {}".format(str(e)))
                return False
        return True

    def create_kafka_topics(self, lst_topic: list):
        all_property = self.get_all_property()
        new_topics = []
        admin_client = AdminClient({"bootstrap.servers": self.bootstrap_server})
        existing_topics = list(admin_client.list_topics().topics.keys())
        for topic in lst_topic:
            if topic.get(self.TOPIC_NAME) not in existing_topics:
                if not topic.get(self.REPLICATION_FACTOR):
                    topic[self.REPLICATION_FACTOR] = int(os.getenv(MobioEnvironment.KAFKA_REPLICATION_FACTOR))
                if not topic.get(self.NUM_PARTITIONS):
                    topic[self.NUM_PARTITIONS] = 8
                if not topic.get(self.REPLICATION_ASSIGNMENT) and os.getenv(MobioEnvironment.DEFAULT_BROKER_ID_ASSIGN):
                    topic[self.REPLICATION_ASSIGNMENT] = os.getenv(MobioEnvironment.DEFAULT_BROKER_ID_ASSIGN)
                if topic.get(self.REPLICATION_ASSIGNMENT):
                    lst_assignment = []
                    lst_broker = [int(x.split(":")[0]) for x in topic.get(self.REPLICATION_ASSIGNMENT).split(",")]
                    for i in range(topic.get(self.NUM_PARTITIONS)):
                        random.shuffle(lst_broker)
                        lst_assignment.append(deepcopy(lst_broker)[: topic.get(self.REPLICATION_FACTOR)])
                    topic[self.REPLICATION_ASSIGNMENT] = lst_assignment
                    topic[self.REPLICATION_FACTOR] = None
                conf = {x: topic.get(x) for x in all_property if topic.get(x)}
                new_topics.append(NewTopic(**conf))
        if new_topics:
            fs = admin_client.create_topics(new_topics, operation_timeout=30)

            # Wait for each operation to finish.
            for topic, f in fs.items():
                try:
                    f.result()  # The result itself is None
                except Exception as e:
                    MobioLogging().error("Failed create topic, err: {}".format(str(e)))
                    return False
        return True
