"""
    Author: KIEUANH
    Company: MOBIO
    Date Created: 26/07/2022
"""

import json
import time

from confluent_kafka.cimpl import Producer
from mobio.libs.Singleton import Singleton
from mobio.libs.kafka_lib import KAFKA_BOOTSTRAP
from mobio.libs.kafka_lib.helpers.kafka_consumer_manager import BaseKafkaConsumer

from src.models import base_client


class ConfluentKafkaConsumer(BaseKafkaConsumer):

    def __init__(self, topic_name, group_id):
        super().__init__(topic_name=topic_name, group_id=group_id, client_mongo=base_client)

    def process_msg(self, payload):
        pass

    def message_handle(self, data):
        self.process_msg(data)

    def start_consumer(self):
        time.sleep(1000)


@Singleton
class KafkaProducerManager:
    producer = Producer({"request.timeout.ms": 20000, "bootstrap.servers": KAFKA_BOOTSTRAP, "compression.type": "zstd"})

    def flush_message(self, topic: str, key: str, value):
        self.producer.produce(
            topic=topic,
            key=key,
            value=json.dumps(value).encode("utf-8"),
            on_delivery=self.kafka_delivery_report,
        )
        self.producer.poll(0)
        self.producer.flush(1)

    def kafka_delivery_report(self, err, msg):
        """Called once for each message produced to indicate delivery result.
        Triggered by poll() or flush()."""
        if err is not None:
            print("Message delivery failed: {}".format(err))
        else:
            print("message delivery to: {}, {}".format(msg.topic(), msg.partition()))
