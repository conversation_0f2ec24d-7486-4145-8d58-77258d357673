from mobio.libs.logging import MobioLogging

from src.apis.uri import URI_NOTIFY_MANAGEMENT
from src.common import KeyHostService, lru_redis_cache
from src.common.requests_retry import RequestRetryAdapter
from src.internal_module.admin import GetInternalHost


class NotifyManagementHelper(RequestRetryAdapter):
    @staticmethod
    def upsert_provider_config(merchant_id, payload):
        MobioLogging().info("NotifyManagementHelper :: upsert_provider_config :: Request :: %s" % payload)
        token = GetInternalHost.get_token_by_merchant()

        headers = {"Content-Type": "application/json", "Authorization": token, "X-Merchant-Id": merchant_id}

        nm_host = GetInternalHost.get_internal_host_by_merchant(merchant_id, key=KeyHostService.NM_SERVICE_HOST)
        url = URI_NOTIFY_MANAGEMENT.PROVIDER_CONFIG.format(host=nm_host, merchant_id=merchant_id)
        response = RequestRetryAdapter().retry_a_put_request(url, headers=headers, payload=payload)
        MobioLogging().debug("NotifyManagementHelper :: upsert_provider_config :: Response :: %s" % response)
        if response:
            if response.get("code") == 200:
                return True
        return False

    @staticmethod
    def send_test_message(merchant_id, payload):
        MobioLogging().info("NotifyManagementHelper :: send_test_message :: Request :: %s" % payload)
        token = GetInternalHost.get_token_by_merchant()
        headers = {"Content-Type": "application/json", "Authorization": token, "X-Merchant-Id": merchant_id}
        nm_host = GetInternalHost.get_internal_host_by_merchant(merchant_id, key=KeyHostService.NM_SERVICE_HOST)
        if not nm_host:
            return None

        url = URI_NOTIFY_MANAGEMENT.SEND_MESSAGE.format(host=nm_host, channel="sms")
        response = RequestRetryAdapter().retry_a_post_request(url, headers=headers, payload=payload)
        MobioLogging().debug("NotifyManagementHelper :: send_test_message :: Response :: %s" % response)
        return response

    @staticmethod
    def send_email_test(merchant_id, payload):
        MobioLogging().info("NotifyManagementHelper :: send_email_test :: Request :: %s" % payload)
        token = GetInternalHost.get_token_by_merchant()
        headers = {"Content-Type": "application/json", "Authorization": token, "X-Merchant-Id": merchant_id}
        nm_host = GetInternalHost.get_internal_host_by_merchant(merchant_id, key=KeyHostService.NM_SERVICE_HOST)

        url = URI_NOTIFY_MANAGEMENT.SEND_MESSAGE.format(host=nm_host, channel="email")
        response = RequestRetryAdapter().retry_a_post_request(url, headers=headers, payload=payload)
        MobioLogging().debug("NotifyManagementHelper :: send_email_test :: Response :: %s" % response)
        return response

    @staticmethod
    def get_modules_by_domain(merchant_id, domain):
        MobioLogging().info("NotifyManagementHelper :: get_module_by_domain :: Request :: %s" % domain)
        token = GetInternalHost.get_token_by_merchant()
        headers = {"Content-Type": "application/json", "Authorization": token, "X-Merchant-Id": merchant_id}
        params = {"domain": domain}

        nm_host = GetInternalHost.get_internal_host_by_merchant(merchant_id, key=KeyHostService.NM_SERVICE_HOST)
        url = URI_NOTIFY_MANAGEMENT.MODULE_BY_DOMAIN.format(host=nm_host, domain=domain)
        response = RequestRetryAdapter().retry_a_get_request(url, headers=headers, params=params)
        MobioLogging().debug("NotifyManagementHelper :: get_module_by_domain :: Response :: %s" % response)
        return response.get("data")
