#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 01/07/2024
"""

import requests
from mobio.libs.logging import MobioLogging

from src.apis.uri import URI_DATA_OUT
from src.common import KeyHostService
from src.common.data_flow_constant import ConstantStatusCheckConnection
from src.common.requests_retry import RequestRetryAdapter
from src.internal_module.admin import GetInternalHost


class DataOutHelper(RequestRetryAdapter):
    @staticmethod
    def send_data_test_data_out(merchant_id, body):
        from src.helpers.data_flow.check_connect_to_config_connect import (
            ConstantHttpStatusCode,
        )

        token = GetInternalHost.get_token_by_merchant()
        headers = {"Content-Type": "application/json", "Authorization": token, "X-Merchant-Id": merchant_id}
        data_out_host = GetInternalHost.get_internal_host_by_merchant(
            merchant_id,
            key=KeyHostService.DATA_OUT_SERVICE_HOST,  # key host muốn l<PERSON>y gi<PERSON> trị
        )
        if not data_out_host:
            return None
        url = URI_DATA_OUT.SEND_DATA_TEST
        url = url.format(host=data_out_host)
        errors = []
        status_code = "Không xác định"
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                url, headers=headers, payload=body, is_build_json=False
            )
            MobioLogging().info("DataOutHelper :: send_data_test_data_out:Response: %s" % response)
            if not response:
                status_code = 400
                errors.append(f"Đã có lỗi xảy ra!")
            else:
                status_code = ConstantHttpStatusCode.OK
                status_connect = ConstantStatusCheckConnection.SUCCESS
        except ConnectionError as ce:
            MobioLogging().error(f"ConnectionError: {ce}")
            errors.append(f"Không thể kết nối tới máy chủ. Chi tiết lỗi :: {ce}")
        except requests.Timeout as te:
            MobioLogging().error(f"TimeoutError: {te}")
            errors.append(f"Yêu cầu đã bị vượt quá thời gian chờ. Chi tiết lỗi :: {te}")
        except requests.HTTPError as he:
            status_code = he.response.status_code
            MobioLogging().error(f"HTTPError: {he.response.status_code}")
            errors.append(f"Đã xảy ra lỗi HTTP: {he.response.status_code}. Chi tiết lỗi :: {he.response}")
        except requests.RequestException as re:
            MobioLogging().error(f"RequestException: {re}")
            errors.append(f"Đã xảy ra lỗi trong quá trình yêu cầu. Chi tiết lỗi :: {re}")
        except Exception as e:
            MobioLogging().error(f"Error: {str(e)}")
            errors.append(f"Lỗi kết nối. Chi tiết lỗi :: {str(e)}")
        connect_information = {
            "name": "Mã: {status_code}".format(
                status_code=status_code if status_code != ConstantHttpStatusCode.OK else "200"
            ),
            "status": ConstantStatusCheckConnection.SUCCESS,
        }
        if status_code != ConstantHttpStatusCode.OK:
            status_connect = ConstantStatusCheckConnection.FAIL
            connect_information.update({"status": ConstantStatusCheckConnection.FAIL, "errors": errors})
        return status_connect, [connect_information]

    @staticmethod
    def change_config_data_out(merchant_id, body):
        token = GetInternalHost.get_token_by_merchant()
        headers = {"Content-Type": "application/json", "Authorization": token, "X-Merchant-Id": merchant_id}
        data_out_host = GetInternalHost.get_internal_host_by_merchant(
            merchant_id,
            key=KeyHostService.DATA_OUT_SERVICE_HOST,  # key host muốn lấy giá trị
        )
        if not data_out_host:
            return None
        # data_out_host = "https://api-test1.mobio.vn/"
        url = URI_DATA_OUT.CHANGE_CONFIG.format(host=data_out_host)
        response = RequestRetryAdapter().retry_a_post_request(url, headers=headers, payload=body)
        MobioLogging().info("DataOutHelper :: change_config_data_out:Response: %s" % response)
        if response:
            return True
        return None


if __name__ == "__main__":
    DataOutHelper.send_data_test_data_out("mobio", {})
