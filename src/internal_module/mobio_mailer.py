import select

from mobio.libs.logging import MobioLogging
from mobio.sdks.base.common.mobio_exception import CustomError

from src.apis.uri import URI_MOBIO_MAILER
from src.common import KeyHostService, lru_redis_cache
from src.common.requests_retry import RequestRetryAdapter
from src.internal_module.admin import GetInternalHost


class MobioMailerHelper(RequestRetryAdapter):
    def send_request_create_domain(self, host, token, merchant_id, payload):
        MobioLogging().info("MobioMailerHelper :: send_request_create_domain:: host :: %s :: token :: %s :: merchant_id :: %s :: payload :: %s" % (host, token, merchant_id, payload))
        headers = {"Content-Type": "application/json", "Authorization": token, "X-Merchant-Id": merchant_id}
        url = URI_MOBIO_MAILER.DOMAIN_VERIFY.format(host=host)
        response = self.retry_a_post_request(url, headers=headers, payload=payload)
        MobioLogging().debug("MobioMailerHelper :: send_request_create_domain :: Response :: %s" % response)
        code = response.get("code")
        if code != 200:
            raise CustomError(response.get("message"), code)
        return response.get("data")

    def send_request_check_status_domain(self, host, token, merchant_id, payload):
        MobioLogging().info("MobioMailerHelper :: send_request_check_status_domain:: host :: %s :: token :: %s :: merchant_id :: %s :: payload :: %s" % (host, token, merchant_id, payload))
        headers = {"Content-Type": "application/json", "Authorization": token, "X-Merchant-Id": merchant_id}

        url = URI_MOBIO_MAILER.CHECK_STATUS.format(host=host)
        response = self.retry_a_post_request(url, headers=headers, payload=payload)
        MobioLogging().debug("MobioMailerHelper :: send_request_check_status_domain :: Response :: %s" % response)
        if response:
            return response.get("data")
        return None

    def send_request_delete_domain(self, host, token, merchant_id, payload):
        MobioLogging().info("MobioMailerHelper :: send_request_delete_domain:: host :: %s :: token :: %s :: merchant_id :: %s :: payload :: %s" % (host, token, merchant_id, payload))
        headers = {"Content-Type": "application/json", "Authorization": token, "X-Merchant-Id": merchant_id}
        url = URI_MOBIO_MAILER.DELETE_DOMAIN_CONFIG.format(host=host)
        response = self.retry_a_delete_request(url, headers=headers, payload=payload)
        MobioLogging().debug("MobioMailerHelper :: send_request_delete_domain :: Response :: %s" % response)
        if response:
            return response.get("data")
        return None
