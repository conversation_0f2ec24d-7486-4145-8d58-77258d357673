from mobio.libs.logging import MobioLogging

from src.apis.uri import URI_NM, URI_WF
from src.common import KeyHostService
from src.common.requests_retry import RequestRetryAdapter
from src.internal_module.admin import GetInternalHost


class WorkflowHelper(RequestRetryAdapter):

    @staticmethod
    def get_wf_by_domain(merchant_id, domain):
        MobioLogging().info("WorkflowHelper :: get_wf_by_domain :: Request: %s" % domain)
        headers = {
            "Content-Type": "application/json",
            "Authorization": GetInternalHost.get_token_by_merchant(),
            "X-Merchant-Id": merchant_id,
        }

        params = {"email_domain": domain}
        wf_service_host = GetInternalHost.get_internal_host_by_merchant(
            merchant_id=merchant_id,
            key=KeyHostService.WF_SERVICE_HOST,  # key host muốn lấy giá trị
        )
        url = URI_WF.GET_DOMAIN
        url = url.format(host=wf_service_host)
        response = RequestRetryAdapter().retry_a_get_request(url, headers=headers, params=params)
        MobioLogging().info("WorkflowHelper :: get_wf_by_domain :: Response: %s" % response)
        if response:
            MobioLogging().info("WorkflowHelper :: get_wf_by_domain :: Response: %s" % response)
            data = response.get("data")
            return data
        return None
