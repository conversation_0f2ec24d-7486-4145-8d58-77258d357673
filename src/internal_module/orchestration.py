#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 02/04/2024
"""
from mobio.libs.logging import MobioLogging

from src.apis.uri import URI_ORCHESTRATION
from src.common import KeyHostService
from src.common.requests_retry import RequestRetryAdapter
from src.internal_module.admin import GetInternalHost


class OrchestrationApiHelper(RequestRetryAdapter):

    def create_pipeline(self, merchant_id, data):
        token = GetInternalHost.get_token_by_merchant()
        headers = {"Content-Type": "application/json", "Authorization": token, "X-Merchant-Id": merchant_id}
        orchestration_host = GetInternalHost.get_internal_host_by_merchant(
            merchant_id,
            key=KeyHostService.ORCHESTRATION_SERVICE_HOST,  # key host muốn lấy giá trị
        )
        if not orchestration_host:
            return None
        url = URI_ORCHESTRATION.CREATE_PIPELINE
        url = url.format(host=orchestration_host)
        response = self.retry_a_post_request(url, headers=headers, payload=data)
        MobioLogging().info("OrchestrationApiHelper :: create_pipeline:Response: %s" % response)
        if response:
            MobioLogging().info("OrchestrationApiHelper :: create_pipeline:Response: %s" % response)
            data = response.get("data")
            return data

        return None

    def update_pipeline(self, merchant_id, data, orchestration_id):
        token = GetInternalHost.get_token_by_merchant()
        headers = {"Authorization": token, "X-Merchant-Id": merchant_id}
        orchestration_host = GetInternalHost.get_internal_host_by_merchant(
            merchant_id,
            key=KeyHostService.ORCHESTRATION_SERVICE_HOST,  # key host muốn lấy giá trị
        )
        if not orchestration_host:
            return None
        url = URI_ORCHESTRATION.DETAIL_PIPELINE
        url = url.format(host=orchestration_host, orchestration_id=orchestration_id)
        response = self.retry_a_put_request(url, headers=headers, payload=data)
        MobioLogging().info("OrchestrationApiHelper :: update_pipeline:Response: %s" % response)
        if response:
            MobioLogging().info("OrchestrationApiHelper :: update_pipeline:Response: %s" % response)
            data = response.get("data")
            return data

        return None

    def start_pipeline(self, merchant_id, pipeline_id):
        token = GetInternalHost.get_token_by_merchant()
        headers = {"Authorization": token, "X-Merchant-Id": merchant_id}
        orchestration_host = GetInternalHost.get_internal_host_by_merchant(
            merchant_id,
            key=KeyHostService.ORCHESTRATION_SERVICE_HOST,  # key host muốn lấy giá trị
        )
        if not orchestration_host:
            return None
        url = URI_ORCHESTRATION.START_PIPELINE
        url = url.format(host=orchestration_host, pipeline_id=pipeline_id)
        response = self.retry_a_put_request(url, headers=headers, payload={})
        MobioLogging().info("OrchestrationApiHelper :: start_pipeline:Response: %s" % response)
        if response:
            MobioLogging().info("OrchestrationApiHelper :: start_pipeline:Response: %s" % response)
            data = response.get("data")
            return data

        return None

    def stop_pipeline(self, merchant_id, pipeline_id):
        token = GetInternalHost.get_token_by_merchant()
        headers = {"Authorization": token, "X-Merchant-Id": merchant_id}
        orchestration_host = GetInternalHost.get_internal_host_by_merchant(
            merchant_id,
            key=KeyHostService.ORCHESTRATION_SERVICE_HOST,  # key host muốn lấy giá trị
        )
        if not orchestration_host:
            return None
        url = URI_ORCHESTRATION.STOP_PIPELINE
        url = url.format(host=orchestration_host, pipeline_id=pipeline_id)
        response = self.retry_a_put_request(url, headers=headers, payload={})
        MobioLogging().info("OrchestrationApiHelper :: stop_pipeline:Response: %s" % response)
        if response:
            MobioLogging().info("OrchestrationApiHelper :: stop_pipeline:Response: %s" % response)
            data = response.get("data")
            return data

        return None

    def delete_pipeline(self, merchant_id, orchestration_id):
        token = GetInternalHost.get_token_by_merchant()
        headers = {"Authorization": token, "X-Merchant-Id": merchant_id}
        orchestration_host = GetInternalHost.get_internal_host_by_merchant(
            merchant_id,
            key=KeyHostService.ORCHESTRATION_SERVICE_HOST,  # key host muốn lấy giá trị
        )
        if not orchestration_host:
            return None
        url = URI_ORCHESTRATION.DELETE_PIPELINE
        url = url.format(host=orchestration_host, orchestration_id=orchestration_id)
        response = self.retry_a_delete_request(url, headers=headers)
        MobioLogging().info("OrchestrationApiHelper :: delete_pipeline:Response: %s" % response)
        if response:
            MobioLogging().info("OrchestrationApiHelper :: delete_pipeline:Response: %s" % response)
            return response

        return None
