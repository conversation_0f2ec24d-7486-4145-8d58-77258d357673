#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 08/05/2024
"""


from mobio.libs.logging import MobioLogging

from src.apis.uri import URI_NM
from src.common import KeyHostService
from src.common.requests_retry import RequestRetryAdapter
from src.internal_module.admin import GetInternalHost


class NMHelper(RequestRetryAdapter):

    @staticmethod
    def update_config_push_firebase_nm(merchant_id, config_firebase, domain, status):
        # token = GetInternalHost.get_token_by_merchant()
        headers = {
            "Content-Type": "application/json",
            "Authorization": GetInternalHost.get_token_by_merchant(),
            "X-Merchant-Id": merchant_id,
        }
        payload = {
            "auth_name": "FIREBASE",
            "auth_pass": config_firebase.get("api_key"),  # apikey
            "provider_type": 700,
            "auth_attachment": domain,  # website
            "provider_api": "",
            "others": {
                "SDK_CONFIG": {
                    "apiKey": config_firebase.get("api_key"),
                    "authDomain": domain,
                    "projectId": config_firebase.get("project_id"),
                    "storageBucket": config_firebase.get("storage_bucket"),
                    "messagingSenderId": config_firebase.get("messagingsender_id"),
                    "appId": config_firebase.get("app_id"),
                    "measurementId": config_firebase.get("measurement_id"),
                }
            },
            "status": status,
        }
        nm_service_host = GetInternalHost.get_internal_host_by_merchant(
            merchant_id=merchant_id,
            key=KeyHostService.NM_SERVICE_HOST,  # key host muốn lấy giá trị
        )
        if not nm_service_host:
            return None
        url = URI_NM.UPDATE_CONFIG_FIREBASE
        url = url.format(host=nm_service_host, merchant_id=merchant_id)
        response = RequestRetryAdapter().retry_a_put_request(url, headers=headers, payload=payload)
        MobioLogging().info("NMHelper :: update_config_push_firebase_nm:payload: %s" % payload)
        MobioLogging().info("NMHelper :: update_config_push_firebase_nm:Response: %s" % response)
        if response:
            MobioLogging().info("NMHelper :: update_config_push_firebase_nm:Response: %s" % response)
            data = response.get("data")
            return data

        return None
