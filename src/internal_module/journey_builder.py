from mobio.libs.logging import MobioLogging

from src.apis.uri import URI_JOURNEY_BUILDER
from src.common import KeyHostService
from src.common.requests_retry import RequestRetryAdapter
from src.internal_module.admin import GetInternalHost


class JourneyBuilderHelper(RequestRetryAdapter):
    def get_campaigns(self, merchant_id, payload, page=-1, per_page=15):
        token = GetInternalHost.get_token_by_merchant()

        params = {"page": page, "per_page": per_page}
        headers = {"Content-Type": "application/json", "Authorization": token, "X-Merchant-Id": merchant_id}

        jb_host = GetInternalHost.get_internal_host_by_merchant(
            merchant_id, key=KeyHostService.JOURNEY_BUILDER_SERVICE_HOST
        )
        url = URI_JOURNEY_BUILDER.JOURNEYS.format(host=jb_host)
        response = self.retry_a_post_request(url, headers=headers, payload=payload, params=params)
        MobioLogging().debug("JourneyBuilderHelper :: get_campaigns :: Response :: %s" % response)
        if response:
            return response.get("data")
        return None
