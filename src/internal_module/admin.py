#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: tungdd
Company: MobioVN
Date created: 11/01/2022
"""
import os

from mobio.libs.logging import MobioLogging
from mobio.sdks.admin import MobioAdminSDK

from configs import MarketPlaceApplicationConfig
from src.apis.uri import URI_ADMIN
from src.common import KeyHostService
from src.common.requests_retry import RequestRetryAdapter


class GetInternalHost:

    @staticmethod
    def get_internal_host_by_merchant(merchant_id, key):
        if os.environ.get("VM") == "local":
            return "http://localhost:8081/"
        internal_host = MobioAdminSDK().request_get_merchant_config_host(merchant_id=merchant_id, key=key)
        MobioLogging().info("get_internal_host_by_merchant :: get host :: {}, {}".format(key, internal_host))
        return internal_host

    @staticmethod
    def get_token_by_merchant():
        return "Basic {}".format(MarketPlaceApplicationConfig.MOBIO_TOKEN)

    @staticmethod
    def get_detail_user_information_by_account_id(merchant_id, account_id):
        token = GetInternalHost.get_token_by_merchant()
        headers = {"Content-Type": "application/json", "Authorization": token}
        params = {"account_id": account_id}
        admin_host = GetInternalHost.get_internal_host_by_merchant(merchant_id, key=KeyHostService.ADMIN_SERVICE_HOST)
        url = URI_ADMIN.ACCOUNT_DETAIL.format(host=admin_host)
        response = RequestRetryAdapter().retry_a_get_request(url, headers=headers, params=params)
        MobioLogging().debug("GetInternalHost :: get_detail_user_information_by_account_id:Response: %s" % response)
        if response:
            return response.get("data")
        return {}


class AdminInternal:
    @staticmethod
    def get_detail_user_information_by_account_id(merchant_id, account_id):
        token = GetInternalHost.get_token_by_merchant()
        headers = {"Content-Type": "application/json", "Authorization": token}
        params = {"account_id": account_id}
        admin_host = GetInternalHost.get_internal_host_by_merchant(merchant_id, key=KeyHostService.ADMIN_SERVICE_HOST)
        # admin_host = "https://api-test1.mobio.vn/"
        url = URI_ADMIN.ACCOUNT_DETAIL.format(host=admin_host)
        response = RequestRetryAdapter().retry_a_get_request(url, headers=headers, params=params)
        MobioLogging().debug("GetInternalHost :: get_detail_user_information_by_account_id:Response: %s" % response)
        if response:
            return response.get("data")
        return {}

    def get_merchant_id_by_merchant_code(self, merchant_code):
        headers = {
            "Content-Type": "application/json",
            "Authorization": GetInternalHost.get_token_by_merchant(),
        }
        payload = {"codes": merchant_code}
        admin_host = GetInternalHost.get_internal_host_by_merchant("mobio", "admin-app-api-service-host")
        if not admin_host:
            return None
        url = URI_ADMIN.GET_MERCHANT_ID_BY_MERCHANT_CODE
        url = url.format(host=admin_host)
        response = RequestRetryAdapter().retry_a_get_request(url, headers=headers, params=payload)
        MobioLogging().info("InternalAdminHelper:: get_merchant_id_by_merchant_code :: Response: %s" % response)
        if response:
            data_response = response.get("data")
            for data in data_response:
                if data.get("code") == merchant_code:
                    return data.get("id")

        return None

    def get_merchant_type_by_merchant_id(self, merchant_id):
        token = GetInternalHost.get_token_by_merchant()
        admin_host = GetInternalHost.get_internal_host_by_merchant(merchant_id, key=KeyHostService.ADMIN_SERVICE_HOST)
        url_merchant_detail = URI_ADMIN.MERCHANT_DETAIL.format(host=admin_host, merchant_id=merchant_id)
        headers = {"Authorization": token, "X-Merchant-ID": merchant_id}

        response = RequestRetryAdapter().retry_a_get_request(
            url=url_merchant_detail, headers=headers, params={}, timeout=5
        )
        merchant_detail = {}
        if response:
            merchant_detail = response.get("data")
        if merchant_detail:
            merchant_detail_data = merchant_detail.get("merchant")
            if merchant_detail_data:
                merchant_type = merchant_detail_data.get("type")
                if merchant_type:
                    lst_merchant_type = merchant_type.split(";")
                    if "BANK" in lst_merchant_type:
                        return ["BANK"]
        return ["NON_BANK"]


if __name__ == "__main__":
    print(AdminInternal().get_merchant_type_by_merchant_id("1b99bdcf-d582-4f49-9715-1b61dfff3924"))
