from mobio.libs.logging import Mo<PERSON>Logging

from src.apis.uri import URI_DIGIENTY
from src.common import KeyHostService, lru_redis_cache
from src.common.requests_retry import RequestRetryAdapter
from src.internal_module.admin import GetInternalHost



class DigientyHelper(RequestRetryAdapter):
    @staticmethod
    @lru_redis_cache.add_for_class(expiration=60)
    def register_tracking_landing_page(merchant_id, website_url, token):
        # token = GetInternalHost.get_token_by_merchant()
        headers = {"Content-Type": "application/json", "Authorization": token, "X-Merchant-Id": merchant_id}
        payload = {"source": "mobio", "domain": website_url, "status": 1}
        digienty_host = GetInternalHost.get_internal_host_by_merchant(
            merchant_id,
            key=KeyHostService.DIFIENTY_SERVICE_HOST,  # key host muốn l<PERSON>y giá trị
        )
        if not digienty_host:
            return None
        url = URI_DIGIENTY.REGISTER_TRACKING_LANDING_PAGE
        url = url.format(host=digienty_host)
        response = RequestRetryAdapter().retry_a_post_request(url, headers=headers, payload=payload)
        MobioLogging().info("DigientyHelper :: register_tracking_landing_page:Response: %s" % response)
        if response:
            MobioLogging().info("DigientyHelper :: register_tracking_landing_page:Response: %s" % response)
            data = response.get("data")
            return data

        return None

    @staticmethod
    def update_tracking_status(merchant_id, status, token, code):
        # token = GetInternalHost.get_token_by_merchant()
        headers = {"Content-Type": "application/json", "Authorization": token, "X-Merchant-Id": merchant_id}
        payload = {"status": status}
        digienty_host = GetInternalHost.get_internal_host_by_merchant(
            merchant_id,
            key=KeyHostService.DIFIENTY_SERVICE_HOST,  # key host muốn lấy giá trị
        )
        if not digienty_host:
            return None
        url = URI_DIGIENTY.UPDATE_TRACKING_STATUS.replace("code", code)
        url = url.format(host=digienty_host)
        response = RequestRetryAdapter().retry_a_put_request(url, headers=headers, payload=payload)
        MobioLogging().info("DigientyHelper :: update_tracking_status:Response: %s" % response)
        if response:
            MobioLogging().info("DigientyHelper :: update_tracking_status:Response: %s" % response)
            data = response.get("data")
            return data

        return None
