class URI:
    class FILE:
        FILE = "/file"

    class MARKETPLACE:
        MARKETPLACE = "/market-place"
        UPDATE_STATUS = "/market-place/update-status"
        ACTION_HISTORY = "/market-place/action-history/<config_id>"
        LIST_CONFIG = "/market-place/get-list-config"
        DETAIL = "/market-place/<config_id>"
        SDK_SCRIPT = "/market-place/sdk-script"
        CONNECT_CONFIG = "/market-place/connect-config"
        LIST_CONFIG_BY_IDS = "/market-place/get-list-config-by-ids"

    class SMS_CONFIG:
        SMS_CONFIG = "/sms-configs"
        DETAIL = "/sms-configs/<sms_config_id>"
        CHANGE_STATUS = "/sms-configs/<sms_config_id>/actions/change-status"
        LOGS = "/sms-configs/<sms_config_id>/actions/logs"
        DECODE = "/sms-configs/<sms_config_id>/actions/decode"
        SYNC_DATA = "/sms-configs/actions/sync-data"
        SEND_SMS = "/sms-configs/<sms_config_id>/actions/send-sms"
        RESPONSE_SEND_SMS = "/webhook/sms-configs/response-send-sms"

    class EMAIL_CONFIG:
        EMAIL_CONFIG = "/email-configs"
        WF_BY_DOMAIN = "/email-configs/workflows-by-domain"
        DETAIL = "/email-configs/<email_config_id>"
        CHANGE_STATUS = "/email-configs/<email_config_id>/actions/change-status"
        LOGS = "/email-configs/<email_config_id>/actions/logs"
        DECODE = "/email-configs/<email_config_id>/actions/decode"
        SYNC_DATA = "/email-configs/actions/sync-data"
        SEND_EMAIL = "/email-configs/<email_config_id>/actions/send-email"
        EXPORT_RECORDS = "/email-configs/<email_config_id>/actions/export-records"
        EMAIL = "/email-configs/<email_config_id>/emails"
        EMAIL_DETAIL = "/email-configs/<email_config_id>/emails/<email>"
        RESPONSE_SEND_SMS = "/webhook/email-configs/response-send-email"
        LIST_REGION_AWS = "/email-configs/aws-regions"
        DOMAIN_CHANGE_STATUS = "/email-configs/<email_config_id>/domain-change-status"
        DOMAIN_CONFIG = "/email-configs/<email_config_id>/domain-config"
        CONFIG_SHOW_ACCOUNT_AWS = "/email-configs/config-show-aws"
        LIST_DOMAIN_BY_MERCHANT = "/email-configs/domain-by-merchant"

    class PROVIDER:
        PROVIDER = "/providers"
        DETAIL = "/providers/<provider_id>"
        LOGS = "/providers/configs/<provider_config_id>/actions/logs"

    class WEBPUSH:
        WEBPUSH_INFORMATION_BY_TRACKING_CODE = "/webpush/tracking-code/<tracking_code>/information"
        WEBPUSH_CONFIG_BY_TRACKING_CODE = "/webpush/tracking-code/<tracking_code>/config"
        WEBPUSH_UPSERT_LANDING_PAGE = "/webpush/landing-pages/actions/upsert"

    class DATA_FLOW:
        SOURCE_TYPES = "/data-flow/source-types"  # Lấy danh sách loại nguồn dữ liệu.
        SOURCES = "/data-flow/sources"  # Lấy danh sách nguồn dữ liệu
        WHITELIST_IPS = "/data-flow/whitelist/ips"  # Lấy danh sác IP white list
        OBJECTS_HANDLED = "/data-flow/objects-handled"  # Lấy danh sách đối tượng dược xử lý

        CREATE_CONNECTORS = "/data-flow/connectors"  # Khởi tạo connectors
        LIST_CONNECTORS = "/data-flow/connectors"  # Lấy dah sách connectors
        DELETE_CONNECTORS = "/data-flow/connectors"
        CHECK_CONNECTION_DATABASE = "/data-flow/databases/check-connection"  # Kiểm tra kết nối tới database
        CHECK_CONNECTION = "/data-flow/check-connection"  # Kiểm tra kết nối tới với connection
        GET_LIST_TABLES_DATABASE = (
            "/data-flow/connections/<connector_id>/databases/tables"  # Lấy danh sách table của database
        )
        GET_DATA_SAMPLE_TABLES_DATABASE = (
            "/data-flow/connections/<connector_id>/databases/tables/data/actions/get-sample"  # Lấy dữ liệu mẫu của bảng
        )
        GET_SCHEMA_OF_TABLES_DATABASE = (
            "/data-flow/connections/<connector_id>/databases/tables/schemas"  # Lấy thông tin schemas của bảng
        )

        UPDATE_CONNECTOR = "/data-flow/connections/<connector_id>"  # Cập nhật thông tin connector
        DETAIL_CONNECTOR = "/data-flow/connections/<connector_id>"  # Detail connector

        # danh sách API cấu hình tham số chung
        UPSERT_INFORMATION_OBJECT_HANDED = "/object-handled/action/upsert"

        # Đồng bộ dữ liệu
        CONNECTOR_ACTION_SYNC_DATA = "/data-flow/connections/<connector_id>/actions/sync"

        # Cập nhật trạng thái đồng bộ dữ liệu
        UPDATE_STATUS_SYNC_DATA = "/data-flow/connections/<connector_id>/update/status-sync-data"

        # Lấy danh sách state của connector
        GET_STATE_CONNECTOR = "/connections/<connector_id>/state"

        # Lấy danh sách nguồn dữ liệu đã cấu hình connector
        SOURCES_CONNECTIONS = "/data-flow/sources-connections"

        # Lấy danh sách connector được cấu hình nguồn dữ liệu
        GET_CONNECTOR_SOURCES_CONNECTIONS = "/data-flow/sources-connections/<source_key>/connectors"

        # Cập nhật trạng thái kết nối của connector
        UPDATE_STATUS_CONNECT = "/data-flow/connections/<connector_id>/status-connect"
        MODIFIED_CONTENT_CONNECTOR = "/data-flow/connections/<connector_id>/modified/content"

        # Lấy danh sách đối tượng mapping dựa theo đối tượng chính
        GET_RELATED_OBJECT = "data-flow/<object_primary_key>/related-objects"

        # Giải mã thông tin cấu hình kết nối
        DECRYPT_CONNECTOR_CONFIG = "/data-flow/connections/<connector_id>/decrypt-connection-config"

        # Send data test event data out
        DATA_FLOW_SEND_DATA_TEST_EVENT_DATA_OUT = "/data-flow/connectors/<connector_id>/send-event/test"
        DATA_FLOW_GET_URL_REQUEST_CONNECTOR = "/data-flow/url-request/connector"
        DATA_FLOW_INTEGRATION_GUIDE = "/data-flow/<connector_id>/integration-guide"
        DATA_FLOW_API_SAMPLE_RESPONSE = "/data-flow/<connector_id>/sample-response"

        DATA_FLOW_GET_DETAIL_CONNECTORS_BY_IDS = "/data-flow/actions/connector-detail-by-ids"

        UPSERT_SETTING_ADD_ON_CONNECTOR = "/connectors/setting-add-on/actions/upsert"

        GET_LIST_TABLES_VIEWS_DATABASE = (
            "/data-flow/connections/<connector_id>/databases/tables-views"  # Lấy danh sách table va view của database"
        )
        TOTAL_CONNECTORS_SOURCE = "/data-flow/connectors/total-source"

        class REPORTS:
            class TRIGGER:
                TRIGGER_AGGREGATE_REPORT = "/connectors/triggers/aggregate-report"
                TRIGGER_SEND_NOTI = "/connectors/triggers/send-noti"

            class SNAPSHOT:
                # Docs: https://dev.mobio.vn/docs/market_place_report/#api-[DataFlow]Snapshot-SnapshotDetailSession
                DETAIL_SESSIONS = "/connectors/<connector_id>/snapshot/sessions/<session_id>"

                # Docs: https://dev.mobio.vn/docs/market_place_report/#api-[DataFlow]Snapshot-SnapshotListSession
                LIST_SESSIONS = "/connectors/<connector_id>/snapshot/sessions"

                # Docs: https://dev.mobio.vn/docs/market_place_report/#api-[DataFlow]Snapshot-SnapshotTotalSession
                TOTAL_SESSIONS = "/connectors/<connector_id>/snapshot/total-sessions"

                # Docs: https://dev.mobio.vn/docs/market_place_report/#api-[DataFlow]Snapshot-SnapshotResultRunningSession
                RESULT_RUNNING_SESSIONS = "/connectors/<connector_id>/snapshot/running-sessions"

                # Docs: https://dev.mobio.vn/docs/market_place_report/#api-[DataFlow]Snapshot-SnapshotTotalSessionByStatusEveryDay
                RESULT_TOTAL_SESSIONS_BY_STATUS_EVERYDAY = "/connectors/<connector_id>/snapshot/total-sessions/by-date"

            class STREAMING:
                # Docs: https://dev.mobio.vn/docs/market_place_report/#api-[DataFlow]Streaming-StreamingHistoryResultByObject
                HISTORY_BY_OBJECT = "/connectors/<connector_id>/streaming/history/object"

                # Docs: https://dev.mobio.vn/docs/market_place_report/#api-[DataFlow]Streaming-StreamingHistoryResultByLine
                TOTAL_HISTORY_BY_PIPELINE = "/connectors/<connector_id>/streaming/history/pipeline/total"

                # Docs: https://dev.mobio.vn/docs/market_place_report/#api-[DataFlow]Streaming-StreamingRealTimeResult
                RESULT_REALTIME = "/connectors/<connector_id>/streaming/real-time/result"

                # Docs: https://dev.mobio.vn/docs/market_place_report/#api-[DataFlow]Streaming-StreamingRealTimeResultByStatus
                RESULT_REALTIME_BY_STATUS = "/connectors/<connector_id>/streaming/real-time/result-by-status"

                # Docs: https://dev.mobio.vn/docs/market_place_report/#api-[DataFlow]Streaming-StreamingRealTimeResultByObject
                RESULT_REALTIME_BY_OBJECT = "/connectors/<connector_id>/streaming/real-time/result-by-object"

                # Docs: https://dev.mobio.vn/docs/market_place_report/#api-[DataFlow]Streaming-StreamingHistoryListPipeLine
                LIST_PIPELINE_HISTORY = "/connectors/<connector_id>/streaming/history/pipeline/list"

                # Docs: https://dev.mobio.vn/docs/market_place_report/#api-DataFlow-Streaming-StreamingRealTimeResultByObjectEvent
                RESULT_REALTIME_BY_OBJECT_EVENT = (
                    "/connectors/<connector_id>/streaming/real-time/result-by-object-event"
                )

                # Docs: https://dev.mobio.vn/docs/market_place_report/#api-DataFlow-Streaming-StreamingHistoryResultByObjectEvent
                RESULT_HISTORY_BY_OBJECT_EVENT = "/connectors/<connector_id>/streaming/history/result-by-object-event"

                # Docs: https://dev.mobio.vn/docs/market_place_report/#api-DataFlow-Streaming-StreamingHistoryResultFromSource
                RESULT_HISTORY_FROM_SOURCE = "/connectors/<connector_id>/streaming/history/result-from-source"

            class DATA_OUT:
                GET_LIST_AREA_CODE = "/connectors/destinations/area-codes"
                DETAIL_REQUEST_DATA_OUT = "/connectors/destinations/<source_key>/details/<process_data_out_id>"
                LIST_REQUEST_DATA_OUT = "/connectors/destinations/<source_key>/details"
                OVERVIEW_TOTALS = "/connectors/destinations/<source_key>/overview/totals"
                OVERVIEW_REQUEST_BY_DAY = "/connectors/destinations/<source_key>/overview/request-exports-by-day"
                OVERVIEW_EXPORT_BY_ACCOUNT = (
                    "/connectors/destinations/<source_key>/overview/frequency/export-by-account"
                )

    class PROVIDER:
        PROVIDER = "/providers"
        DETAIL = "/providers/<provider_id>"
        LOGS = "/providers/configs/<provider_config_id>/actions/logs"

    class APP:
        CREATE_NEW_APP = "/apps"  # Tạo app
        LIST_APPS = "/apps"  # Danh sách app
        UPDATE_APP = "/apps/<app_id>"  # Sửa app
        APP_DETAIL = "/apps/<app_id>"  # Chi tiết app
        DELETE_APP = "/apps/<app_id>"  # Xoá app
        RENEW_SECRET = "/apps/<app_id>/actions/renew-secret"  # Tạo secret key mới
        SEEN_APP = "/apps/<app_id>/actions/seen"  # Xác nhận đã có user click vào xem detail của app
        GET_LIST_CONNECTOR_USED_APP = (
            "/apps/<app_id>/actions/list-connector-in-used"  # Danh sách connector đang sử dụng app
        )

        LIST_EVENT_APP = "/apps/destination/events"  # Danh sách event data-out
        UPDATE_EVENT_STATUS = "/apps/destination/events/<event_key>/update/event-status"  # Cập nhật trạng thái event

        HEADER_DEFAULT = "/apps/<app_id>/header-default"
        PARAM_DEFAULT_CONFIG_CONNECT = "/apps/<app_id>/param-default/config-connect"

    class INTEGRATION_ACCOUNTS:
        GOOGLE_CALLBACK = "/google/authorized-callback"

        LIST = "/integration-accounts"  # Lấy danh sách tài khoản
        GET_INFORMATION_CONNECT_APP = (
            "/integration-accounts/information/connect-app"  # Lấy thông tin kết nối APP Google Sheet
        )
        DELETE = "/integration-accounts/<integration_account_id>"  # Xoá tài khoản tích hợp
        DETAIL = "/integration-accounts/<integration_account_id>"  # Chi tiết khoản tích hợp

        GOOGLE_SHEET_GET_FOLDER_DEFAULT = (
            "/integration-accounts/google-sheet/folder-default"  # Lấy thông tin folder mặc định khi liên kết tài khoản
        )

        GOOGLE_SHEET_SPREADSHEETS = "/integration-accounts/google-sheet/spreadsheets"  # Lấy danh sách bảng tính
        DETAIL_GOOGLE_SHEET_SPREADSHEETS = (
            "/integration-accounts/google-sheet/spreadsheets/<spreadsheet_id>"  # Chi tiết bảng tính
        )
        ADD_GOOGLE_SHEET_SPREADSHEETS = "/integration-accounts/google-sheet/spreadsheets"  # Tạo bảng tính mới
        GET_SHEETS_IN_GOOGLE_SHEET_SPREADSHEETS = "/integration-accounts/google-sheet/spreadsheets/<spreadsheet_id>/sheets"  # Lấy danh sách trang tính trong bảng tính
        ADD_SHEETS_IN_GOOGLE_SHEET_SPREADSHEETS = (
            "/integration-accounts/google-sheet/spreadsheets/<spreadsheet_id>/sheets"  # Tạo trang tính trong bảng tính
        )
        GET_COLUMNS_IN_SHEET_WITHIN_SPREADSHEET = "/integration-accounts/google-sheet/spreadsheets/<spreadsheet_id>/sheets/<sheet_id>/columns"  # Lấy danh sách cột trong bảng tính
        DETAIL_SHEET_WITHIN_SPREADSHEET = (
            "/integration-accounts/google-sheet/spreadsheets/<spreadsheet_id>/sheets/<sheet_id>"  # Chi tiết sheets
        )


class URI_DIGIENTY:
    REGISTER_TRACKING_LANDING_PAGE = "{host}/digienty/web/api/v1.1/management/landing_page"
    UPDATE_TRACKING_STATUS = "{host}/digienty/web/api/v1.1/management/landing_page/code/tracking_status"


class URI_DATA_OUT:
    SEND_DATA_TEST = "{host}/datasync/api/v1.0/data-flow/send-event/test"
    CHANGE_CONFIG = "{host}/datasync/api/v1.0/data-flow/change-config"


class URI_NOTIFY_MANAGEMENT:
    PROVIDER_CONFIG = "{host}/nm/api/v2.0/merchants/{merchant_id}/configs"
    SEND_MESSAGE = "{host}/nm/api/v2.0/message/multiple/{channel}/transaction"
    MODULE_BY_DOMAIN = "{host}/nm/api/v1.0/customize-email/module/module-by-domain"


class URI_ADMIN:
    MERCHANT_DETAIL = "{host}/adm/api/v2.1/merchants/{merchant_id}"
    ACCOUNT_DETAIL = "{host}/adm/api/v2.1/account/detail"
    GET_MERCHANT_ID_BY_MERCHANT_CODE = "{host}/adm/api/v2.1/merchants/detail/by-code"


class URI_MOBIO_MAILER:
    DOMAIN_VERIFY = "{host}/api/v1.0/email-config/domain/verify"
    CHECK_STATUS = "{host}/api/v1.0/email-config/domain/verify-status"
    DELETE_DOMAIN_CONFIG = "{host}/api/v1.0/email-config/domain"


class URI_JOURNEY_BUILDER:
    JOURNEYS = "{host}/journey/api/v1.0/journeys/common"


class URI_ORCHESTRATION:
    CREATE_PIPELINE = "{host}orchestration/api/v1.0/pipeline_config"
    START_PIPELINE = "{host}orchestration/api/v1.0/pipeline_config/{pipeline_id}/start"
    STOP_PIPELINE = "{host}orchestration/api/v1.0/pipeline_config/{pipeline_id}/stop"
    DETAIL_PIPELINE = "{host}orchestration/api/v1.0/pipeline_config/{orchestration_id}"
    DELETE_PIPELINE = "{host}orchestration/api/v1.0/pipeline_config/{orchestration_id}"


class URI_NM:
    UPDATE_CONFIG_FIREBASE = "{host}api/v2.0/merchants/{merchant_id}/configs"


class URI_WF:
    GET_DOMAIN = "{host}/workflow/internal/api/v1.0/workflow/list-by-email-domain"
