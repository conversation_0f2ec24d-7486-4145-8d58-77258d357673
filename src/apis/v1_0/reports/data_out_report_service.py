from flask import Blueprint

from src.apis import HTTP, auth, build_response_message, try_catch_error
from src.apis.uri import URI
from src.controllers.reports.data_out_report_controller import DataOutReportController

data_out_report_mod = Blueprint("data_out_report", __name__)


@data_out_report_mod.route(URI.DATA_FLOW.REPORTS.DATA_OUT.GET_LIST_AREA_CODE, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_list_area_codes():
    return build_response_message(
        DataOutReportController().get_list_area_codes()
    )
    
@data_out_report_mod.route(URI.DATA_FLOW.REPORTS.DATA_OUT.DETAIL_REQUEST_DATA_OUT, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_detail_request_data_out(source_key, process_data_out_id):
    return build_response_message(
        DataOutReportController().get_detail_request_data_out(source_key, process_data_out_id)
    )


@data_out_report_mod.route(URI.DATA_FLOW.REPORTS.DATA_OUT.LIST_REQUEST_DATA_OUT, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def get_list_request_data_out(source_key):
    return build_response_message(DataOutReportController().get_list_request_data_out(source_key))


@data_out_report_mod.route(URI.DATA_FLOW.REPORTS.DATA_OUT.OVERVIEW_TOTALS, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def get_overview_totals(source_key):
    return build_response_message(
        DataOutReportController().get_report_total_by_field(source_key, field="status_process")
    )


@data_out_report_mod.route(URI.DATA_FLOW.REPORTS.DATA_OUT.OVERVIEW_REQUEST_BY_DAY, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def get_overview_request_by_day(source_key):
    return build_response_message(DataOutReportController().get_report_total_by_field(source_key, field="request_time"))


@data_out_report_mod.route(URI.DATA_FLOW.REPORTS.DATA_OUT.OVERVIEW_EXPORT_BY_ACCOUNT, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def get_overview_export_by_account(source_key):
    return build_response_message(DataOutReportController().get_report_total_by_field_paging(source_key, field="account_id"))
