from flask import Blueprint

from src.apis import HTTP, auth, build_response_message, try_catch_error
from src.apis.uri import URI
from src.controllers.sms_config_controller import SmsConfigController

sms_config_service_mod = Blueprint("sms_config_service", __name__)


@sms_config_service_mod.route(URI.SMS_CONFIG.SMS_CONFIG, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def create_sms_config():
    return build_response_message(SmsConfigController().create_sms_config())


@sms_config_service_mod.route(URI.SMS_CONFIG.SMS_CONFIG, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_sms_configs():
    return build_response_message(SmsConfigController().get_sms_configs())


@sms_config_service_mod.route(URI.SMS_CONFIG.DETAIL, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_detail_sms_config(sms_config_id):
    return build_response_message(SmsConfigController().get_detail_sms_config(sms_config_id))


@sms_config_service_mod.route(URI.SMS_CONFIG.DETAIL, methods=[HTTP.METHOD.DELETE])
@auth.verify_token
@try_catch_error
def delete_sms_config(sms_config_id):
    return build_response_message(SmsConfigController().delete_sms_config(sms_config_id))


@sms_config_service_mod.route(URI.SMS_CONFIG.CHANGE_STATUS, methods=[HTTP.METHOD.PATCH])
@auth.verify_token
@try_catch_error
def change_status_sms_config(sms_config_id):
    return build_response_message(SmsConfigController().change_status_sms_config(sms_config_id))


@sms_config_service_mod.route(URI.SMS_CONFIG.LOGS, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_logs_sms_config(sms_config_id):
    return build_response_message(SmsConfigController().get_logs_sms_config(sms_config_id))


@sms_config_service_mod.route(URI.SMS_CONFIG.DECODE, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def decrypt_values(sms_config_id):
    return build_response_message(SmsConfigController().decrypt_values(sms_config_id))


@sms_config_service_mod.route(URI.SMS_CONFIG.SYNC_DATA, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def sync_data():
    return build_response_message(SmsConfigController().sync_data())


@sms_config_service_mod.route(URI.SMS_CONFIG.SEND_SMS, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def send_sms(sms_config_id):
    return build_response_message(SmsConfigController().send_sms(sms_config_id))


@sms_config_service_mod.route(URI.SMS_CONFIG.RESPONSE_SEND_SMS, methods=[HTTP.METHOD.GET])
@try_catch_error
def response_send_sms():
    return build_response_message(SmsConfigController().response_send_sms())
