from flask import Blueprint

from src.apis import HTTP, auth, build_response_message, try_catch_error
from src.apis.uri import URI
from src.controllers.webpush_controller import WebpushController

webpush_service_mod = Blueprint("webpush_service", __name__)


@webpush_service_mod.route(URI.MARKETPLACE.UPDATE_STATUS, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def update_status():
    return build_response_message(WebpushController().change_status_config())


@webpush_service_mod.route(URI.MARKETPLACE.LIST_CONFIG, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def list_config():
    return build_response_message(WebpushController().get_list_config())


@webpush_service_mod.route(URI.MARKETPLACE.MARKETPLACE, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def create_config():
    return build_response_message(WebpushController().create_config())


@webpush_service_mod.route(URI.MARKETPLACE.MARKETPLACE, methods=[HTTP.METHOD.DELETE])
@auth.verify_token
@try_catch_error
def delete_config():
    return build_response_message(WebpushController().delete_config())


@webpush_service_mod.route(URI.MARKETPLACE.ACTION_HISTORY, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def action_history(config_id):
    return build_response_message(WebpushController().get_history_action(config_id))


@webpush_service_mod.route(URI.MARKETPLACE.DETAIL, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def detail_config(config_id):
    return build_response_message(WebpushController().detail_config(config_id))


@webpush_service_mod.route(URI.MARKETPLACE.DETAIL, methods=[HTTP.METHOD.PUT])
@auth.verify_token
@try_catch_error
def update_config(config_id):
    return build_response_message(WebpushController().update_config(config_id))


@webpush_service_mod.route(URI.MARKETPLACE.SDK_SCRIPT, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def sdk_script_upsert():
    return build_response_message(WebpushController().sdk_script_upsert())


@webpush_service_mod.route(URI.MARKETPLACE.SDK_SCRIPT, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def sdk_script_get():
    return build_response_message(WebpushController().sdk_script_get())


@webpush_service_mod.route(URI.MARKETPLACE.CONNECT_CONFIG, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_connect_config():
    return build_response_message(WebpushController().get_connect_config())


@webpush_service_mod.route(URI.MARKETPLACE.LIST_CONFIG_BY_IDS, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def list_config_by_ids():
    return build_response_message(WebpushController().list_config_by_ids())


@webpush_service_mod.route(URI.WEBPUSH.WEBPUSH_INFORMATION_BY_TRACKING_CODE, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def information_by_tracking_code(tracking_code):
    return build_response_message(WebpushController().get_information_webpush_by_tracking_code(tracking_code))


@webpush_service_mod.route(URI.WEBPUSH.WEBPUSH_CONFIG_BY_TRACKING_CODE, methods=[HTTP.METHOD.GET])
# @auth.verify_token
@try_catch_error
def config_webpush_by_tracking_code(tracking_code):
    return build_response_message(WebpushController().config_webpush_by_tracking_code(tracking_code))


@webpush_service_mod.route(URI.WEBPUSH.WEBPUSH_UPSERT_LANDING_PAGE, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def upsert_landing_page():
    return build_response_message(WebpushController().upsert_landing_page())
