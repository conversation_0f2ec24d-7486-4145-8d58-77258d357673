#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 12/09/2024
"""
from flask import Blueprint

from src.apis import HTTP, auth, build_response_message, try_catch_error
from src.apis.uri import URI
from src.controllers.integration_account_controller import IntegrationAccountController

integration_account_service_mod = Blueprint("integration_account_service", __name__)


@integration_account_service_mod.route(URI.INTEGRATION_ACCOUNTS.LIST, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def list_integration_account():
    return build_response_message(IntegrationAccountController().list_integration_accounts())


@integration_account_service_mod.route(URI.INTEGRATION_ACCOUNTS.DELETE, methods=[HTTP.METHOD.DELETE])
@auth.verify_token
@try_catch_error
def delete_integration_account(integration_account_id):
    return build_response_message(IntegrationAccountController().delete_integration_account(integration_account_id))


@integration_account_service_mod.route(URI.INTEGRATION_ACCOUNTS.DETAIL, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def detail_integration_account(integration_account_id):
    return build_response_message(IntegrationAccountController().detail_integration_account(integration_account_id))


@integration_account_service_mod.route(URI.INTEGRATION_ACCOUNTS.GET_INFORMATION_CONNECT_APP, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_information_config_app():
    return build_response_message(IntegrationAccountController().get_information_app_config_connect())


@integration_account_service_mod.route(URI.INTEGRATION_ACCOUNTS.GOOGLE_CALLBACK, methods=[HTTP.METHOD.GET])
# @auth.verify_token
# @try_catch_error
def google_authorize_callback():
    return IntegrationAccountController().google_authorize_callback()


@integration_account_service_mod.route(
    URI.INTEGRATION_ACCOUNTS.GOOGLE_SHEET_GET_FOLDER_DEFAULT, methods=[HTTP.METHOD.GET]
)
@auth.verify_token
@try_catch_error
def get_folder_default_google_sheet():
    return IntegrationAccountController().get_folder_default_google_sheet()


@integration_account_service_mod.route(URI.INTEGRATION_ACCOUNTS.GOOGLE_SHEET_SPREADSHEETS, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_google_sheet_spreadsheets():
    return IntegrationAccountController().get_google_sheet_spreadsheets()


@integration_account_service_mod.route(
    URI.INTEGRATION_ACCOUNTS.DETAIL_GOOGLE_SHEET_SPREADSHEETS, methods=[HTTP.METHOD.GET]
)
@auth.verify_token
@try_catch_error
def detail_google_sheet_spreadsheets(spreadsheet_id):
    return IntegrationAccountController().detail_google_sheet_spreadsheets(spreadsheet_id)


@integration_account_service_mod.route(
    URI.INTEGRATION_ACCOUNTS.ADD_GOOGLE_SHEET_SPREADSHEETS, methods=[HTTP.METHOD.POST]
)
@auth.verify_token
@try_catch_error
def add_google_sheet_spreadsheets():
    return IntegrationAccountController().add_google_sheet_spreadsheets()


@integration_account_service_mod.route(
    URI.INTEGRATION_ACCOUNTS.GET_SHEETS_IN_GOOGLE_SHEET_SPREADSHEETS, methods=[HTTP.METHOD.GET]
)
@auth.verify_token
@try_catch_error
def get_sheets_in_google_sheet_spreadsheets(spreadsheet_id):
    return IntegrationAccountController().get_sheets_in_google_sheet_spreadsheets(spreadsheet_id)


@integration_account_service_mod.route(
    URI.INTEGRATION_ACCOUNTS.ADD_SHEETS_IN_GOOGLE_SHEET_SPREADSHEETS, methods=[HTTP.METHOD.POST]
)
@auth.verify_token
@try_catch_error
def add_sheets_in_google_sheet_spreadsheets(spreadsheet_id):
    return IntegrationAccountController().add_sheets_in_google_sheet_spreadsheets(spreadsheet_id)


@integration_account_service_mod.route(
    URI.INTEGRATION_ACCOUNTS.GET_COLUMNS_IN_SHEET_WITHIN_SPREADSHEET, methods=[HTTP.METHOD.GET]
)
@auth.verify_token
@try_catch_error
def get_columns_in_sheet_within_spreadsheet(spreadsheet_id, sheet_id):
    return IntegrationAccountController().get_columns_in_sheet_within_spreadsheet(spreadsheet_id, sheet_id)


@integration_account_service_mod.route(
    URI.INTEGRATION_ACCOUNTS.DETAIL_SHEET_WITHIN_SPREADSHEET, methods=[HTTP.METHOD.GET]
)
@auth.verify_token
@try_catch_error
def detail_sheets_in_google_sheet_spreadsheets(spreadsheet_id, sheet_id):
    return IntegrationAccountController().detail_sheets_in_google_sheet_spreadsheets(spreadsheet_id, sheet_id)
