#!/usr/bin/env python
# -*- coding: utf-8 -*-
""" Company: MobioVN
    Date created: 2023/09/22
"""

from mobio.sdks.base.apis.check_service import checking_service_mod

from src.apis import app
from src.apis.v1_0.data_flow_service import data_flow_mod
from src.apis.v1_0.email_config_service import email_config_service_mod
from src.apis.v1_0.file_service import file_service_mod
from src.apis.v1_0.integration_account_service import integration_account_service_mod
from src.apis.v1_0.provider_service import provider_service_mod
from src.apis.v1_0.reports.data_flow_report_service import data_flow_report_mod
from src.apis.v1_0.reports.data_out_report_service import data_out_report_mod
from src.apis.v1_0.sms_config_service import sms_config_service_mod
from src.apis.v1_0.webpush_service import webpush_service_mod

v1_0_prefix = "/api/v1.0"

app.register_blueprint(checking_service_mod, url_prefix=v1_0_prefix, name=checking_service_mod.name)
app.register_blueprint(sms_config_service_mod, url_prefix=v1_0_prefix, name=sms_config_service_mod.name)
app.register_blueprint(data_flow_mod, url_prefix=v1_0_prefix, name=data_flow_mod.name)
app.register_blueprint(webpush_service_mod, url_prefix=v1_0_prefix, name=webpush_service_mod.name)

external_v1_0_prefix = "/market-place/api/v1.0"

app.register_blueprint(
    checking_service_mod, url_prefix=external_v1_0_prefix, name=";{}".format(checking_service_mod.name)
)
app.register_blueprint(
    sms_config_service_mod, url_prefix=external_v1_0_prefix, name="external_{}".format(sms_config_service_mod.name)
)
app.register_blueprint(
    email_config_service_mod, url_prefix=external_v1_0_prefix, name="external_{}".format(email_config_service_mod.name)
)
app.register_blueprint(
    provider_service_mod, url_prefix=external_v1_0_prefix, name="external_{}".format(provider_service_mod.name)
)
app.register_blueprint(
    webpush_service_mod, url_prefix=external_v1_0_prefix, name="external_{}".format(webpush_service_mod.name)
)

app.register_blueprint(
    file_service_mod, url_prefix=external_v1_0_prefix, name="external_{}".format(file_service_mod.name)
)
app.register_blueprint(data_flow_mod, url_prefix=external_v1_0_prefix, name="external_{}".format(data_flow_mod.name))
app.register_blueprint(
    integration_account_service_mod,
    url_prefix=external_v1_0_prefix,
    name="external_{}".format(integration_account_service_mod.name),
)


report_external_v1_0_prefix = "/market-place/api/v1.0/reports"

app.register_blueprint(
    checking_service_mod,
    url_prefix=report_external_v1_0_prefix,
    name="external_report_{}".format(checking_service_mod.name),
)
app.register_blueprint(
    data_flow_report_mod,
    url_prefix=report_external_v1_0_prefix,
    name="external_report_{}".format(data_flow_report_mod.name),
)

app.register_blueprint(
    data_out_report_mod,
    url_prefix=report_external_v1_0_prefix,
    name="external_report_{}".format(data_out_report_mod.name),
)
