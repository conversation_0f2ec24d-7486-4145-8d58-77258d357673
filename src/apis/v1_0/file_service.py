from flask import Blueprint
from src.apis.uri import URI
from src.apis import try_catch_error, build_response_message, HTTP, auth
from src.controllers.file_controller import FileControllers


file_service_mod = Blueprint("file_service", __name__)


@file_service_mod.route(URI.FILE.FILE, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def upload_file():
    return build_response_message(FileControllers().upload_file())


@file_service_mod.route(URI.FILE.FILE, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_file():
    return build_response_message(FileControllers().get_file())
