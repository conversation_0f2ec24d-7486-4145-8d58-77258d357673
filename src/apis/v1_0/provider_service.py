from flask import Blueprint

from src.apis import HTTP, auth, build_response_message, try_catch_error
from src.apis.uri import URI
from src.controllers.provider_controller import ProviderController

provider_service_mod = Blueprint("provider_service", __name__)


@provider_service_mod.route(URI.PROVIDER.PROVIDER, methods=[HTTP.METHOD.PUT])
@auth.verify_token
@try_catch_error
def upsert_provider():
    return build_response_message(ProviderController().upsert_provider())


@provider_service_mod.route(URI.PROVIDER.PROVIDER, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_providers():
    return build_response_message(ProviderController().get_providers())


@provider_service_mod.route(URI.PROVIDER.LOGS, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_log_provider_config(provider_config_id):
    return build_response_message(ProviderController().get_log_provider_config(provider_config_id))
