#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 27/03/2024
"""

from flask import Blueprint

from src.apis import HTTP, auth, build_response_message, try_catch_error
from src.apis.uri import URI
from src.controllers.app_config_controller import AppConfigController
from src.controllers.data_flow_controller import DataFlowController

data_flow_mod = Blueprint("data_flow_service", __name__)


@data_flow_mod.route(URI.DATA_FLOW.SOURCE_TYPES, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_source_types():
    return build_response_message(DataFlowController().get_source_types())


@data_flow_mod.route(URI.DATA_FLOW.SOURCES, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_sources():
    return build_response_message(DataFlowController().get_sources())


@data_flow_mod.route(URI.DATA_FLOW.WHITELIST_IPS, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_ip_whitelist():
    return build_response_message(DataFlowController().get_ip_whitelist())


@data_flow_mod.route(URI.DATA_FLOW.OBJECTS_HANDLED, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_object_handled():
    return build_response_message(DataFlowController().get_object_handled())


@data_flow_mod.route(URI.DATA_FLOW.CHECK_CONNECTION, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def check_connection():
    return build_response_message(DataFlowController().check_connection())


@data_flow_mod.route(URI.DATA_FLOW.CHECK_CONNECTION_DATABASE, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def database_check_connection():
    return build_response_message(DataFlowController().check_connection())


@data_flow_mod.route(URI.DATA_FLOW.CREATE_CONNECTORS, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def create_connectors():
    return build_response_message(DataFlowController().create_connectors())


@data_flow_mod.route(URI.DATA_FLOW.LIST_CONNECTORS, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def list_connectors():
    return build_response_message(DataFlowController().list_connectors())


@data_flow_mod.route(URI.DATA_FLOW.DELETE_CONNECTORS, methods=[HTTP.METHOD.DELETE])
@auth.verify_token
@try_catch_error
def delete_connectors():
    return build_response_message(DataFlowController().delete_connectors())


@data_flow_mod.route(URI.DATA_FLOW.DECRYPT_CONNECTOR_CONFIG, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def decrypt_connector_config(connector_id):
    return build_response_message(DataFlowController().decrypt_connector_config(connector_id))


@data_flow_mod.route(URI.DATA_FLOW.DATA_FLOW_SEND_DATA_TEST_EVENT_DATA_OUT, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def send_data_test_data_out(connector_id):
    return build_response_message(DataFlowController().send_data_test_data_out(connector_id))


@data_flow_mod.route(URI.DATA_FLOW.DATA_FLOW_GET_URL_REQUEST_CONNECTOR, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def data_flow_get_url_request_connector():
    return build_response_message(DataFlowController().data_flow_get_url_request_connector())


@data_flow_mod.route(URI.DATA_FLOW.DATA_FLOW_INTEGRATION_GUIDE, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def data_flow_integration_guide(connector_id):
    return build_response_message(DataFlowController().data_flow_integration_guide(connector_id))

@data_flow_mod.route(URI.DATA_FLOW.DATA_FLOW_API_SAMPLE_RESPONSE, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def data_flow_api_sample_response(connector_id):
    return build_response_message(DataFlowController().data_flow_api_sample_response(connector_id))


@data_flow_mod.route(URI.DATA_FLOW.GET_LIST_TABLES_DATABASE, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_list_table_in_database(connector_id):
    return build_response_message(DataFlowController().get_list_table_in_database(connector_id))


@data_flow_mod.route(URI.DATA_FLOW.GET_DATA_SAMPLE_TABLES_DATABASE, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def get_data_sample_table_in_database(connector_id):
    return build_response_message(DataFlowController().get_data_sample_table_in_database(connector_id))


@data_flow_mod.route(URI.DATA_FLOW.GET_SCHEMA_OF_TABLES_DATABASE, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def get_schema_of_table(connector_id):
    return build_response_message(DataFlowController().get_schema_of_table(connector_id))


@data_flow_mod.route(URI.DATA_FLOW.UPSERT_INFORMATION_OBJECT_HANDED, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def upsert_information_object_handed():
    return build_response_message(DataFlowController().upsert_information_object_handed())


@data_flow_mod.route(URI.DATA_FLOW.UPDATE_CONNECTOR, methods=[HTTP.METHOD.PUT])
@auth.verify_token
@try_catch_error
def update_connectors(connector_id):
    return build_response_message(DataFlowController().update_connectors(connector_id))


@data_flow_mod.route(URI.DATA_FLOW.MODIFIED_CONTENT_CONNECTOR, methods=[HTTP.METHOD.PUT])
@auth.verify_token
@try_catch_error
def modified_content_connector(connector_id):
    return build_response_message(DataFlowController().modified_content_connector(connector_id))


@data_flow_mod.route(URI.DATA_FLOW.DETAIL_CONNECTOR, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def detail_connectors(connector_id):
    return build_response_message(DataFlowController().detail_connectors(connector_id))


@data_flow_mod.route(URI.DATA_FLOW.CONNECTOR_ACTION_SYNC_DATA, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def connector_action_sync(connector_id):
    return build_response_message(DataFlowController().connector_action_sync(connector_id))


@data_flow_mod.route(URI.DATA_FLOW.UPDATE_STATUS_SYNC_DATA, methods=[HTTP.METHOD.PUT])
@auth.verify_token
@try_catch_error
def update_status_sync_data(connector_id):
    return build_response_message(DataFlowController().update_status_sync_data(connector_id))


@data_flow_mod.route(URI.DATA_FLOW.GET_STATE_CONNECTOR, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_state_connector(connector_id):
    return build_response_message(DataFlowController().get_state_connector(connector_id))


@data_flow_mod.route(URI.DATA_FLOW.SOURCES_CONNECTIONS, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_sources_connections():
    return build_response_message(DataFlowController().get_sources_connections())


@data_flow_mod.route(URI.DATA_FLOW.GET_CONNECTOR_SOURCES_CONNECTIONS, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_connector_of_sources_connections_by_source_type(source_key):
    return build_response_message(DataFlowController().get_connector_of_sources_connections_by_source_type(source_key))


@data_flow_mod.route(URI.DATA_FLOW.GET_RELATED_OBJECT, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_related_objects_by_object_primary(object_primary_key):
    return build_response_message(DataFlowController().get_related_objects_by_object_primary(object_primary_key))


@data_flow_mod.route(URI.DATA_FLOW.UPDATE_STATUS_CONNECT, methods=[HTTP.METHOD.PUT])
@auth.verify_token
@try_catch_error
def update_status_connect(connector_id):
    return build_response_message(DataFlowController().update_status_connect(connector_id))


@data_flow_mod.route(URI.APP.LIST_APPS, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_list_app_configs():
    return build_response_message(AppConfigController().get_list_app_configs())


@data_flow_mod.route(URI.APP.CREATE_NEW_APP, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def create_new_app():
    return build_response_message(AppConfigController().create_app())


@data_flow_mod.route(URI.APP.UPDATE_APP, methods=[HTTP.METHOD.PATCH])
@auth.verify_token
@try_catch_error
def update_app_config(app_id):
    return build_response_message(AppConfigController().update_app_config(app_id))


@data_flow_mod.route(URI.APP.APP_DETAIL, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_app_detail(app_id):
    return build_response_message(AppConfigController().app_detail(app_id))


@data_flow_mod.route(URI.APP.DELETE_APP, methods=[HTTP.METHOD.DELETE])
@auth.verify_token
@try_catch_error
def delete_app(app_id):
    return build_response_message(AppConfigController().delete_app(app_id))


@data_flow_mod.route(URI.APP.RENEW_SECRET, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def renew_secret_key(app_id):
    return build_response_message(AppConfigController().generate_new_secret_key(app_id))


@data_flow_mod.route(URI.APP.LIST_EVENT_APP, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def event_app():
    return build_response_message(AppConfigController().get_list_event_by_group())


@data_flow_mod.route(URI.APP.UPDATE_EVENT_STATUS, methods=[HTTP.METHOD.PUT])
@auth.verify_token
@try_catch_error
def update_event_status(event_key):
    return build_response_message(AppConfigController().update_event_status(event_key))


@data_flow_mod.route(URI.APP.SEEN_APP, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def update_is_new_app(app_id):
    return build_response_message(AppConfigController().update_is_new_app(app_id))


@data_flow_mod.route(URI.APP.HEADER_DEFAULT, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def header_default(app_id):
    return build_response_message(AppConfigController().get_header_default(app_id))


@data_flow_mod.route(URI.APP.PARAM_DEFAULT_CONFIG_CONNECT, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_param_default_config_connect(app_id):
    return build_response_message(AppConfigController().get_param_default_config_connect(app_id))


@data_flow_mod.route(URI.APP.GET_LIST_CONNECTOR_USED_APP, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_list_connector_use_app(app_id):
    return build_response_message(AppConfigController().get_list_connector_use_app(app_id))


@data_flow_mod.route(URI.DATA_FLOW.DATA_FLOW_GET_DETAIL_CONNECTORS_BY_IDS, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def detail_connector_by_ids():
    return build_response_message(DataFlowController().detail_connector_by_ids())


@data_flow_mod.route(URI.DATA_FLOW.UPSERT_SETTING_ADD_ON_CONNECTOR, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def upsert_setting_add_on_connector():
    return build_response_message(DataFlowController().upsert_setting_add_on_connector())

@data_flow_mod.route(URI.DATA_FLOW.GET_LIST_TABLES_VIEWS_DATABASE, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_list_table_view_in_database(connector_id):
    return build_response_message(DataFlowController().get_list_table_view_in_database(connector_id))

@data_flow_mod.route(URI.DATA_FLOW.TOTAL_CONNECTORS_SOURCE, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def total_connectors_source():
    return build_response_message(DataFlowController().total_connectors_source())
