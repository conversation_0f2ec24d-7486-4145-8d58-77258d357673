#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 01/04/2024
"""
import inspect
import re


class IBaseClass:

    def __init__(self):
        self.result = {}

    @classmethod
    def get_all_attribute(cls):
        attributes = inspect.getmembers(cls, lambda a: not (inspect.isroutine(a)))
        values = []
        for a in attributes:
            if (
                (a[0].startswith("__") and a[0].endswith("__"))
                or re.match("<function.*?>", str(a[1]))
                or re.match("<class.*?>", str(a[1]))
            ):
                continue
            values.append(a[1])
        return values

    def set_all_data(self, **kwargs):
        for key in self.get_all_attribute():
            if key in kwargs:
                self.result[key] = kwargs.get(key)
