import base64
import datetime
import hashlib
import json
import re
from urllib.parse import Pa<PERSON><PERSON><PERSON><PERSON>, parse_qsl, unquote, urlencode, urlparse

import pytz
from dateutil.parser import parse
from flask import request

from src.common import TimeConfig
from src.common.data_flow_constant import ConstantObjectHandle
from src.common.json_encoder import JSONEncoder


class Zone(datetime.tzinfo):
    def __init__(self, offset, isdst, name):
        self.offset = offset
        self.isdst = isdst
        self.name = name

    def utcoffset(self, dt):
        return datetime.timedelta(hours=self.offset) + self.dst(dt)

    def dst(self, dt):
        return datetime.timedelta(hours=1) if self.isdst else datetime.timedelta(0)

    def tzname(self, dt):
        return self.name


GMT_7 = Zone(7, False, "GMT+7")


class Base64(object):
    @staticmethod
    def encode(data):
        try:
            byte_string = data.encode("utf-8")
            encoded_data = base64.b64encode(byte_string)
            return encoded_data.decode(encoding="UTF-8")
        except Exception as ex:
            print("src/common/utils.py:Base64::encode():error: %s" % ex)
            return ""

    @staticmethod
    def decode(encoded_data):
        try:
            if isinstance(encoded_data, bytes):
                encoded_data = encoded_data.decode("UTF-8")
            decoded_data = base64.urlsafe_b64decode(encoded_data + "=" * (-len(encoded_data) % 4))
            return decoded_data.decode(encoding="UTF-8")
        except Exception as ex:
            print("src/common/utils.py:Base64::decode():error: %s" % ex)
            return ""


def get_request_id():
    url = request.url
    request_time = datetime.datetime.utcnow()
    identify = url + str(request_time)
    identify = identify.encode("utf-8")
    return hashlib.md5(identify).hexdigest()


patterns = {
    "[àáảãạăắằẵặẳâầấậẫẩ]": "a",
    "[đ]": "d",
    "[èéẻẽẹêềếểễệ]": "e",
    "[ìíỉĩị]": "i",
    "[òóỏõọôồốổỗộơờớởỡợ]": "o",
    "[ùúủũụưừứửữự]": "u",
    "[ỳýỷỹỵ]": "y",
}


def utf8_to_ascii(text):
    if text is None:
        return ""
    output = text
    for regex, replace in patterns.items():
        output = re.sub(regex, replace, output)
        # deal with upper case
        output = re.sub(regex.upper(), replace.upper(), output)
    return output


def json_compare(json_old, json_new):
    if not json_old:
        return {"before": None, "after": json_new}
    if not json_new:
        return {"before": json_old, "after": None}

    json_old = json.loads(JSONEncoder().encode(json_old)) if json_old else None
    json_new = json.loads(JSONEncoder().encode(json_new)) if json_new else None

    list_key_old = json_old.keys()
    list_key_new = json_new.keys()

    # Lay danh sach key khong co
    compare_list_old = list(set(list_key_old) - set(list_key_new))
    compare_list_new = list(set(list_key_new) - set(list_key_old))

    before_data = {}
    for old in compare_list_old:
        before_data[old] = json_old.get(old)

    after_data = {}
    for new in compare_list_new:
        after_data[new] = json_new.get(new)

    # Danh sach cac key co trong 2 mang
    list_intersection = [k for k in list_key_old if k in list_key_new]
    list_change = []
    for key in list_intersection:
        value_old = json_old.get(key)
        value_new = json_new.get(key)
        if value_old != value_new:
            list_change.append(key)

    for key_change in list_change:
        before_data[key_change] = json_old.get(key_change)
        after_data[key_change] = json_new.get(key_change)

    return {"before": before_data, "after": after_data}


def get_time_now(timezone=TimeConfig.UTC, is_iso_string=False):
    datetime_now = datetime.datetime.now(pytz.timezone(timezone))
    if is_iso_string:
        return datetime_now.isoformat()
    return datetime_now


def time_minutes_delta(start=datetime.datetime.now(), minutes=0):
    time = start + datetime.timedelta(minutes=minutes)
    return time


def get_timestamp(timezone=TimeConfig.UTC):
    data = datetime.datetime.now(pytz.timezone(timezone)).timestamp()
    return int(data) * 1000


def to_as_timezone(time, timezone=TimeConfig.UTC, is_str=False):
    if is_str:
        return parse(time).astimezone(pytz.timezone(timezone))
    return time.astimezone(pytz.timezone(timezone))


def to_as_tzinfo(time, tzinfo=pytz.UTC):
    return tzinfo.localize(time)


def convert_string_datetime_to_timezone(str_datetime, format_time, timezone=GMT_7):
    from dateutil import tz

    time_value = datetime.datetime.strptime(str_datetime, format_time)
    tzinfo = datetime.datetime.now(tz.tzlocal()).tzname()
    if tzinfo == "UTC":
        time_value = time_value - datetime.timedelta(hours=7)
    result = time_value.astimezone(pytz.utc)
    return result


def convert_string_datetime_to_datetime(str_datetime, format_time):
    if not str_datetime:
        return str_datetime
    time_value = datetime.datetime.strptime(str_datetime, format_time)
    return time_value


def add_url_params(url, params):
    """Add GET params to provided URL being aware of existing.

    :param url: string of target URL
    :param params: dict containing requested params to be added
    :return: string with updated URL

    >> url = 'http://stackoverflow.com/test?answers=true'
    >> new_params = {'answers': False, 'data': ['some','values']}
    >> add_url_params(url, new_params)
    'http://stackoverflow.com/test?data=some&data=values&answers=false'
    """
    # Unquoting URL first so we don't loose existing args
    url = unquote(url)
    # Extracting url info
    parsed_url = urlparse(url)
    # Extracting URL arguments from parsed URL
    get_args = parsed_url.query
    # Converting URL arguments to dict
    parsed_get_args = dict(parse_qsl(get_args))
    # Merging URL arguments dict with new params
    parsed_get_args.update(params)

    # Bool and Dict values should be converted to json-friendly values
    # you may throw this part away if you don't like it :)
    parsed_get_args.update({k: json.dumps(v) for k, v in parsed_get_args.items() if isinstance(v, (bool, dict))})

    # Converting URL argument to proper query string
    encoded_get_args = urlencode(parsed_get_args, doseq=True)
    # Creating new parsed result object based on provided with new
    # URL arguments. Same thing happens inside of urlparse.
    new_url = ParseResult(
        parsed_url.scheme, parsed_url.netloc, parsed_url.path, parsed_url.params, encoded_get_args, parsed_url.fragment
    ).geturl()

    return new_url


def convert_isoformat_to_date(dt_str):
    dt, _, us = dt_str.partition(".")
    dt = datetime.datetime.strptime(dt, "%Y-%m-%dT%H:%M:%S")
    us = int(us.rstrip("Z"), 10)
    return dt + datetime.timedelta(microseconds=us)


def get_list_object_query_starrock():
    lst_object = ConstantObjectHandle.Object.get_all_attribute()
    lst_object_attribute = ConstantObjectHandle.ObjectAttribute.get_all_attribute()

    lst_object.extend(lst_object_attribute)

    lst_object_gen_query = []
    mapping_object_gen_query = {"profiles": "profile", "sale": "deal"}

    for object_item in lst_object:
        if object_item in mapping_object_gen_query:
            lst_object_gen_query.append(mapping_object_gen_query[object_item])
        else:
            lst_object_gen_query.append(object_item)
    lst_object = list(set(lst_object_gen_query))
    return lst_object

def compute_new_sort_time(action_time, current_sort_time=None):
    if isinstance(action_time, str):
        action_time = to_as_timezone(action_time, is_str=True)
    else:
        action_time = to_as_timezone(action_time)

    if not current_sort_time:
        return action_time

    if isinstance(current_sort_time, str):
        current_sort_time = to_as_timezone(current_sort_time, is_str=True)
    else:
        current_sort_time = to_as_timezone(current_sort_time)

    return max(action_time, current_sort_time)


if __name__ == "__main__":
    print(type(convert_string_datetime_to_timezone("2025-04-09 15:50:22", "%Y-%m-%d %H:%M:%S")))
