#!/usr/bin/python
# -*- coding: utf8 -*-

import datetime

import requests
from mobio.libs.logging import MobioLogging
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from src.models.request_log_error_model import RequestLogErrorModel


class RequestRetryAdapter(object):

    def __init__(self):
        self.logging = MobioLogging()

    @staticmethod
    def requests_retry_session(
        retries=3, backoff_factor=0.3, status_forcelist=(500, 501, 502, 504, 400, 401, 404), session=None
    ):
        session = session or requests.Session()
        retry = Retry(
            total=retries,
            read=retries,
            connect=retries,
            backoff_factor=backoff_factor,
            status_forcelist=status_forcelist,
        )
        adapter = HTTPAdapter(max_retries=retry)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        return session

    @classmethod
    def _convert_curl_from_request(cls, response):
        command = "curl -X {method} -H {headers} -d '{data}' '{uri}'"
        method = response.request.method
        uri = response.request.url
        data = response.request.body
        headers = ['"{0}: {1}"'.format(k, v) for k, v in response.request.headers.items()]
        headers = " -H ".join(headers)
        return command.format(method=method, headers=headers, data=data, uri=uri)

    def retry_a_post_request(
        self, url, headers, payload, params=None, times=3, timeout=10, save_log_db=True, is_build_json=True
    ):
        curl = None
        try:

            self.logging.info("RequestRetryAdapter::retry_a_post_request: payload: {}".format(payload))
            response = self.requests_retry_session(retries=times).post(
                url, params=params, json=payload, headers=headers, timeout=timeout
            )
            response.raise_for_status()
            curl = self._convert_curl_from_request(response)
            self.logging.info("RequestRetryAdapter::retry_a_post_request: Request Info: curl-done :: {}".format(curl))
            if is_build_json:
                response = response.json()
            else:
                response = response.text
        except Exception as error:
            self.logging.error(
                "RequestRetryAdapter::retry_a_post_request: Request Info: {} ==== Error: {}".format(payload, error)
            )
            # curl = self._convert_curl_from_request(response)
            # self.logging.info("RequestRetryAdapter::retry_a_post_request: Request Info: curl-fail :: {}".format(curl))
            if save_log_db:
                data_insert = {
                    "method": "POST",
                    "url": url,
                    "headers": headers,
                    "body": payload,
                    "times": times,
                    "curl": curl,
                    "timeout": timeout,
                    "error": str(error),
                    "action_time": datetime.datetime.now(datetime.UTC),
                }
                insert_id = str(RequestLogErrorModel().insert(data_insert).inserted_id)
                self.logging.info("RequestRetryAdapter:: insert_id :: %s " % insert_id)
            return None
        return response

    def retry_a_get_request(self, url, headers, params, times=5, timeout=3):
        try:
            response = self.requests_retry_session(retries=times).get(
                url, params=params, headers=headers, timeout=timeout
            )
            response.raise_for_status()
            response = response.json()
        except Exception as error:
            self.logging.error(
                "RequestRetryAdapter::retry_a_get_request: Request Info: {} ==== Error: {}".format(params, error)
            )
            return None
        return response

    def retry_a_delete_request(self, url, headers, params=None, times=3, payload=None, save_log_db=True):
        try:
            response = self.requests_retry_session(retries=times).delete(
                url, params=params, headers=headers, json=payload
            )
            response.raise_for_status()
            curl = self._convert_curl_from_request(response)
            response = response.json()
        except Exception as error:
            curl = self._convert_curl_from_request(response)
            self.logging.error(
                "RequestRetryAdapter::retry_a_delete_request: Request Info: {} ==== Error: {}".format(params, error)
            )
            if save_log_db:
                data_insert = {
                    "method": "DELETE",
                    "url": url,
                    "headers": headers,
                    "times": times,
                    "params": params,
                    "timeout": times,
                    "curl": curl,
                    "error": str(error),
                    "action_time": datetime.datetime.now(datetime.UTC),
                }
                insert_id = str(RequestLogErrorModel().insert(data_insert).inserted_id)
            return None
        return response

    def retry_a_put_request(self, url, headers, payload=None, times=3, save_log_db=True):
        try:
            response = self.requests_retry_session(retries=times).put(url, json=payload, headers=headers)
            self.logging.info("response_text: %s" % response.text)
            response.raise_for_status()
            curl = self._convert_curl_from_request(response)
            response = response.json()
        except Exception as error:
            # curl = self._convert_curl_from_request(response)
            self.logging.error(
                "RequestRetryAdapter::retry_a_put_request: Request Info: {} ==== Error: {}".format(payload, error)
            )
            if save_log_db:
                data_insert = {
                    "method": "PUT",
                    "url": url,
                    "headers": headers,
                    "times": times,
                    "curl": "",
                    "payload": payload,
                    "timeout": times,
                    "error": str(error),
                    "action_time": datetime.datetime.now(datetime.UTC),
                }
                insert_id = str(RequestLogErrorModel().insert(data_insert).inserted_id)
            return None
        return response
