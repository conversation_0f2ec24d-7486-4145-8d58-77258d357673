import uuid
from datetime import datetime

from mobio.libs.logging import <PERSON><PERSON>Logging

from configs.kafka_config import KAFKA_TOPIC
from src.common.json_encoder import J<PERSON><PERSON>ncoder
from src.helpers.confluent_kafka_base import KafkaProducerManager


class HandleQueue:
    @staticmethod
    def push_message_webpush_config(
        connect_config_id, webpush_config_id, data_before, data_after, action, merchant_id, account_id
    ):
        data_send = {
            "merchant_id": merchant_id,
            "account_id": account_id,
            "connect_config_id": connect_config_id,
            "webpush_config_id": webpush_config_id,
            "data_before": data_before,
            "data_after": data_after,
            "action": action,
            "type": "webpush",
            "time_action": datetime.utcnow(),
        }
        data_send = JSONEncoder("%Y-%m-%dT%H:%MZ").json_loads(data_send)
        HandleQueue.send_message_to_topic(KAFKA_TOPIC.MARKET_PLACE_CHANGE_DATA, data_send)

    @staticmethod
    def push_message_connector_config(connect_config_id, data_before, data_after, action, merchant_id, account_id):
        data_send = {
            "merchant_id": merchant_id,
            "account_id": account_id,
            "connect_config_id": connect_config_id,
            "type": "data_flow",
            "data_before": data_before,
            "data_after": data_after,
            "action": action,
            "time_action": datetime.utcnow(),
        }
        data_send = JSONEncoder("%Y-%m-%dT%H:%MZ").json_loads(data_send)
        HandleQueue.send_message_to_topic(KAFKA_TOPIC.MARKET_PLACE_CHANGE_DATA, data_send)

    def send_message_to_topic(topic_name, data_send, key=None):
        if not key:
            key_uuid = str(uuid.uuid1())
        else:
            key_uuid = key
        MobioLogging().debug("topic_name :: %s " % topic_name)
        MobioLogging().debug("data_send :: %s " % data_send)
        KafkaProducerManager().flush_message(topic=topic_name, value=data_send, key=key_uuid)

    @staticmethod
    def push_message_webpush_config_change_status(status, code, merchant_id):
        data_send = {
            "merchant_id": merchant_id,
            "status": status,
            "source_id": code,
            "created_time": datetime.utcnow().timestamp(),
        }
        data_send = JSONEncoder("%Y-%m-%dT%H:%MZ").json_loads(data_send)
        HandleQueue.send_message_to_topic(KAFKA_TOPIC.ONPAGE_WEBSITE_CHANGING_HOOK, data_send, code)

    @staticmethod
    def push_message_upsert_email_nm(payload, merchant_id):
        data_send = {
            "merchant_id": merchant_id,
            "payload": payload,
            "created_time": datetime.utcnow().timestamp(),
        }
        data_send = JSONEncoder("%Y-%m-%dT%H:%MZ").json_loads(data_send)
        HandleQueue.send_message_to_topic(KAFKA_TOPIC.UPSERT_EMAIL_NM, data_send)
