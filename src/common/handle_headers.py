#!/usr/bin/env python
# -*- coding: utf-8 -*-
from flask import request
from mobio.sdks.admin import MobioAdminSDK

from mobio.sdks.base.common.mobio_exception import CustomError


def get_param_value_temp(param_name):
    return MobioAdminSDK().get_value_from_token(param_name)


def validate_merchant_header():
    merchant_id = request.headers.get("X-Merchant-Id")
    if not merchant_id:
        raise CustomError("Merchant not exist")

    return merchant_id
