#!/usr/bin/env python
# -*- coding: utf-8 -*-
""" Company: MobioVN
"""
import json

from mobio.libs.Singleton import Singleton
from mobio.sdks.base.common.system_config import SystemConfig

from configs import MarketPlaceApplicationConfig
from src.common import SECTION, LANG

LANG_VI = "vi"
LANG_EN = "en"


@Singleton
class LangConfig:
    class STRUCTURE:
        MESSAGE = "message"
        CODE = "code"
        LANG = "lang"

    def __init__(self):
        sys_conf = SystemConfig()
        self.keys = sys_conf.get_section_map(SECTION.LANG)[LANG.KEYS]
        self.default = sys_conf.get_section_map(SECTION.LANG)[LANG.DEFAULT]

        self.languages = self.keys.split(",")
        self.support = {}
        print("lang support: %s" % self.languages)
        for lang in self.languages:
            lang = lang.strip()
            resource = LangConfig._lang_json(lang)
            if resource:
                self.support.update({lang: resource})

    def lang_map(self, lang):
        if not lang:
            lang = self.default

        if lang in self.support:
            return self.support[lang]

        return self.support[self.default]

    @staticmethod
    def _lang_json(lang):
        path = MarketPlaceApplicationConfig.LANG_DIR + "/message_" + lang + ".json"
        try:
            print("lang(%s): %s" % (lang, path))
            with open(path) as data_file:
                data = json.loads(data_file.read())

            return data
        except Exception as ex:
            print(ex)
            return None
