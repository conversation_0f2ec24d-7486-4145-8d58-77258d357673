#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 26/06/2024
"""


class GetFuncNameHandler(object):

    def get_func_name_check_connect_connection(self, source_key, source_type, data_type):
        return "check_connection_{}_{}_{}".format(data_type, source_type, source_key)

    def get_func_name_information_databases(self, action, source_key, source_type, data_type):
        return "get_{}_{}_{}_{}".format(action, data_type, source_type, source_key)

    def get_func_name_validate_param_config_connect(self, source_key, source_type, data_type):
        return "validate_param_config_connect_{}_{}_{}".format(data_type, source_type, source_key)

    def get_func_name_validate_param_create_connector(self, source_key, source_type, data_type):
        return "validate_param_create_connector_{}_{}_{}".format(data_type, source_type, source_key)

    def get_func_name_validate_and_smooth_update_connectors_config_rule_mapping_data(
        self, source_key, source_type, data_type
    ):
        return "validate_and_smooth_update_connectors_config_rule_mapping_data_{}_{}_{}".format(
            data_type, source_type, source_key
        )

    def get_func_name_validate_and_smooth_update_connectors_config_rule_unification(
        self, source_key, source_type, data_type
    ):
        return "validate_and_smooth_update_connectors_config_rule_unification_{}_{}_{}".format(
            data_type, source_type, source_key
        )

    def get_func_name_validate_and_smooth_update_connectors_config_sync_calendar(
        self, source_key, source_type, data_type
    ):
        return "validate_and_smooth_update_connectors_config_sync_calendar_{}_{}_{}".format(
            data_type, source_type, source_key
        )

    def get_func_name_validate_and_smooth_update_connectors_config(self, source_key, source_type, data_type):
        return "validate_and_smooth_update_connectors_{}_{}_{}".format(data_type, source_type, source_key)

    def get_func_name_validate_and_smooth_update_connectors(self, source_key, source_type, data_type):
        return "validate_and_smooth_update_connectors_{}_{}_{}".format(data_type, source_type, source_key)

    def get_func_name_validate_and_smooth_modified_content_connectors(self, source_key, source_type, data_type):
        return "validate_and_smooth_update_connectors_{}_{}_{}".format(data_type, source_type, source_key)
