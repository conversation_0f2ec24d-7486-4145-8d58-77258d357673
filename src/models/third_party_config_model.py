import uuid

from bson import ObjectId

from src.common import ConstantThirdPartyConfigStatus, ConstantTypeThirdParty
from src.common.utils import utf8_to_ascii
from src.models import BaseModel


class ThirdPartyConfigField:
    ID = "_id"
    WEBSITE_NAME = "website_name"
    KEYWORD = "keyword"
    WEBSITE_URL = "website_url"
    WEBSITE_IMG_ID = "website_img_id"
    WEBSITE_IMG_INFO = "website_img_info"
    GTM_ID = "gtm_id"
    CONNECT_INFORMATION_CONFIG_ID = "connect_information_config_id"
    CONNECT_INFORMATION_CONFIG = "connect_information_config"
    STATUS = "status"
    NM_APP_ID = "nm_app_id"
    TYPE = "type"
    CODE = "code"
    SOURCE = "source"
    MERCHANT_ID = "merchant_id"
    CREATED_BY = "created_by"
    UPDATED_BY = "updated_by"
    CREATED_TIME = "created_time"
    UPDATED_TIME = "updated_time"
    INFORMATION_CREATE = "information_create"


class ThirdPartyConfigStatus:
    ENABLE = "ENABLE"
    DISABLE = "DISABLE"
    DELETED = "DELETED"
    ACCEPT_VALUE = [ENABLE, DISABLE]


class ThirdPartyConfigModel(BaseModel):

    def __init__(self):
        super().__init__()
        self.collection_name = "third_party_config"

    def find_by_id(self, merchant_id, config_id):
        return self.find_one({"_id": ObjectId(config_id), ThirdPartyConfigField.MERCHANT_ID: merchant_id})

    def get_by_merchant(self, merchant_id, search, after_token, per_page, search_by_name=None):
        query = {
            ThirdPartyConfigField.MERCHANT_ID: merchant_id,
            ThirdPartyConfigField.STATUS: {"$in": ConstantThirdPartyConfigStatus.ACCEPT_VALUE},
        }
        if search:
            search = utf8_to_ascii(search).lower()
            query_update = {
                "$or": [
                    {ThirdPartyConfigField.KEYWORD: {"$regex": search}},
                    {ThirdPartyConfigField.WEBSITE_URL: {"$regex": search}},
                ]
            }
            query.update(query_update)

        if search_by_name:
            search_by_name = utf8_to_ascii(search_by_name).lower()
            query_update = {ThirdPartyConfigField.KEYWORD: {"$regex": search_by_name}}
            query.update(query_update)
        fields_select = {
            ThirdPartyConfigField.ID: 1,
            ThirdPartyConfigField.WEBSITE_NAME: 1,
            ThirdPartyConfigField.WEBSITE_IMG_ID: 1,
            ThirdPartyConfigField.STATUS: 1,
            ThirdPartyConfigField.WEBSITE_URL: 1,
            ThirdPartyConfigField.STATUS: 1,
            ThirdPartyConfigField.CREATED_TIME: 1,
            ThirdPartyConfigField.UPDATED_TIME: 1,
            ThirdPartyConfigField.SOURCE: 1,
            ThirdPartyConfigField.CODE: 1,
        }
        webpush_configs, next_token = ThirdPartyConfigModel().find_paginate_load_more(
            search_option=query,
            projection=fields_select,
            per_page=per_page,
            after_token=after_token,
            sort_option=[("updated_time", -1)],
        )
        return webpush_configs, next_token

    def get_by_ids(self, config_ids, after_token, per_page):
        query = {"_id": {"$in": config_ids}}
        fields_select = {
            ThirdPartyConfigField.ID: 1,
            ThirdPartyConfigField.WEBSITE_NAME: 1,
            ThirdPartyConfigField.WEBSITE_IMG_ID: 1,
            ThirdPartyConfigField.WEBSITE_URL: 1,
            ThirdPartyConfigField.STATUS: 1,
            ThirdPartyConfigField.WEBSITE_URL: 1,
            ThirdPartyConfigField.STATUS: 1,
            ThirdPartyConfigField.CREATED_TIME: 1,
            ThirdPartyConfigField.UPDATED_TIME: 1,
            ThirdPartyConfigField.UPDATED_TIME: 1,
        }
        webpush_configs, next_token = ThirdPartyConfigModel().find_paginate_load_more(
            search_option=query,
            projection=fields_select,
            per_page=per_page,
            after_token=after_token,
            sort_option=[("updated_time", -1)],
        )
        return webpush_configs, next_token

    def get_by_list_code(self, list_code, after_token, per_page, merchant_id):
        query = {ThirdPartyConfigField.MERCHANT_ID: merchant_id, ThirdPartyConfigField.CODE: {"$in": list_code}}
        fields_select = {
            ThirdPartyConfigField.ID: 1,
            ThirdPartyConfigField.WEBSITE_NAME: 1,
            ThirdPartyConfigField.WEBSITE_IMG_ID: 1,
            ThirdPartyConfigField.WEBSITE_URL: 1,
            ThirdPartyConfigField.STATUS: 1,
            ThirdPartyConfigField.CODE: 1,
            ThirdPartyConfigField.WEBSITE_URL: 1,
            ThirdPartyConfigField.STATUS: 1,
            ThirdPartyConfigField.CODE: 1,
            ThirdPartyConfigField.CREATED_TIME: 1,
            ThirdPartyConfigField.UPDATED_TIME: 1,
            ThirdPartyConfigField.UPDATED_TIME: 1,
        }
        webpush_configs, next_token = ThirdPartyConfigModel().find_paginate_load_more(
            search_option=query,
            projection=fields_select,
            per_page=per_page,
            after_token=after_token,
            sort_option=[("updated_time", -1)],
        )
        return webpush_configs, next_token

    def get_data_to_compare(self, config_id):
        query = {"_id": ObjectId(config_id)}
        field_select = {
            ThirdPartyConfigField.ID: 0,
            ThirdPartyConfigField.CONNECT_INFORMATION_CONFIG_ID: 1,
            ThirdPartyConfigField.TYPE: 1,
            ThirdPartyConfigField.WEBSITE_NAME: 1,
            ThirdPartyConfigField.WEBSITE_URL: 1,
            ThirdPartyConfigField.WEBSITE_IMG_ID: 1,
            ThirdPartyConfigField.GTM_ID: 1,
        }
        return self.find_one(query, field_select)

    def find_by_website_url(self, merchant_id, website_url):
        query = {ThirdPartyConfigField.MERCHANT_ID: merchant_id, ThirdPartyConfigField.WEBSITE_URL: website_url}
        return ThirdPartyConfigModel().find_one(query)

    def generate_code_webpush(self):
        return "-".join(["MWP", str(uuid.uuid1())])

    def get_information_webpush_by_tracking_code(
        self, request_merchant_id, tracking_code, field_select=None, status=None
    ):
        if not field_select:
            field_select = {
                ThirdPartyConfigField.ID: 0,
                ThirdPartyConfigField.MERCHANT_ID: 1,
                ThirdPartyConfigField.STATUS: 1,
            }
        filter_option = {
            ThirdPartyConfigField.CODE: tracking_code,
            ThirdPartyConfigField.TYPE: ConstantTypeThirdParty.WEBPUSH,
            ThirdPartyConfigField.MERCHANT_ID: request_merchant_id,
        }
        if status:
            filter_option[ThirdPartyConfigField.STATUS] = status
        return self.find_one(filter_option, field_select)

    def get_webpush_detail_by_landing_page_id(self, merchant_id, landing_page_id):
        filter_option = {
            "information_create.source": "landing_page",
            "information_create.object_id": landing_page_id,
            "merchant_id": merchant_id,
        }
        field_select = {
            ThirdPartyConfigField.ID: 1,
            ThirdPartyConfigField.CODE: 1,
        }
        return self.find_one(filter_option, field_select)

    def get_detail_config_by_id(self, merchant_id, config_id):
        filter_option = {
            ThirdPartyConfigField.MERCHANT_ID: merchant_id,
            ThirdPartyConfigField.ID: ObjectId(config_id),
        }
        field_select = {
            ThirdPartyConfigField.ID: 1,
            ThirdPartyConfigField.WEBSITE_NAME: 1,
            ThirdPartyConfigField.WEBSITE_URL: 1,
            ThirdPartyConfigField.WEBSITE_IMG_ID: 1,
            ThirdPartyConfigField.GTM_ID: 1,
            ThirdPartyConfigField.CONNECT_INFORMATION_CONFIG_ID: 1,
            ThirdPartyConfigField.STATUS: 1,
            ThirdPartyConfigField.MERCHANT_ID: 1,
            ThirdPartyConfigField.CREATED_BY: 1,
            ThirdPartyConfigField.UPDATED_BY: 1,
            ThirdPartyConfigField.CREATED_TIME: 1,
        }
        return self.find_one(filter_option, field_select)


if __name__ == "__main__":
    ThirdPartyConfigModel().insert(
        {
            ThirdPartyConfigField.ID: ObjectId(),
            ThirdPartyConfigField.WEBSITE_NAME: "test",
            ThirdPartyConfigField.WEBSITE_URL: "test",
            ThirdPartyConfigField.WEBSITE_IMG_ID: "test",
            ThirdPartyConfigField.GTM_ID: "test",
            ThirdPartyConfigField.CONNECT_INFORMATION_CONFIG_ID: "test",
            ThirdPartyConfigField.STATUS: ConstantThirdPartyConfigStatus.ENABLE,
            ThirdPartyConfigField.MERCHANT_ID: "test",
            ThirdPartyConfigField.CREATED_BY: "test",
            ThirdPartyConfigField.UPDATED_BY: "test",
            ThirdPartyConfigField.CREATED_TIME: "test",
            ThirdPartyConfigField.UPDATED_TIME: "test",
            "type": "webpush",
        }
    )
