#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 01/04/2024
"""
import datetime

from src.models import BaseModel


class LogSyncDataToModuleOtherModel(BaseModel):
    """
    - <PERSON><PERSON><PERSON> l<PERSON> log khi request đến module khác
    """

    def __init__(self):
        self.collection_name = "log_sync_data_to_module_other"

    def get_detail_log_sync_orchestration_by_connector_id(self, merchant_id, connector_id):
        filter_option = {"merchant_id": merchant_id, "module_sync": "orchestration", "connector_id": int(connector_id)}
        return self.find_one(filter_option)

    def insert_log_sync_data_to_module_orchestration(
        self, merchant_id, connector_id, orchestration_id, data_logs, module_data, body_data_sync
    ):
        data_insert = {
            "merchant_id": merchant_id,
            "module_sync": "orchestration",
            "connector_id": int(connector_id),
            "status": "success",
            "module_id": orchestration_id,
            "logs": data_logs,
            "module_data": module_data,
            "data_sync": body_data_sync,
            "action_time": datetime.datetime.now(datetime.UTC),
        }
        if not connector_id:
            data_insert["status"] = "fail"
        log_id = self.insert_document(data_insert)
        return log_id

    def update_log_sync_data_to_module_orchestration(self, log_id, orchestration_id, body_data_sync):
        data_update = {
            "status": "success",
            "module_id": orchestration_id,
            "data_sync": body_data_sync,
            "action_time": datetime.datetime.now(datetime.UTC),
        }
        if not orchestration_id:
            data_update["status"] = "fail"
        status = self.update_dictionary(log_id, {"$set": data_update})
        return

    def get_orchestration_id_by_connector_id(self, merchant_id, connector_id):
        filter_option = {
            "merchant_id": merchant_id,
            "module_sync": "orchestration",
            "connector_id": int(connector_id),
        }
        detail = self.find_one(filter_option)
        if not detail:
            return None
        return detail.get("module_id")
