#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 13/09/2024
"""

from bson import ObjectId

from src.common.integration_account_constant import TypeIntegrationAccount
from src.common.utils import utf8_to_ascii
from src.models import BaseModel


class IntegrationAccountModel(BaseModel):

    def __init__(self):
        super().__init__()
        self.collection_name = "integration_account"

    def get_list_paging_by_condition(self, merchant_id, name_search, sort_option, before_token, per_page, projection):
        query_params = {"merchant_id": merchant_id, "type": TypeIntegrationAccount.GOOGLE_SHEET}
        if name_search:
            query_params.update({"name": {"$regex": utf8_to_ascii(name_search), "$options": "i"}})
        data, after_token = self.find_paginate_load_more(
            query_params,
            after_token=before_token,
            per_page=per_page,
            sort_option=sort_option,
            projection=projection,
        )
        return data, after_token

    def get_by_id(self, merchant_id, integration_account_id, protection=None):
        query = {"_id": ObjectId(integration_account_id), "merchant_id": merchant_id}
        return self.find_one(query, protection)
