import math

from src.common import CommonKeys
from src.common.utils import get_time_now
from src.models import BaseModel


class LogActionModel(BaseModel):

    def __init__(self):
        super().__init__()
        self.collection_name = "log_action"

    def insert_log_action(
        self, merchant_id, account_id, provider_config_id, action, status, service
    ):
        payload = {
            CommonKeys.CREATED_TIME: get_time_now(),
            "provider_config_id": provider_config_id,
            "merchant_id": merchant_id,
            "created_by": account_id,
            "action": action,
            "status": status,
            "type": service,
        }
        return self.insert(payload).inserted_id

    def get_logs(self, query, order, sort, page, per_page):
        results = list(self.find_paginate(query, page, per_page, sort_option=sort, order=order))
        total_count = self.count(query)
        total_page = math.ceil(total_count / per_page)
        return results, {
            "page": page,
            "per_page": per_page,
            "total_page": total_page,
            "total_count": total_count,
        }
