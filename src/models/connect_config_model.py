from bson import ObjectId

from src.models import BaseModel


class ConnectConfigField:
    ID = "_id"
    SERVER_KEY = "server_key"
    API_KEY = "api_key"
    AUTH_DOMAIN = "auth_domain"
    DATABASE_URL = "database_url"
    PROJECT_ID = "project_id"
    STORAGE_BUCKET = "storage_bucket"
    MESSAGINGSENDER_ID = "messagingsender_id"
    APP_ID = "app_id"
    MEASUREMENT_ID = "measurement_id"
    TYPE = "type"
    APP_CONNECT_TYPE = "connect_type"
    MERCHANT_ID = "merchant_id"
    CREATED_TIME = "created_time"
    UPDATED_TIME = "updated_time"
    CREATED_BY = "created_by"
    UPDATED_BY = "updated_by"


class TypeConnectConfig:
    DEFAULT = "DEFAULT"
    CUSTOM = "CUSTOM"
    ACCEPT_VALUE = [DEFAULT, CUSTOM]


class AppConnectType:
    FIREBASE = "firebase"
    ACCEPT_VALUE = [FIREBASE]


class ConnectConfigModel(BaseModel):

    def __init__(self):
        super().__init__()
        self.collection_name = "connect_config"

    @staticmethod
    def get_default_config_id(merchant_id):
        field_select = {ConnectConfigField.ID: 1}
        default_config_by_merchant = ConnectConfigModel().find_one(
            {ConnectConfigField.TYPE: TypeConnectConfig.DEFAULT, ConnectConfigField.MERCHANT_ID: merchant_id},
            field_select,
        )
        if default_config_by_merchant:
            return default_config_by_merchant
        return ConnectConfigModel().find_one(
            {
                ConnectConfigField.TYPE: TypeConnectConfig.DEFAULT,
                ConnectConfigField.MERCHANT_ID: TypeConnectConfig.DEFAULT,
            },
            field_select,
        )

    @staticmethod
    def get_default_config(merchant_id):
        field_select = {
            ConnectConfigField.ID: 0,
            ConnectConfigField.SERVER_KEY: 1,
            ConnectConfigField.API_KEY: 1,
            ConnectConfigField.DATABASE_URL: 1,
            ConnectConfigField.PROJECT_ID: 1,
            ConnectConfigField.MESSAGINGSENDER_ID: 1,
            ConnectConfigField.APP_ID: 1,
            ConnectConfigField.MEASUREMENT_ID: 1,
            ConnectConfigField.TYPE: 1,
        }
        default_config_by_merchant = ConnectConfigModel().find_one(
            {ConnectConfigField.TYPE: TypeConnectConfig.DEFAULT, ConnectConfigField.MERCHANT_ID: merchant_id},
            field_select,
        )
        if default_config_by_merchant:
            return default_config_by_merchant
        return ConnectConfigModel().find_one(
            {
                ConnectConfigField.TYPE: TypeConnectConfig.DEFAULT,
                ConnectConfigField.MERCHANT_ID: TypeConnectConfig.DEFAULT,
            },
            field_select,
        )

    @staticmethod
    def get_current_config(connect_config_id):
        query = {ConnectConfigField.ID: ObjectId(connect_config_id)}
        field_select = {
            ConnectConfigField.ID: 0,
            ConnectConfigField.SERVER_KEY: 1,
            ConnectConfigField.API_KEY: 1,
            ConnectConfigField.DATABASE_URL: 1,
            ConnectConfigField.PROJECT_ID: 1,
            ConnectConfigField.STORAGE_BUCKET: 1,
            ConnectConfigField.MESSAGINGSENDER_ID: 1,
            ConnectConfigField.APP_ID: 1,
            ConnectConfigField.MEASUREMENT_ID: 1,
            ConnectConfigField.TYPE: 1,
        }
        return ConnectConfigModel().find_one(query, field_select)

    def get_current_config_by_type(self, connect_config_id, config_type):
        query = {ConnectConfigField.ID: ObjectId(connect_config_id), ConnectConfigField.APP_CONNECT_TYPE: config_type}
        field_select = {
            ConnectConfigField.ID: 0,
            ConnectConfigField.SERVER_KEY: 1,
            ConnectConfigField.API_KEY: 1,
            ConnectConfigField.DATABASE_URL: 1,
            ConnectConfigField.PROJECT_ID: 1,
            ConnectConfigField.STORAGE_BUCKET: 1,
            ConnectConfigField.MESSAGINGSENDER_ID: 1,
            ConnectConfigField.APP_ID: 1,
            ConnectConfigField.MEASUREMENT_ID: 1,
            ConnectConfigField.TYPE: 1,
            ConnectConfigField.AUTH_DOMAIN: 1,
            ConnectConfigField.APP_CONNECT_TYPE: 1,
        }
        return self.find_one(query, field_select)

    def upsert_config_firebase_default(self, data_upsert):
        filter_option = {
            ConnectConfigField.TYPE: TypeConnectConfig.DEFAULT,
            ConnectConfigField.APP_CONNECT_TYPE: AppConnectType.FIREBASE,
        }
        return self.update_one_query(filter_option, data_upsert)


if __name__ == "__main__":
    data_insert = {
        ConnectConfigField.SERVER_KEY: "test",
        ConnectConfigField.API_KEY: "test",
        ConnectConfigField.DATABASE_URL: "test",
        ConnectConfigField.PROJECT_ID: "test",
        ConnectConfigField.STORAGE_BUCKET: "test",
        ConnectConfigField.MESSAGINGSENDER_ID: "test",
        ConnectConfigField.APP_ID: "test",
        ConnectConfigField.MEASUREMENT_ID: "test",
        ConnectConfigField.TYPE: "DEFAULT",
        ConnectConfigField.MERCHANT_ID: "DEFAULT",
        ConnectConfigField.APP_CONNECT_TYPE: "firebase",
    }
    ConnectConfigModel().insert(data_insert)
