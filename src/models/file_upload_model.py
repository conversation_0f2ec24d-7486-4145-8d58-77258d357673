from src.common.i_base_class import IBaseClass
from src.models import BaseModel


class FileUploadField:
    ID = "_id"
    URL = "url"
    LOCAL_PATH = "local_path"
    FILENAME = "filename"
    FORMAT = "format"
    CAPACITY = "capacity"
    CREATED_BY = "created_by"
    CREATED_TIME = "created_time"
    FILE_NAME = "file_name"
    TYPE = "type"


class TypeFileUpload(IBaseClass):
    IMAGE = "image"
    SDK = "sdk"
    INSTRUCTIONS_ADD_GOOGLE_TAG_IN_WEBSITE = "instructions_add_google_tag_in_website"
    ACCEPT_VALUE = [IMAGE, SDK]


class FileUploadModel(BaseModel):

    def __init__(self):
        super().__init__()
        self.collection_name = "file_upload"
