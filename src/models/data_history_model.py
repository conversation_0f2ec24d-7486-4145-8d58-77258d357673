from src.models import BaseModel


class DataHistoryModel(BaseModel):

    def __init__(self):
        super().__init__()
        self.collection_name = "data_history"

    def get_data_by_config_id(self, config_id, merchant_id, per_page, after_token):
        query = {"webpush_config_id": config_id, "merchant_id": merchant_id}
        field_select = {"action": 1, "time_action": 1, "account_id": 1, "merchant_id": 1}
        history_data, next_token = DataHistoryModel().find_paginate_load_more(
            search_option=query,
            projection=field_select,
            per_page=per_page,
            after_token=after_token,
            sort_option=[("_id", -1)],
        )
        return history_data, next_token

    def get_state_connector_by_id(self, connector_id, merchant_id, per_page, after_token):
        query = {"connect_config_id": int(connector_id), "merchant_id": merchant_id}
        field_select = {"action": 1, "time_action": 1, "account_id": 1, "merchant_id": 1, "data_after": 1}
        history_data, next_token = DataHistoryModel().find_paginate_load_more(
            search_option=query,
            projection=field_select,
            per_page=per_page,
            after_token=after_token,
            sort_option=[("_id", -1)],
        )
        return history_data, next_token
