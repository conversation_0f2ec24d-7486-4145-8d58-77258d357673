from src.common import CommonKeys
from src.common.utils import get_time_now
from src.models import BaseModel

class ConfigSesField:
    ID = "_id"
    MERCHANT_ID = "merchant_id"
    STATUS = "status"


class ConfigShowSesModel(BaseModel):

    def __init__(self):
        super().__init__()
        self.collection_name = "config_show_account_ses"


    def get_account_ses_config(self, merchant_id):
        return self.find_one({ConfigSesField.MERCHANT_ID: merchant_id})


    def upsert_account_ses_config(self, payload, created_by):
        query = {
            ConfigSesField.MERCHANT_ID: payload.get(ConfigSesField.MERCHANT_ID),
        }

        payload.update(
            {
                CommonKeys.CREATED_BY: created_by,
                CommonKeys.CREATED_TIME: get_time_now(),
                CommonKeys.UPDATED_TIME: get_time_now(),
                CommonKeys.UPDATED_BY: created_by,
            }
        )

        return self.upsert(query, payload)

