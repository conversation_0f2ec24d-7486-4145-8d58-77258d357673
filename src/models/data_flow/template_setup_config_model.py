#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 27/03/2024
"""

from src.models import BaseModel


class TemplateSetupConfigModel(BaseModel):
    def __init__(self):
        super().__init__()
        self.collection_name = "template_setup_config"

    def _upsert_template_setup_config(self):
        data_config_default = [
            {
                "type": "databases",
                "key": "postgres",
                "template": """import psycopg2\ntry:\n\tconn = psycopg2.connect(host="{host}", database="{database}", user="{username}", password="{password}")\n\tconn.close()\nexcept Exception as ex:\n\tprint(ex)\n\tprint(psycopg2.errors.lookup(ex.pgcode[:2]))""",
            }
        ]
        for data_config in data_config_default:
            data_config_type = data_config.get("type")
            data_config_key = data_config.get("key")

            self.update_by_set({"type": data_config_type, "key": data_config_key}, data_config, upsert=True)


if __name__ == "__main__":
    TemplateSetupConfigModel()._upsert_template_setup_config()
    x = TemplateSetupConfigModel().find_one({})
    exec(x["template"].format(host="localhost", database="12313", username="username", password="password"))
