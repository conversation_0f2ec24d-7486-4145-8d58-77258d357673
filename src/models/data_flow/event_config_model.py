import datetime

from src.common import lru_redis_cache
from src.models import BaseModel


class GroupEventModel:
    PROFILE = "Profile"
    JOURNEY_BUILDER = "Journey builder"
    SALE = "Sale"
    TASK = "Task"
    COMPANY = "Company"
    TICKET = "Ticket"
    NOTE = "Note"

    GROUP_EVENT = [PROFILE, JOURNEY_BUILDER, SALE, TASK, COMPANY, TICKET, NOTE]


class InformationEventDataOutField:
    ID = "_id"
    EVENT_NAME = "name"
    EVENT_KEY = "event_key"
    DESCRIPTION = "description"
    STATUS = "status"
    DATA_TEST = "data_test"
    LINK_DOC = "link_doc"
    GROUP_CODE = "group_code"
    CREATED_TIME = "created_time"
    UPDATED_TIME = "updated_time"

    class EventStatus:
        ACTIVE = "active"
        INACTIVE = "inactive"
        STATUS = [ACTIVE, INACTIVE]


class InformationEventDataOutModel(BaseModel):
    def __init__(self):
        super().__init__()
        self.collection_name = "information_event_data_out"

    def get_list_event(self, obj_query: dict, selected_field: list = None, sort_option=None, page=-1):
        projection = {InformationEventDataOutField.ID: 1}
        if not selected_field:
            projection.update(
                {
                    InformationEventDataOutField.EVENT_NAME: 1,
                    InformationEventDataOutField.EVENT_KEY: 1,
                    InformationEventDataOutField.STATUS: 1,
                    InformationEventDataOutField.DATA_TEST: 1,
                }
            )
        else:
            for field in selected_field:
                projection.update({field: 1})
        if not sort_option:
            sort_option = [(InformationEventDataOutField.CREATED_TIME, 1)]
        events = self.find_paginate(search_option=obj_query, page=page, projection=projection, sort_option=sort_option)
        results = [x for x in events]
        return results

    def get_event_by_id(self, event_id):
        return self.find_one({InformationEventDataOutField.ID: event_id})

    def update_event_status(self, event_id, status):
        update_data = {
            InformationEventDataOutField.STATUS: status,
            InformationEventDataOutField.UPDATED_TIME: datetime.datetime.now(datetime.UTC),
        }
        update_status = self.update_by_set({InformationEventDataOutField.ID: event_id}, update_data)
        return update_status

    @lru_redis_cache.add_for_class(expiration=60)
    def get_list_event_key_active_by_group_key(self, group_key):
        return self.distinct(
            InformationEventDataOutField.EVENT_KEY,
            {
                InformationEventDataOutField.STATUS: InformationEventDataOutField.EventStatus.ACTIVE,
                InformationEventDataOutField.GROUP_CODE: group_key,
            },
        )

    @lru_redis_cache.add_for_class(expiration=60)
    def get_detail_event_by_event_key(self, merchant_id, group_key, event_key):
        return self.find_one(
            {
                InformationEventDataOutField.STATUS: InformationEventDataOutField.EventStatus.ACTIVE,
                InformationEventDataOutField.GROUP_CODE: group_key,
                InformationEventDataOutField.EVENT_KEY: event_key,
            },
        )
