#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 12/09/2024
"""
from src.models import BaseModel


class SettingAddOnConnectorModel(BaseModel):
    """
    - <PERSON><PERSON><PERSON> trúc table
        + merchant_id: Đ<PERSON><PERSON> danh tenant nếu không có thì sẽ là mặc định
        + source_type:
        + source_key:
        + data_type:
        + extract_config: <PERSON><PERSON><PERSON> h<PERSON><PERSON> thêm.
    Sample:
    {
        "merchant_id": "default",
        "source_type": "form",
        "source_key": "contact_form",
        "data_type": "data_in",
        "extract_config": {
            "topic_consume_data_raw": "topic_x",
            "include_fields": [
                {
                    "fields": ["profile_id"],
                    "source": "sdk,
                    "object": "profile"
                }
            ],

        }
    }

    """

    def __init__(self):
        super().__init__()
        self.collection_name = "setting_add_on_connector"

    def upsert_setting(self, filter_option, data_update):

        return self.upsert(filter_option, data_update)

    def get_setting_by_merchant_id(
        self,
        merchant_id,
        source_type,
        source_key,
        data_type,
        object_primary=None,
        object_attribute=None,
    ):
        filter_option = {
            "merchant_id": {"$in": [merchant_id, "default"]},
            "source_type": source_type,
            "source_key": source_key,
            "data_type": data_type,
        }
        if object_primary:
            filter_option["object_primary"] = object_primary
        if object_attribute:
            filter_option["object_attribute"] = object_attribute

        results = self.find(filter_option)
        results = [*results]
        for item in results:
            if item["merchant_id"] == merchant_id:
                return item
        return results[0] if len(results) == 1 else {}
