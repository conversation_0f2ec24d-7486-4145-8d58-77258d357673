#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 28/03/2024
"""
import datetime

from src.common import utils
from src.common.data_flow_constant import (
    ConstantParamApi,
    ConstantStatusConfigConnector,
    ConstantStatusConnect,
    ConstantStatusSyncData,
)
from src.common.i_base_class import IBaseClass
from src.models import BaseModel


class ConstantConfigConnectorsModel(IBaseClass):
    ID = "_id"
    MERCHANT_ID = "merchant_id"
    CREATED_BY = "created_by"
    UPDATED_BY = "updated_by"
    DATA_TYPE = "data_type"
    CREATED_TIME = "created_time"
    UPDATED_TIME = "updated_time"
    STATUS = "status"
    SORT_TIME = "sort_time"


class ConfigConnectorsModel(BaseModel):
    def __init__(self):
        super().__init__()
        self.collection_name = "config_connectors"

    def insert_connector(self, merchant_id, account_id, **kwargs):

        record_id = 1
        get_last_record = [
            *self.find({}, {ConstantConfigConnectorsModel.ID: 1}).sort({ConstantConfigConnectorsModel.ID: -1}).limit(1)
        ]
        if get_last_record:
            record_id = get_last_record[0].get(ConstantConfigConnectorsModel.ID) + 1
        data_init_insert = {
            ConstantConfigConnectorsModel.ID: record_id,
            ConstantConfigConnectorsModel.MERCHANT_ID: merchant_id,
            ConstantConfigConnectorsModel.CREATED_BY: account_id,
            ConstantConfigConnectorsModel.STATUS: ConstantStatusConfigConnector.ACTIVE,
            ConstantConfigConnectorsModel.UPDATED_BY: account_id,
            ConstantConfigConnectorsModel.CREATED_TIME: datetime.datetime.now(datetime.UTC),
            ConstantConfigConnectorsModel.UPDATED_TIME: datetime.datetime.now(datetime.UTC),
            ConstantConfigConnectorsModel.SORT_TIME: datetime.datetime.now(datetime.UTC),
        }
        data_init_insert.update(kwargs)

        self.insert_document(data_init_insert)
        return data_init_insert

    def detail_connector_by_id(self, merchant_id, connector_id):
        filter_option = {
            ConstantConfigConnectorsModel.MERCHANT_ID: merchant_id,
            ConstantConfigConnectorsModel.ID: int(connector_id),
            ConstantConfigConnectorsModel.STATUS: ConstantStatusConfigConnector.ACTIVE,
        }
        return self.find_one(filter_option)

    def update_config_connector_by_id(self, connector_id, data_update, account_id, sort_time_current=None):
        sort_time = utils.compute_new_sort_time(datetime.datetime.now(datetime.UTC), sort_time_current)

        data_op_update = {
            ConstantConfigConnectorsModel.UPDATED_BY: account_id,
            ConstantConfigConnectorsModel.UPDATED_TIME: datetime.datetime.now(datetime.UTC),
            ConstantConfigConnectorsModel.SORT_TIME: sort_time,
        }
        data_op_update.update(data_update)
        return self.update_by_set({"_id": int(connector_id)}, data_op_update, upsert=True)

    def aggregate_source_connections(self, merchant_id, search, data_type):
        filter_option = {
            ConstantConfigConnectorsModel.MERCHANT_ID: merchant_id,
            ConstantConfigConnectorsModel.DATA_TYPE: data_type,
            ConstantConfigConnectorsModel.STATUS: ConstantStatusConfigConnector.ACTIVE,
        }
        if search:
            search = self._gen_data_build_search_query(search)
            filter_option.update({"keywords": search})
        return self.get_collector().aggregate(
            [
                {"$match": filter_option},
                {
                    "$group": {
                        "_id": {"source_key": "$source_key", "source_type": "$source_type"},
                        "connector_id": {"$first": "$_id"},
                        "is_trust_source": {"$first": "$is_trust_source"},
                        "status_sync": {"$first": "$status_sync"},
                        "created_by": {"$first": "$created_by"},
                        "updated_by": {"$first": "$updated_by"},
                        "created_time": {"$first": "$created_time"},
                        "updated_time": {"$first": "$updated_time"},
                        "object": {"$first": "$object"},
                        "name": {"$first": "$name"},
                        "description": {"$first": "$description"},
                        "source_key": {"$first": "$source_key"},
                        "source_type": {"$first": "$source_type"},
                        "config_sync_calendar": {"$first": "$config_sync_calendar"},
                        "config_mapping_data": {"$first": "$config_mapping_data"},
                        "status_connect": {"$first": "$status_connect"},
                        "last_datetime_sync_data": {"$first": "$last_datetime_sync_data"},
                        "config_information_out": {"$first": "$config_information_out"},
                        "count": {"$sum": 1},
                    }
                },
                {
                    "$addFields": {
                        "detail_connector": {
                            "$cond": {
                                "if": {"$eq": ["$count", 1]},
                                "then": {
                                    "_id": "$connector_id",
                                    "is_trust_source": "$is_trust_source",
                                    "object": "$object",
                                    "status_sync": "$status_sync",
                                    "created_by": "$created_by",
                                    "updated_by": "$updated_by",
                                    "created_time": "$created_time",
                                    "updated_time": "$updated_time",
                                    "name": "$name",
                                    "description": "$description",
                                    "source_key": "$source_key",
                                    "source_type": "$source_type",
                                    "config_sync_calendar": "$config_sync_calendar",
                                    "config_mapping_data": "$config_mapping_data",
                                    "status_connect": "$status_connect",
                                    "last_datetime_sync_data": "$last_datetime_sync_data",
                                    "config_information_out": "$config_information_out",
                                },
                                "else": {},
                            }
                        }
                    }
                },
                {
                    "$project": {
                        "_id": 0,
                        "source_key": "$_id.source_key",
                        "source_type": "$_id.source_type",
                        "detail_connector": 1,
                        "count": 1,
                    }
                },
            ]
        )

    def get_connector_by_source_key(self, merchant_id, source_key, search, data_type):
        filter_option = {
            ConstantConfigConnectorsModel.MERCHANT_ID: merchant_id,
            ConstantConfigConnectorsModel.DATA_TYPE: data_type,
            ConstantConfigConnectorsModel.STATUS: ConstantStatusConfigConnector.ACTIVE,
            "source_key": source_key,
        }
        if search:
            search = self._gen_data_build_search_query(search)
            filter_option.update({"keywords": search})

        protection_select = {f: 1 for f in ConstantConfigConnectorsModel.get_all_attribute()}
        protection_select.update(
            {
                "is_trust_source": 1,
                "status_sync": 1,
                "object": 1,
                "name": 1,
                "description": 1,
                "source_key": 1,
                "source_type": 1,
                "config_sync_calendar": 1,
                "config_mapping_data": 1,
                "status_connect": 1,
                "last_datetime_sync_data": 1,
                "config_information_out": 1,
                "object_attribute": 1,
            }
        )

        return self.find(filter_option, protection_select).sort([(ConstantConfigConnectorsModel.UPDATED_TIME, -1)])

    def get_detail_connector_by_ids(self, merchant_id, connector_ids):
        filter_option = {
            "_id": {"$in": connector_ids},
            ConstantConfigConnectorsModel.MERCHANT_ID: merchant_id,
        }
        protection_select = {f: 1 for f in ConstantConfigConnectorsModel.get_all_attribute()}
        protection_select.update(
            {
                "is_trust_source": 1,
                "status_sync": 1,
                "object": 1,
                "name": 1,
                "description": 1,
                "source_key": 1,
                "source_type": 1,
                "config_sync_calendar": 1,
                "config_mapping_data": 1,
                "status_connect": 1,
                "last_datetime_sync_data": 1,
                "config_information_out": 1,
            }
        )

        return self.find(filter_option, protection_select).sort([(ConstantConfigConnectorsModel.UPDATED_TIME, -1)])

    def count_connector_by_source_keys(self, merchant_id, source_keys, search, data_type):
        filter_option = {
            ConstantConfigConnectorsModel.MERCHANT_ID: merchant_id,
            ConstantConfigConnectorsModel.DATA_TYPE: data_type,
            ConstantConfigConnectorsModel.STATUS: ConstantStatusConfigConnector.ACTIVE,
        }
        if search:
            search = self._gen_data_build_search_query(search)
            filter_option.update({"keywords": search})

        query = [
            {"$match": filter_option},
            {"$group": {"_id": "$source_key", "count": {"$sum": 1}}},
            {"$project": {"_id": 0, "source_key": "$_id", "count": 1}},
        ]

        return self.get_collector().aggregate(query)

    def update_status_connect_by_orchestration(self, merchant_id, connector_id, status_connect, reason, status_sync):
        filter_option = {
            "merchant_id": merchant_id,
            "_id": int(connector_id),
            ConstantConfigConnectorsModel.STATUS: ConstantStatusConfigConnector.ACTIVE,
        }

        data_update = {"status_connect": status_connect, "reason_connect": reason, "status_sync": status_sync}

        status_connect = self.update_by_set(filter_option, data_update)
        return status_connect

    def get_connectors_by_condition(
        self,
        merchant_id,
        search,
        is_trust_source,
        status_connect,
        status_sync,
        source_keys,
        source_types,
        created_by,
        order_by,
        sort_by,
        after_token,
        per_page,
        data_type,
        object_attribute,
        status_sync_conn,
        last_time_sync_start_time=None,
        last_time_sync_end_time=None,
    ):

        option_oder = [(sort_by, order_by)]

        filter_option = {
            "merchant_id": merchant_id,
            "data_type": data_type,
            ConstantConfigConnectorsModel.STATUS: ConstantStatusConfigConnector.ACTIVE,
        }

        if search:
            search = self._gen_data_build_search_query(search)
            filter_option.update({"keywords": search})
        if is_trust_source is not None:
            filter_option.update({"is_trust_source": is_trust_source})

        if status_connect:
            status_connect = status_connect.split(",")
            filter_option.update({"status_connect": {"$in": status_connect}})

        if status_sync:
            status_sync = (
                status_sync.replace("running", "on")
                .replace("stopped", "off")
                .replace("stop", "off")
                .replace("error", "failed")
            )
            status_sync_list = status_sync.split(",")

            lst_status_connect = [s for s in status_sync_list if s in ConstantStatusConnect.get_all_attribute()]
            lst_status_connect = list(set(lst_status_connect))
            if "not_sync_in_range_time" in status_sync_list:
                all_status_connect = lst_status_connect + status_connect if status_connect else lst_status_connect
                all_status_connect = list(set(all_status_connect))
                if "off" in status_sync_list:
                    filter_option.update({"status_sync": ConstantStatusSyncData.NOT_DATA_SYNC})
                else:
                    filter_option.update(
                        {
                            "$and": [
                                {"status_sync": ConstantStatusSyncData.NOT_DATA_SYNC},
                                {"status_connect": ConstantStatusConnect.ON},
                            ],
                        },
                    )
                if "status_connect" in filter_option:
                    filter_option.pop("status_connect")
            else:
                filter_option.update({"status_connect": {"$in": lst_status_connect}})

        if source_keys:
            source_keys = source_keys.split(",")
            filter_option.update({"source_key": {"$in": source_keys}})
        if source_types:
            source_types = source_types.split(",")
            filter_option.update({"source_type": {"$in": source_types}})

        if created_by:
            filter_option.update({"created_by": created_by})

        if object_attribute:
            object_attribute = object_attribute.split(",")
            filter_option.update(
                {"$or": [{"object_attribute": {"$in": object_attribute}}, {"object": {"$in": object_attribute}}]}
            )

        if status_sync_conn:
            object_attribute = status_sync_conn.split(",")
            # Xu ly truong hop co ca stop va stopped
            if "stop" in object_attribute:
                object_attribute.append("stopped")
            filter_option.update({"status_sync": {"$in": object_attribute}})
        if last_time_sync_start_time and last_time_sync_end_time:
            filter_option.update(
                {
                    "$and": [
                        {"last_datetime_sync_data": {"$gte": last_time_sync_start_time}},
                        {"last_datetime_sync_data": {"$lte": last_time_sync_end_time}},
                    ]
                }
            )
        data, after_token = self.find_paginate_load_more(
            filter_option, per_page, after_token, option_oder, projection={}
        )
        return data, after_token

    def update_status_connect(self, merchant_id, connector_id, account_id, status_connect, status_sync, sort_time_current):
        filter_option = {
            ConstantConfigConnectorsModel.ID: int(connector_id),
            ConstantConfigConnectorsModel.MERCHANT_ID: merchant_id,
            ConstantConfigConnectorsModel.STATUS: ConstantStatusConfigConnector.ACTIVE,
        }
        sort_time = utils.compute_new_sort_time(datetime.datetime.now(datetime.UTC), sort_time_current)

        data_update = {
            ConstantConfigConnectorsModel.UPDATED_BY: account_id,
            ConstantConfigConnectorsModel.UPDATED_TIME: datetime.datetime.now(datetime.UTC),
            "status_connect": status_connect,
            "status_sync": status_sync,
            "action_time_change_status_connect": datetime.datetime.now(datetime.UTC),
            ConstantConfigConnectorsModel.SORT_TIME: sort_time,
        }

        match_count_update = self.update_one_query(filter_option, data_update)
        return match_count_update

    def get_connectors_by_ids(self, merchant_id, connector_ids):
        filter_option = {
            ConstantConfigConnectorsModel.MERCHANT_ID: merchant_id,
            ConstantConfigConnectorsModel.ID: {"$in": connector_ids},
            ConstantConfigConnectorsModel.STATUS: ConstantStatusConfigConnector.ACTIVE,
        }
        return self.find(filter_option)

    def delete_multi_connector_by_ids(self, merchant_id, connector_ids):
        filter_option = {
            ConstantConfigConnectorsModel.MERCHANT_ID: merchant_id,
            ConstantConfigConnectorsModel.ID: {"$in": connector_ids},
            ConstantConfigConnectorsModel.STATUS: ConstantStatusConfigConnector.ACTIVE,
        }

        update_data = {
            ConstantConfigConnectorsModel.STATUS: ConstantStatusConfigConnector.DISABLED,
        }

        return self.update_by_set(filter_option, update_data)

    def count_connector_use_app_by_app_ids(self, merchant_id, app_ids):
        pipeline = [
            {
                "$match": {
                    ConstantConfigConnectorsModel.MERCHANT_ID: merchant_id,
                    "config_connect.app_id": {"$in": app_ids},
                    ConstantConfigConnectorsModel.STATUS: ConstantStatusConfigConnector.ACTIVE,
                }
            },
            {
                "$group": {
                    "_id": {"app_id": "$config_connect.app_id", "data_type": "$data_type"},
                    "total": {"$sum": 1},
                },
            },
        ]
        return self.get_collector().aggregate(pipeline)

    def get_list_connector_use_app_by_app_id(self, merchant_id, app_id, data_type: str | list, page, per_page):
        query = {
            ConstantConfigConnectorsModel.MERCHANT_ID: merchant_id,
            f"{ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT}.{ConstantParamApi.BodyWebhookCheckConnection.CONFIG_APP_ID}": app_id,
            ConstantConfigConnectorsModel.STATUS: ConstantStatusConfigConnector.ACTIVE,
        }
        if isinstance(data_type, list):
            query.update({ConstantParamApi.BodyCreateConnectors.DAT_TYPE: {"$in": data_type}})
        else:
            query.update({ConstantParamApi.BodyCreateConnectors.DAT_TYPE: data_type})
        projection = {
            ConstantConfigConnectorsModel.ID: 1,
            ConstantParamApi.BodyDatabaseCheckConnection.SOURCE_KEY: 1,
            ConstantParamApi.BodyDatabaseCheckConnection.SOURCE_TYPE: 1,
            ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT: 1,
            ConstantParamApi.BodyUpdateConnectors.CONFIG_INFORMATION_OUT: 1,
            ConstantParamApi.BodyUpdateConnectors.IS_TRUST_SOURCE: 1,
            ConstantParamApi.BodyCreateConnectors.NAME: 1,
            ConstantConfigConnectorsModel.CREATED_TIME: 1,
            "status_connect": 1,
            "object": 1,
            ConstantParamApi.BodyCreateConnectors.DAT_TYPE: 1,
            "status_sync": 1,
        }
        total = self.count_by_query(query)
        list_connector = self.find_paginate(search_option=query, page=page, per_page=per_page, projection=projection)
        result = [x for x in list_connector]
        return result, total

    def get_connector_by_connector_name(self, merchant_id, connector_name, ignore_connector_id=None):
        filter_option = {
            ConstantConfigConnectorsModel.MERCHANT_ID: merchant_id,
            "name_ascii": connector_name,
            ConstantConfigConnectorsModel.STATUS: ConstantStatusConfigConnector.ACTIVE,
        }
        if ignore_connector_id:
            filter_option["_id"] = {"$ne": int(ignore_connector_id)}
        return self.find_one(filter_option)

    def get_connector_by_integration_account_id(self, merchant_id, integration_account_id):
        filter_option = {
            ConstantConfigConnectorsModel.MERCHANT_ID: merchant_id,
            "config_connect.integration_account_id": integration_account_id,
            ConstantConfigConnectorsModel.STATUS: ConstantStatusConfigConnector.ACTIVE,
            "status_connect": "on",
        }
        return self.find_one(filter_option)

    def get_total_connectors_by_condition(
        self,
        merchant_id,
        search,
        is_trust_source,
        status_connect,
        status_sync,
        source_keys,
        source_types,
        created_by,
        data_type,
        object_attribute,
        status_sync_conn,
        last_time_sync_start_time,
        last_time_sync_end_time,
    ):

        filter_option = {
            "merchant_id": merchant_id,
            "data_type": data_type,
            ConstantConfigConnectorsModel.STATUS: ConstantStatusConfigConnector.ACTIVE,
        }

        if search:
            search = self._gen_data_build_search_query(search)
            filter_option.update({"keywords": search})
        if is_trust_source is not None:
            filter_option.update({"is_trust_source": is_trust_source})

        if status_connect:
            status_connect = status_connect.split(",")
            filter_option.update({"status_connect": {"$in": status_connect}})

        if status_sync:
            status_sync = (
                status_sync.replace("running", "on")
                .replace("stopped", "off")
                .replace("stop", "off")
                .replace("error", "failed")
            )
            status_sync_list = status_sync.split(",")

            lst_status_connect = [s for s in status_sync_list if s in ConstantStatusConnect.get_all_attribute()]
            lst_status_connect = list(set(lst_status_connect))
            filter_option.update({"status_connect": {"$in": lst_status_connect}})

        if source_keys:
            source_keys = source_keys.split(",")
            filter_option.update({"source_key": {"$in": source_keys}})
        if source_types:
            source_types = source_types.split(",")
            filter_option.update({"source_type": {"$in": source_types}})

        if created_by:
            filter_option.update({"created_by": created_by})

        if object_attribute:
            object_attribute = object_attribute.split(",")
            filter_option.update(
                {"$or": [{"object_attribute": {"$in": object_attribute}}, {"object": {"$in": object_attribute}}]}
            )

        if status_sync_conn:
            object_attribute = status_sync_conn.split(",")
            # Xu ly truong hop co ca stop va stopped
            if "stop" in object_attribute:
                object_attribute.append("stopped")
            filter_option.update({"status_sync": {"$in": object_attribute}})
        if last_time_sync_start_time and last_time_sync_end_time:
            filter_option.update(
                {
                    "$and": [
                        {"last_datetime_sync_data": {"$gte": last_time_sync_start_time}},
                        {"last_datetime_sync_data": {"$lte": last_time_sync_end_time}},
                    ]
                }
            )

        data = self.count(filter_option)
        return data

