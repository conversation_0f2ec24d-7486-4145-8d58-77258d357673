#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 30/03/2024
"""
from src.models import BaseModel


class SourceModel(BaseModel):
    data_init = [
        {
            "name": "PostgreSQL",
            "key": "postgres",
            "type": "databases",
            "detail_type": "sql",
            "icon": "",
            "description": "",
            "order": 0,
            "data_type": "data_in",
        },
        {
            "name": "Webhook",
            "key": "webhooks",
            "type": "server",
            "detail_type": "",
            "icon": "",
            "description": "",
            "order": 0,
            "data_type": "data_out",
        },
        {
            "name": "Google Sheet",
            "key": "google_sheet",
            "type": "raw_data",
            "detail_type": "",
            "icon": "",
            "description": "",
            "order": 0,
            "data_type": "data_out",
        },
        {
            "name": "MySQL",
            "key": "mysql",
            "type": "databases",
            "detail_type": "sql",
            "icon": "",
            "description": "",
            "order": 0,
            "data_type": "data_in",
        },
        {
            "name": "Oracle",
            "key": "oracle",
            "type": "databases",
            "detail_type": "sql",
            "icon": "",
            "description": "",
            "order": 0,
            "data_type": "data_in",
        },
        {
            "name": "HTTP API",
            "key": "api",
            "type": "server",
            "detail_type": "",
            "icon": "",
            "description": "",
            "order": 0,
            "data_type": "data_in",
        },
        {
            "name": "Contact Form",
            "key": "contact_form",
            "type": "form",
            "detail_type": "",
            "icon": "",
            "description": "",
            "order": 0,
            "data_type": "data_in",
        },
        {
            "name": "IBM DB2",
            "key": "db2",
            "type": "databases",
            "detail_type": "sql",
            "icon": "",
            "description": "",
            "order": 0,
            "data_type": "data_in",
        },
        {
            "name": "SQL Server",
            "key": "sqlserver",
            "type": "databases",
            "detail_type": "sql",
            "icon": "",
            "description": "",
            "order": 0,
            "data_type": "data_in",
        },
    ]

    def __init__(self):
        super().__init__()
        self.collection_name = "data_flow_source"

    def init_source(self):
        for data in self.data_init:
            data_type = data.get("type")
            data_key = data.get("key")
            data_config_type = data.get("data_type")
            if not self.find_one({"key": data_key, "type": data_type, "data_type": data_config_type}):
                self.insert_document(data)
            else:
                self.update_by_set(
                    {"key": data_key, "type": data_type, "data_type": data_config_type},
                    data,
                    upsert=True,
                )
        return self.data_init

    def get_list_source_by_type_key(self, source_type, source_key, search, data_type):
        filter_option = {"data_type": data_type}
        if source_type and source_type != "all":
            filter_option.update({"type": {"$in": source_type}})
        if source_key:
            filter_option["key"] = {"$in": source_key}
        if search:
            search = self._gen_data_build_search_query(search)
            filter_option.update({"name": search})
        sources = self.find(filter_option)
        sources = [*sources]
        if not sources:
            self.init_source()
        sources = self.find(filter_option)
        return sources

    def get_name_source_type(self, data_type, source_key, language):
        detail_source_type = self.find_one({"data_type": data_type, "key": source_key})
        if detail_source_type:
            name_source_type = detail_source_type.get("name", {})
            return name_source_type
        return None

    def delete_source_by_key(self, source_key):
        return self.delete_one({"key": source_key})
