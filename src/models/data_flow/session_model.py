#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 28/03/2024
"""
import datetime

from src.common.i_base_class import IBaseClass
from src.models import BaseModel
from mobio.libs.logging import MobioLogging

class ConstantSessionModel(IBaseClass):
    ID = "_id"
    MERCHANT_ID = "merchant_id"
    CONNECTOR_ID = "connector_id"
    SESSION_ID = "session_id"
    MODE = "mode"
    SCHEDULE_CONFIG_TYPE = "schedule_config_type"
    AGENT = "agent"
    CREATED_TIME = "created_time"
    UPDATED_TIME = "updated_time"



class ConfigSessionModel(BaseModel):
    def __init__(self):
        super().__init__()
        self.collection_name = "session"

    def insert_new_session(self, **obj_insert):
        time_now = datetime.datetime.now(datetime.UTC)
        data_insert = {
            **obj_insert,
            ConstantSessionModel.CREATED_TIME: time_now,
            ConstantSessionModel.UPDATED_TIME: time_now,
        }
        MobioLogging().info("session model:: new session :: {}".format(data_insert))
        self.insert_document(data_insert)
        return data_insert
    
    
    def detail_session_by_id(self, merchant_id, session_id):
        filter_option = {
            ConstantSessionModel.MERCHANT_ID: merchant_id,
            ConstantSessionModel.SESSION_ID: int(session_id)
        }
        return self.find_one(filter_option)
    
    def update_session_by_id(self, id, update_option):
        update_option[ConstantSessionModel.UPDATED_TIME] = datetime.datetime.now(datetime.UTC)
        dictionary = {"$set": update_option}
        return self.update_dictionary(id, dictionary)
