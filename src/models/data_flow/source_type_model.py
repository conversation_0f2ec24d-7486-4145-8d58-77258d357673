#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 30/03/2024
"""
from src.models import BaseModel


class SourceTypeModel(BaseModel):
    data_init = [
        {
            "key": "all",
            "name": {"vi": "Tất cả", "en": "All"},
            "data_type": "data_in",
            "order": 0,
        },
        {
            "key": "databases",
            "name": {"vi": "Databases", "en": "Databases"},
            "data_type": "data_in",
            "order": 1,
        },
        {
            "key": "server",
            "name": {"vi": "Server", "en": "Server"},
            "data_type": "data_in",
            "order": 2,
        },
        {
            "key": "message_queue",
            "name": {"vi": "Message Queue", "en": "Message Queue"},
            "data_type": "data_in",
            "order": 3,
        },
        {
            "key": "website",
            "name": {"vi": "Website", "en": "Website"},
            "data_type": "data_in",
            "order": 4,
        },
        {
            "key": "form",
            "name": {"vi": "Form", "en": "Form"},
            "data_type": "data_in",
            "order": 5,
        },
        {
            "key": "mobile",
            "name": {"vi": "Mobile", "en": "Mobile"},
            "data_type": "data_in",
            "order": 6,
        },
        {
            "key": "other",
            "name": {"vi": "Khác", "en": "Other"},
            "data_type": "data_in",
            "order": 7,
        },
        {
            "key": "all",
            "name": {"vi": "Tất cả", "en": "All"},
            "data_type": "data_out",
            "order": 0,
        },
        {
            "key": "server",
            "name": {"vi": "Server", "en": "Server"},
            "data_type": "data_out",
            "order": 1,
        },
        {
            "key": "raw_data",
            "name": {"vi": "Raw Data", "en": "Raw Data"},
            "data_type": "data_out",
            "order": 2,
        },
    ]

    def __init__(self):
        super().__init__()
        self.collection_name = "data_flow_source_type"

    def _init_source_type(self):
        self.insert_many(self.data_init)
        return self.data_init

    def upsert_source_type(self):
        for data in self.data_init:
            key = data.get("key")
            data_type = data.get("data_type")
            filter_query = {"key": key, "data_type": data_type}
            if not self.find_one(filter_query):
                self.insert_document(data)
            else:
                self.update_one_query({"key": key, "data_type": data_type}, data)
        return self.data_init

    # @lru_redis_cache.add_for_class(expiration=86400)
    def get_list_source_type(self, data_type):
        source_types = self.find({"data_type": data_type})
        source_types = [*source_types]
        if not source_types:
            self.upsert_source_type()
            source_types = self.find({"data_type": data_type})
            source_types = [*source_types]

        return source_types

    def get_name_source_type(self, data_type, source_key, language):
        detail_source_type = self.find_one({"data_type": data_type, "key": source_key})
        if detail_source_type:
            name_source_type = detail_source_type.get("name", {}).get(language, "")
            return name_source_type
        return None
