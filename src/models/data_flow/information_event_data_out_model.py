import datetime

from src.common.json_encoder import <PERSON><PERSON><PERSON><PERSON><PERSON>
from src.models import BaseModel


class InformationEventDataOutField:
    ID = "_id"
    EVENT_NAME = "name"
    EVENT_KEY = "event_key"
    DESCRIPTION = "description"
    STATUS = "status"
    DATA_TEST = "data_test"
    LINK_DOC = "link_doc"
    GROUP_CODE = "group_code"
    CREATED_TIME = "created_time"
    UPDATED_TIME = "updated_time"
    POSITION = "position"
    TYPE = "type"

    class EventStatus:
        ACTIVE = "active"
        INACTIVE = "inactive"
        STATUS = [ACTIVE, INACTIVE]

    class ConstantType:
        ALL = "ALL"
        BANK = "BANK"
        NON_BANK = "NON_BANK"


class InformationEventDataOutModel(BaseModel):
    def __init__(self):
        super().__init__()
        self.collection_name = "information_event_data_out"

    def get_list_event_by_group(
        self,
        group_code: list,
        status,
        event_type: list,
        selected_field: list = None,
        sort_option=InformationEventDataOutField.CREATED_TIME,
        page=-1,
        order=1,
        per_page=None,
    ):
        projection = {}
        if selected_field:
            projection.update({InformationEventDataOutField.ID: 0})
            for field in selected_field:
                projection.update({field: 1})

        query = {"type": {"$in": event_type}}
        if group_code and isinstance(group_code, list):
            query.update({InformationEventDataOutField.GROUP_CODE: {"$in": group_code}})
        if status:
            query.update({InformationEventDataOutField.STATUS: {"$in": status}})

        events = self.find_paginate(
            search_option=query,
            page=page,
            per_page=per_page,
            projection=projection,
            sort_option=sort_option,
            order=order,
        )
        results = [x for x in events]
        return results

    def get_event_by_key(self, event_key):
        return self.find_one({InformationEventDataOutField.EVENT_KEY: event_key})

    def update_event_status(self, event_key, status):
        update_data = {
            InformationEventDataOutField.STATUS: status,
            InformationEventDataOutField.UPDATED_TIME: datetime.datetime.now(datetime.UTC),
        }
        update_status = self.update_by_set({InformationEventDataOutField.EVENT_KEY: event_key}, update_data)
        return update_status

    @staticmethod
    def serialize_event_data(event_data: dict, selected_fields: list = None):
        result = {}
        if selected_fields and isinstance(selected_fields, list):
            for field in selected_fields:
                result.update({field: event_data.get(field)})
        else:
            result = {
                "_id": event_data["_id"],
                InformationEventDataOutField.EVENT_NAME: event_data.get(InformationEventDataOutField.EVENT_NAME),
                InformationEventDataOutField.EVENT_KEY: event_data.get(InformationEventDataOutField.EVENT_KEY),
                InformationEventDataOutField.STATUS: event_data.get(InformationEventDataOutField.STATUS),
                InformationEventDataOutField.DATA_TEST: event_data.get(InformationEventDataOutField.DATA_TEST),
                InformationEventDataOutField.LINK_DOC: event_data.get(InformationEventDataOutField.LINK_DOC),
                InformationEventDataOutField.CREATED_TIME: event_data.get(InformationEventDataOutField.CREATED_TIME),
                InformationEventDataOutField.GROUP_CODE: event_data.get(InformationEventDataOutField.GROUP_CODE),
                InformationEventDataOutField.POSITION: event_data.get(InformationEventDataOutField.POSITION, 0),
            }
        return JSONEncoder("%Y-%m-%dT%H:%MZ").json_loads(result)
