import datetime

from bson import ObjectId

from src.common import Common
from src.models import BaseModel


class ProcessDataOutModel(BaseModel):

    LIST_FIELD_DATETIME = [
        "request_time",
        "estimate_start_time",
        "estimate_completion_time",
        "actual_start_time",
        "actual_completion_time",
        "created_time",
        "updated_time",
        "date",
    ]

    def __init__(self):
        super().__init__()
        self.collection_name = "process_data_out"

    def create_request(self, merchant_id, request_data):
        time_now = datetime.datetime.now(datetime.UTC)
        request_data["merchant_id"] = merchant_id

        # action info
        request_data["created_time"] = time_now

        return self.insert(request_data)

    def get_request_by_process_data_out_id(self, merchant_id, process_data_out_id, **extra_search_kw):
        filter_option = {
            "merchant_id": merchant_id,
            "_id": ObjectId(process_data_out_id),
            **extra_search_kw,
        }
        return self.find_one(filter_option)

    def get_request(self, merchant_id, request_id, **extra_search_kw):
        filter_option = {
            "merchant_id": merchant_id,
            "request_id": request_id,
            **extra_search_kw,
        }
        return self.find_one(filter_option)

    def _build_query_list_filter(self, merchant_id, filter_input):
        query_filter = {"merchant_id": merchant_id}

        if filter_input.get("source_key"):
            query_filter.update(
                {"source_key": filter_input["source_key"]},
            )
        if filter_input.get("status_process"):
            query_filter.update(
                {"status_process": {"$in": filter_input["status_process"]}},
            )

        if filter_input.get("account_request_ids"):
            query_filter.update(
                {"account_id": {"$in": filter_input["account_request_ids"]}},
            )

        if filter_input.get("condition_request_time"):
            query_filter["request_time"] = {}
            if filter_input["condition_request_time"].get("start_time"):
                start_time_value = datetime.datetime.strptime(
                    filter_input["condition_request_time"]["start_time"], Common.DATE_TIME_FMT_WITHOUT_SECOND_PARSE
                ) + datetime.timedelta(seconds=0)
                query_filter["request_time"].update({"$gte": start_time_value})
            if filter_input["condition_request_time"].get("end_time"):
                end_time_value = datetime.datetime.strptime(
                    filter_input["condition_request_time"]["end_time"], Common.DATE_TIME_FMT_WITHOUT_SECOND_PARSE
                ) + datetime.timedelta(seconds=59)
                query_filter["request_time"].update({"$lte": end_time_value})
        if filter_input.get("area_codes"):
            query_filter.update(
                {"area_code": {"$in": filter_input["area_codes"]}},
            )
        return query_filter

    def get_list_request_by_filter(self, merchant_id, filter, sort_option, after_token, per_page):
        query_filter = self._build_query_list_filter(merchant_id, filter)
        data, after_token = self.find_paginate_load_more(
            search_option=query_filter,
            after_token=after_token,
            per_page=per_page,
            sort_option=sort_option,
            list_field_datetime=self.LIST_FIELD_DATETIME,
        )
        return data, after_token

    def update_by_request_id(self, merchant_id, request_id, data_update):
        time_now = datetime.datetime.now(datetime.UTC)
        filter_option = {
            "merchant_id": merchant_id,
            "request_id": request_id,
        }

        # action info
        data_update["updated_time"] = time_now
        return self.update_by_set(filter_option, data_update)

    def get_total_request_group_by_field(
        self, merchant_id, filter, group_by, order_by=None, order_type=None, paging=None
    ):
        pipeline = []

        # Query pipeline
        query_filter = self._build_query_list_filter(merchant_id, filter)
        pipeline.append({"$match": query_filter})

        # Build group pipeline
        if group_by in ProcessDataOutModel.LIST_FIELD_DATETIME:
            pipeline.append(
                {
                    "$group": {
                        "_id": {"$dateToString": {"format": "%Y-%m-%d", "date": f"${group_by}"}},
                        "number": {"$sum": 1},
                    }
                }
            )
            group_by = "date"
        else:
            pipeline.append({"$group": {"_id": f"${group_by}", "number": {"$sum": 1}}})

        # Projection
        pipeline.append(
            {
                "$project": {
                    "_id": 0,
                    f"{group_by}": "$_id",
                    "number": 1,
                }
            }
        )

        # Sort
        if order_by and order_type:
            pipeline.append({"$sort": {order_by: order_type, group_by: order_type}})

        # Agg
        result = list(self.aggregate(pipeline))
        return result

    def get_total_request_group_by_field_paging(
        self, merchant_id, filter, group_by, order_by, order_type, after_token, per_page
    ):

        next_paging = {
            "cursors": {"after_token": "", "before": after_token},
            "per_page": per_page,
        }
        pipeline = []

        # Query pipeline
        query_filter = self._build_query_list_filter(merchant_id, filter)
        pipeline.append({"$match": query_filter})

        # Build group pipeline
        if group_by in ProcessDataOutModel.LIST_FIELD_DATETIME:
            pipeline.append(
                {
                    "$group": {
                        "_id": {"$dateToString": {"format": "%Y-%m-%d", "date": f"${group_by}"}},
                        "number": {"$sum": 1},
                    }
                }
            )
            group_by = "date"
        else:
            pipeline.append({"$group": {"_id": f"${group_by}", "number": {"$sum": 1}}})

        # Check token
        page_index = 1
        if after_token:
            page_index = ProcessDataOutModel.decode_paging_int_token(after_token)

        # Projection
        pipeline.append(
            {
                "$project": {
                    "_id": 0,
                    f"{group_by}": "$_id",
                    "number": 1,
                }
            }
        )
        # Sort, limit, skip
        pipeline += [
            {"$sort": {order_by: order_type, group_by: order_type}},
            {"$skip": (page_index - 1) * per_page},
            {"$limit": per_page},
        ]

        # Agg
        result = list(self.aggregate(pipeline))
        next_paging["cursors"]["after_token"] = ProcessDataOutModel.generate_paging_int_token(result, page_index + 1, per_page)
        return result, next_paging
