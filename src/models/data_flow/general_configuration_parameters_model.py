#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 28/03/2024
"""
from src.common import lru_redis_cache
from src.common.data_flow_constant import ConstantTypeConfigurationGeneral
from src.models import BaseModel


class GeneralConfigurationParametersModel(BaseModel):
    object_handled_data_init = {
        "data_in": [
            {
                "name": {"vi": "Profile", "en": "Profile"},
                "key": "profiles",
                "status": 1,
                "order": 1,
                "data_type": "data_in",
            },
            {
                "name": {"vi": "Công ty", "en": "Company"},
                "key": "company",
                "status": 0,
                "order": 2,
                "data_type": "data_in",
            },
            {
                "name": {"vi": "Cơ hội bán", "en": "Sale"},
                "key": "sale",
                "status": 0,
                "order": 3,
                "data_type": "data_in",
            },
            {
                "name": {"vi": "Ticket", "en": "Ticket"},
                "key": "ticket",
                "status": 0,
                "order": 4,
                "data_type": "data_in",
            },
        ],
        "data_out": [
            {
                "name": {"vi": "Profile", "en": "Profile"},
                "key": "profile",
                "status": 1,
                "order": 1,
                "data_type": "data_out",
            },
            {
                "name": {"vi": "Omni-channel Journey", "en": "Omni-channel Journey"},
                "key": "journey_builder",
                "status": 1,
                "order": 3,
                "data_type": "data_out",
            },
            {
                "name": {"vi": "Cơ hội bán", "en": "Oppty"},
                "key": "sale",
                "status": 1,
                "order": 4,
                "data_type": "data_out",
            },
            {
                "name": {"vi": "Công việc", "en": "Task"},
                "key": "task",
                "status": 1,
                "order": 6,
                "data_type": "data_out",
            },
            {
                "name": {"vi": "Company", "en": "Company"},
                "key": "company",
                "status": 1,
                "order": 2,
                "data_type": "data_out",
            },
            {
                "name": {"vi": "Ticket", "en": "Ticket"},
                "key": "ticket",
                "status": 1,
                "order": 5,
                "data_type": "data_out",
            },
            {"name": {"vi": "Ghi chú", "en": "Note"}, "key": "note", "status": 1, "order": 7, "data_type": "data_out"},
        ],
    }

    def __init__(self):
        super().__init__()
        self.collection_name = "general_configuration_parameters"

    def upsert_ip_whitelist(self, ip_whitelist):
        data_upsert = {"type": ConstantTypeConfigurationGeneral.IP_WHITELIST, "ip_whitelist": ip_whitelist}
        filter_data = {
            "type": ConstantTypeConfigurationGeneral.IP_WHITELIST,
        }

        self.update_by_set(filter_data, data_upsert, upsert=True)

    def get_ip_whitelist(self):
        record = self.find_one({"type": ConstantTypeConfigurationGeneral.IP_WHITELIST})
        if not record:
            return []
        return record["ip_whitelist"]

    def init_list_source_handled(self, data_type):
        data_insert = {
            "type": ConstantTypeConfigurationGeneral.OBJECT_HANDLED,
            "data_type": data_type,
            "objects": self.object_handled_data_init.get(data_type),
        }
        self.insert_document(data_insert)
        return data_insert

    def upsert_source_handled_all_data_type(self):
        for data_type, values in self.object_handled_data_init.items():

            data_filter = {"type": ConstantTypeConfigurationGeneral.OBJECT_HANDLED, "data_type": data_type}
            self.update_set_dictionary(data_filter, {"objects": values})

    def get_list_source_handled(self, data_type, source_key):
        filter_option = {
            "type": ConstantTypeConfigurationGeneral.OBJECT_HANDLED,
            "data_type": data_type,
            "source_key": source_key,
        }
        detail = self.find_one(filter_option)
        if not detail:
            detail = self.init_list_source_handled(data_type)
        return detail.get("objects", [])

    @lru_redis_cache.add_for_class(expiration=86400)
    def get_list_key_source_handled_active(self, data_type):
        filter_option = {"type": ConstantTypeConfigurationGeneral.OBJECT_HANDLED, "data_type": data_type}
        detail = self.find_one(filter_option)
        if not detail:
            detail = self.init_list_source_handled()
        objects = detail.get("objects", [])
        results = [obj["key"] for obj in objects if obj["status"] == 1]

        return results

    def get_object_handled_name(self, data_type, object_primary, language):
        objects = self.get_list_source_handled(data_type)
        for obj in objects:
            if obj["key"] == object_primary:
                return obj["name"].get(language)
        return None

    def upsert_source_handled(self, data_upsert, data_type):
        data_filter = {"type": ConstantTypeConfigurationGeneral.OBJECT_HANDLED, "data_type": data_type}
        self.update_set_dictionary(data_filter, {"objects": data_upsert})

    def _init_header_default(self):
        data_init = [
            {
                "data_type": "data_out",
                "source_key": "webhooks",
                "type": ConstantTypeConfigurationGeneral.HEADER_DEFAULT,
                "values": [
                    {
                        "key": "X-mevent-signature",
                        "value": "sha256(app_id + data_string + app_secret)",
                        "type": "formula",
                        "created_by": "system",
                    },
                ],
            }
        ]
        for data in data_init:
            if not self.find_one(
                {
                    "data_type": data.get("data_type"),
                    "source_key": data.get("source_key"),
                    "type": ConstantTypeConfigurationGeneral.HEADER_DEFAULT,
                }
            ):
                self.insert_document(data)
            else:
                self.update_by_set(
                    {
                        "data_type": data.get("data_type"),
                        "source_key": data.get("source_key"),
                        "type": ConstantTypeConfigurationGeneral.HEADER_DEFAULT,
                    },
                    data,
                    upsert=True,
                )

    @lru_redis_cache.add_for_class(expiration=86400)
    def get_header_default_by_type(self, data_type, source_key):
        filter_option = {
            "type": ConstantTypeConfigurationGeneral.HEADER_DEFAULT,
            "data_type": data_type,
            "source_key": source_key,
        }
        detail = self.find_one(filter_option)
        if not detail:
            self._init_header_default()
            detail = self.find_one(filter_option)
        values = detail.get("values", [])
        return values


if __name__ == "__main__":
    GeneralConfigurationParametersModel().upsert_source_handled_all_data_type()
