import datetime
import uuid

from mobio.libs.logging import Mo<PERSON>Logging

from src.common.data_flow_constant import ConstantTypeConfigApp
from src.common.i_base_class import IBaseClass
from src.common.json_encoder import <PERSON><PERSON><PERSON><PERSON><PERSON>
from src.common.utils import utf8_to_ascii
from src.models import BaseModel


class ConfigAppField(IBaseClass):
    ID = "_id"
    UID = "id"
    APP_NAME = "name"
    NAME_SEARCH = "name_search"
    SECRET_KEY = "secret_key"
    MERCHANT_ID = "merchant_id"
    CREATED_BY = "created_by"
    UPDATED_BY = "updated_by"
    CREATED_TIME = "created_time"
    UPDATED_TIME = "updated_time"
    IS_NEW_APP = "is_new_app"
    TYPE = "type"

    def generate_projection_from_select_fields(self, select_fields: list):
        projection = {self.ID: 1, self.TYPE: 0}
        for select_field in select_fields:
            projection.update({select_field: 1})
        return projection


class ConfigAppModel(BaseModel):
    def __init__(self):
        super().__init__()
        self.collection_name = "config_app"

    def insert_new_app(self, merchant_id, account_id, **obj_insert):
        time_now = datetime.datetime.now(datetime.UTC)
        data_insert = {
            **obj_insert,
            ConfigAppField.ID: str(uuid.uuid1()),
            ConfigAppField.MERCHANT_ID: merchant_id,
            ConfigAppField.CREATED_BY: account_id,
            ConfigAppField.UPDATED_BY: account_id,
            ConfigAppField.CREATED_TIME: time_now,
            ConfigAppField.UPDATED_TIME: time_now,
            ConfigAppField.IS_NEW_APP: True,
            ConfigAppField.TYPE: ConstantTypeConfigApp.USER_CREATE,
        }
        MobioLogging().info("app_config :: add new app :: {}".format(data_insert))
        self.insert_document(data_insert)
        return data_insert

    def find_list_app(self, merchant_id, name_search, order_by, order_type, page, per_page, selected_field):
        query_params = {ConfigAppField.MERCHANT_ID: merchant_id, ConfigAppField.TYPE: ConstantTypeConfigApp.USER_CREATE}
        if name_search:
            query_params.update({ConfigAppField.NAME_SEARCH: {"$regex": utf8_to_ascii(name_search), "$options": "i"}})
        if selected_field and isinstance(selected_field, list):
            projection = ConfigAppField().generate_projection_from_select_fields(selected_field)
            apps = self.find_paginate(
                query_params,
                page=page,
                per_page=per_page,
                sort_option=order_by,
                projection=projection,
                order=order_type,
            )
        else:
            apps = self.find_paginate(
                query_params, page=page, per_page=per_page, sort_option=order_by, order=order_type
            )
        results = [x for x in apps]
        total_app = self.count_by_query(query_params)
        return results, total_app

    def update_app(self, account_id, obj_query: dict, obj_update: dict, update_time=True):
        update_data = {
            **obj_update,
            ConfigAppField.UPDATED_BY: account_id,
        }
        if update_time:
            update_data.update({ConfigAppField.UPDATED_TIME: datetime.datetime.now(datetime.UTC)})
        update_status = self.update_one_query(obj_query, update_data)
        if not update_status:
            return {}
        return update_data

    def app_detail_by_id(self, app_id):
        return self.find_one({ConfigAppField.ID: app_id})

    def delete_app_by_id(self, app_id):
        return self.delete_one({ConfigAppField.ID: app_id}).deleted_count

    def find_app_name_exists(self, merchant_id, name_search):
        query = {
            ConfigAppField.MERCHANT_ID: merchant_id,
            ConfigAppField.NAME_SEARCH: name_search,
            ConfigAppField.TYPE: ConstantTypeConfigApp.USER_CREATE,
        }
        return self.find_one(query)

    @staticmethod
    def serialize_app_data(app_data: dict, selected_fields: list = None):
        result = {}
        app_data.update({"id": app_data.get(ConfigAppField.ID)})
        if selected_fields and isinstance(selected_fields, list):
            for field in selected_fields:
                result.update({field: app_data.get(field)})
        else:
            result = {
                "id": app_data.get("id"),
                ConfigAppField.APP_NAME: app_data.get(ConfigAppField.APP_NAME),
                ConfigAppField.NAME_SEARCH: app_data.get(ConfigAppField.NAME_SEARCH),
                ConfigAppField.SECRET_KEY: app_data.get(ConfigAppField.SECRET_KEY),
                ConfigAppField.MERCHANT_ID: app_data.get(ConfigAppField.MERCHANT_ID),
                ConfigAppField.CREATED_BY: app_data.get(ConfigAppField.CREATED_BY),
                ConfigAppField.UPDATED_BY: app_data.get(ConfigAppField.UPDATED_BY),
                ConfigAppField.UPDATED_TIME: app_data.get(ConfigAppField.UPDATED_TIME),
                ConfigAppField.CREATED_TIME: app_data.get(ConfigAppField.CREATED_TIME),
                ConfigAppField.IS_NEW_APP: app_data.get(ConfigAppField.IS_NEW_APP),
            }
        return JSONEncoder("%Y-%m-%dT%H:%MZ").json_loads(result)
