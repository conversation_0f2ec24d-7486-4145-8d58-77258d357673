#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 01/04/2025
"""

from src.models import BaseModel


class DailyReportsModel(BaseModel):
    def __init__(self):
        """
        Collection: daily_reports
        Bảng này sẽ tổng hợp báo cáo hàng ngày từ bảng dataflow_log trong starrock
        Cấu trúc bảng:
        {
            "_id": ObjectId,
            "start_time": datetime,
            "end_time": datetime,
            "connector_id": int,
            "session_id": int,
            "status": str,
            "total_rows": int,
            "total_rows_success": int,
            "total_rows_error": int,
            "total_rows_updated": int,
            "total_rows_find": int,
            "total_rows_add": int,
            "mode": str,
            "<object>_total_rows": int,
            "<object>_total_rows_success": int,
            "<object>_total_rows_error": int,
            "<object>_total_rows_updated": int,
            "<object>_total_rows_find": int,
            "<object>_total_rows_add": int,
        }
        """
        super().__init__()
        self.collection_name = "daily_reports"

    def list_session_by_condition(self, filter_options, per_page, before_token, order_by, sort_by):
        """
        Lấy danh sách session theo điều kiện
        """
        option_order = [(sort_by, order_by)]

        data, after_token = self.find_paginate_load_more(
            filter_options, per_page, before_token, option_order, projection={}
        )
        return data, after_token

    def streaming_history_pipeline_list(self, filter_options, per_page, before_token, order_by, sort_by):
        """
        Lấy danh sách session theo điều kiện
        """
        skip_number = 0
        page_index = 0
        parse_token = self.parse_token(before_token)
        if parse_token:
            page_index = parse_token.get("page_index", 0)
            skip_number = int(page_index) * int(per_page)

        sort_option = {sort_by: order_by}

        pipeline_aggregate = [
            {"$match": filter_options},
            {
                "$group": {
                    "_id": {
                        "$dateToString": {"format": "%Y-%m-%d 00:00:00", "date": "$start_time", "timezone": "+07:00"}
                    },
                    "total_company_add": {"$sum": "$company_total_rows_add"},
                    "total_company_update": {"$sum": "$company_total_rows_updated"},
                    "total_event_done": {"$sum": "$dynamic_event_total_rows_success"},
                    "total_event_fail": {"$sum": "$dynamic_event_total_rows_error"},
                    "total_process_done": {"$sum": "$total_rows_success"},
                    "total_process_fail": {"$sum": "$total_rows_error"},
                    "total_product_holding_add": {"$sum": "$product_holding_total_rows_add"},
                    "total_product_holding_done": {"$sum": "$product_holding_total_rows_success"},
                    "total_product_holding_fail": {"$sum": "$product_holding_total_rows_error"},
                    "total_product_holding_update": {"$sum": "$product_holding_total_rows_updated"},
                    "total_profile_add": {"$sum": "$profile_total_rows_add"},
                    "total_profile_update": {"$sum": "$profile_total_rows_updated"},
                    "total_sale_add": {"$sum": "$deal_total_rows_add"},
                    "total_sale_update": {"$sum": "$deal_total_rows_updated"},
                    "total_ticket_add": {"$sum": "$ticket_total_rows_add"},
                    "total_ticket_update": {"$sum": "$ticket_total_rows_updated"},
                    "total_rows": {"$sum": "$total_rows"},
                    "total_rows_success": {"$sum": "$total_rows_success"},
                    "total_rows_error": {"$sum": "$total_rows_error"},
                }
            },
            {"$sort": sort_option},
            {
                "$project": {
                    "_id": 0,
                    "date": "$_id",
                    "total_company_add": 1,
                    "total_company_update": 1,
                    "total_event_done": 1,
                    "total_event_fail": 1,
                    "total_process_done": 1,
                    "total_process_fail": 1,
                    "total_product_holding_add": 1,
                    "total_product_holding_done": 1,
                    "total_product_holding_fail": 1,
                    "total_product_holding_update": 1,
                    "total_profile_add": 1,
                    "total_profile_update": 1,
                    "total_sale_add": 1,
                    "total_sale_update": 1,
                    "total_ticket_add": 1,
                    "total_ticket_update": 1,
                    "total_rows": 1,
                    "total_wait_process": {
                        "$subtract": ["$total_rows", {"$add": ["$total_rows_success", "$total_rows_error"]}]
                    },
                }
            },
            {"$skip": skip_number},
            {"$limit": per_page},
        ]

        data = self.aggregate(pipeline_aggregate)
        return data, self.generate_after_token_by_page_index(page_index + 1)

    def streaming_history_total_pipeline(self, filter_options):
        """
        Lấy danh sách session theo điều kiện
        """

        pipeline_aggregate = [
            {"$match": filter_options},
            {
                "$group": {
                    "_id": {"$dateToString": {"format": "%Y-%m-%d", "date": "$start_time", "timezone": "+07:00"}},
                    "total_process_done": {"$sum": "$total_rows_success"},
                    "total_process_fail": {"$sum": "$total_rows_error"},
                }
            },
            {
                "$project": {
                    "_id": 0,
                    "date": "$_id",
                    "total_process_done": 1,
                    "total_process_fail": 1,
                }
            },
        ]

        data = self.aggregate(pipeline_aggregate)
        return data

    def streaming_history_result_from_source(self, filter_options):
        """
        Chỉ tính profile
        """
        pipeline_aggregate = [
            {"$match": filter_options},
            {
                "$group": {
                    "_id": None,
                    "profile_add_count": {"$sum": "$profile_total_rows_add"},
                    "total_rows_error": {"$sum": "$total_rows_error"},
                    "total_rows_success": {"$sum": "$total_rows_success"},
                    "total_rows": {"$sum": "$total_rows"},
                }
            },
            {
                "$project": {
                    "_id": 0,
                    "profile_add_count": 1,
                    "total_rows_error": 1,
                    "total_rows_success": 1,
                    "total_rows": 1,
                    "total_wait_process": {
                        "$subtract": ["$total_rows", {"$add": ["$total_rows_success", "$total_rows_error"]}]
                    },
                }
            },
        ]

        data = self.aggregate(pipeline_aggregate)
        return data

    def check_mode_streaming_to_batch(self, connector_id):
        filter_option = {
            "connector_id": int(connector_id),
            "mode": "streaming",
        }
        data = self.find_one(filter_option)
        return True if data else False

    def aggregate_streaming_to_batch(self, filter_options):
        pipeline_aggregate = [
            {"$match": filter_options},
            {
                "$group": {
                    "_id": None,
                    "total_new_profile": {"$sum": "$profile_total_rows_add"},
                    "total_process_fail": {"$sum": "$total_rows_error"},
                    "total_process_done": {"$sum": "$total_rows_success"},
                    "total_row": {"$sum": "$total_rows"},
                    "start_time": {"$min": "$start_time"},
                    "end_time": {"$max": "$end_time"},
                }
            },
            {
                "$project": {
                    "_id": 0,
                    "total_new_profile": 1,
                    "total_process_fail": 1,
                    "total_process_done": 1,
                    "total_row": 1,
                    "start_time": 1,
                    "end_time": 1,
                    "total_wait_process": {
                        "$subtract": ["$total_row", {"$add": ["$total_process_done", "$total_process_fail"]}]
                    },
                }
            },
        ]
        data = self.aggregate(pipeline_aggregate)
        return data

    def streaming_history_result_by_object_event(self, filter_options):
        """
        Chỉ tính profile
        """
        pipeline_aggregate = [
            {"$match": filter_options},
            {
                "$group": {
                    "_id": {
                        "$dateToString": {"format": "%Y-%m-%d 00:00:00", "date": "$start_time", "timezone": "+07:00"}
                    },
                    "profile_add_count": {"$sum": "$profile_total_rows_add"},
                    "dynamic_event_total_rows_success": {"$sum": "$dynamic_event_total_rows_success"},
                    "dynamic_event_total_rows_error": {"$sum": "$dynamic_event_total_rows_error"},
                    "product_holding_total_rows_success": {"$sum": "$product_holding_total_rows_success"},
                    "product_holding_total_rows_error": {"$sum": "$product_holding_total_rows_error"},
                }
            },
            {
                "$project": {
                    "_id": 0,
                    "date": "$_id",
                    "profile_add_count": 1,
                    "dynamic_event_total_rows_success": 1,
                    "dynamic_event_total_rows_error": 1,
                    "product_holding_total_rows_success": 1,
                    "product_holding_total_rows_error": 1,
                }
            },
        ]

        data = self.aggregate(pipeline_aggregate)
        return data

    def streaming_history_result_by_object(self, filter_options):
        """
        Chỉ tính profile
        """
        pipeline_aggregate = [
            {"$match": filter_options},
            {
                "$group": {
                    "_id": {"$dateToString": {"format": "%Y-%m-%d", "date": "$start_time", "timezone": "+07:00"}},
                    "profile_total_rows_add": {"$sum": "$profile_total_rows_add"},
                    "profile_total_rows_updated": {"$sum": "$profile_total_rows_updated"},
                    "deal_total_rows_add": {"$sum": "$deal_total_rows_add"},
                    "deal_total_rows_updated": {"$sum": "$deal_total_rows_updated"},
                    "ticket_total_rows_add": {"$sum": "$ticket_total_rows_add"},
                    "ticket_total_rows_updated": {"$sum": "$ticket_total_rows_updated"},
                    "product_holding_total_rows_add": {"$sum": "$product_holding_total_rows_add"},
                    "product_holding_total_rows_updated": {"$sum": "$product_holding_total_rows_updated"},
                    "company_total_rows_add": {"$sum": "$company_total_rows_add"},
                    "company_total_rows_updated": {"$sum": "$company_total_rows_updated"},
                    "dynamic_event_total_rows_success": {"$sum": "$dynamic_event_total_rows_success"},
                    "dynamic_event_total_rows_error": {"$sum": "$dynamic_event_total_rows_error"},
                }
            },
            {
                "$project": {
                    "_id": 0,
                    "date": "$_id",
                    "profile_total_rows_add": 1,
                    "profile_total_rows_updated": 1,
                    "deal_total_rows_add": 1,
                    "deal_total_rows_updated": 1,
                    "ticket_total_rows_add": 1,
                    "ticket_total_rows_updated": 1,
                    "product_holding_total_rows_add": 1,
                    "product_holding_total_rows_updated": 1,
                    "company_total_rows_add": 1,
                    "company_total_rows_updated": 1,
                    "dynamic_event_total_rows_success": 1,
                    "dynamic_event_total_rows_error": 1,
                }
            },
        ]

        data = self.aggregate(pipeline_aggregate)
        return data

    def detail_total_streaming_prev_snapshot(self, merchant_id, connector_id):
        filter_option = {
            "connector_id": int(connector_id),
            "mode": "streaming",
        }
        pipeline_aggregate = [
            {"$match": filter_option},
            {
                "$group": {
                    "_id": None,
                    "profile_total_rows_add": {"$sum": "$profile_total_rows_add"},
                    "total_rows_error": {"$sum": "$total_rows_error"},
                    "total_process_done": {"$sum": "$total_rows_success"},
                    "total_rows": {"$sum": "$total_rows"},
                    "start_time": {"$min": "$start_time"},
                    "end_time": {"$max": "$end_time"},
                    "profile_total_rows_error": {"$sum": "$profile_total_rows_error"},
                    "profile_total_rows_success": {"$sum": "$profile_total_rows_success"},
                    "company_total_rows_error": {"$sum": "$company_total_rows_error"},
                    "company_total_rows_success": {"$sum": "$company_total_rows_success"},
                    "ticket_total_rows_error": {"$sum": "$ticket_total_rows_error"},
                    "ticket_total_rows_success": {"$sum": "$ticket_total_rows_success"},
                    "sale_total_rows_error": {"$sum": "$sale_total_rows_error"},
                    "sale_total_rows_success": {"$sum": "$sale_total_rows_success"},
                    "product_holding_total_rows_error": {"$sum": "$product_holding_total_rows_error"},
                    "product_holding_total_rows_success": {"$sum": "$product_holding_total_rows_success"},
                    "product_holding_total_rows_add": {"$sum": "$product_holding_total_rows_add"},
                    "product_holding_total_rows_updated": {"$sum": "$product_holding_total_rows_updated"},
                    "dynamic_event_total_rows_error": {"$sum": "$dynamic_event_total_rows_error"},
                    "dynamic_event_total_rows_success": {"$sum": "$dynamic_event_total_rows_success"},
                    "dynamic_event_total_rows_add": {"$sum": "$dynamic_event_total_rows_add"},
                    "dynamic_event_total_rows_updated": {"$sum": "$dynamic_event_total_rows_updated"},
                    "total_rows_success": {"$sum": "$total_rows_success"},
                }
            },
            {
                "$project": {
                    "_id": 0,
                    "profile_total_rows_add": 1,
                    "total_rows_error": 1,
                    "total_process_done": 1,
                    "total_rows": 1,
                    "start_time": 1,
                    "end_time": 1,
                    "process_status": "finished",
                    "total_wait_process": {
                        "$subtract": ["$total_rows", {"$add": ["$total_process_done", "$total_rows_error"]}]
                    },
                    "profile_total_rows_error": 1,
                    "profile_total_rows_success": 1,
                    "company_total_rows_error": 1,
                    "company_total_rows_success": 1,
                    "ticket_total_rows_error": 1,
                    "ticket_total_rows_success": 1,
                    "sale_total_rows_error": 1,
                    "sale_total_rows_success": 1,
                    "product_holding_total_rows_error": 1,
                    "product_holding_total_rows_success": 1,
                    "product_holding_total_rows_add": 1,
                    "product_holding_total_rows_updated": 1,
                    "dynamic_event_total_rows_error": 1,
                    "dynamic_event_total_rows_success": 1,
                    "dynamic_event_total_rows_add": 1,
                    "dynamic_event_total_rows_updated": 1,
                    "total_rows_success": 1,
                }
            },
        ]
        data = [*self.aggregate(pipeline_aggregate)]
        if data:
            data = data[0]
        return data
