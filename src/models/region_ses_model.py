from src.models import BaseModel

class RegionSesField:
    ID = "_id"
    REGION = "region"


class RegionDataField:
    REGION_CODE = "region_code"
    REGION_NAME = "region_name"


class RegionSesModel(BaseModel):

    def __init__(self):
        super().__init__()
        self.collection_name = "region_ses"


    def create_region_ses_config(self, search_option, payload):
        return self.upsert(search_option=search_option, dictionary=payload)


    def get_region_ses_config(self):
        return self.find_one({})

