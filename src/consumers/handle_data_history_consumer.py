#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 06/09/2024
"""
import copy
from datetime import datetime, timedelta

from mobio.libs.logging import MobioLogging
from mobio.sdks.notify import <PERSON>bioNotifySDK

from src.common import ConstantNM, ConstantThirdPartyConfigStatus
from src.helpers.confluent_kafka_base import ConfluentKafkaConsumer
from src.internal_module.admin import AdminInternal
from src.internal_module.data_out import DataOutHelper
from src.internal_module.nm import NMHelper
from src.models.connect_config_model import AppConnectType, ConnectConfigModel
from src.models.data_flow.config_connectors_model import ConfigConnectorsModel
from src.models.data_flow.object_handle_model import SettingObjectHandleModel
from src.models.data_flow.source_model import SourceModel
from src.models.data_history_model import DataHistoryModel
from src.models.third_party_config_model import (
    ThirdPartyConfigField,
    ThirdPartyConfigModel,
)

mobio_notify_sdk = MobioNotifySDK().config(
    source="market-place"  # source module (nguồn gửi từ module nào) (Ex: 'sale', 'admin')
)


class HandleDataHistoryConsumer(ConfluentKafkaConsumer):

    def __init__(self, topic_name, group_id):
        super().__init__(topic_name, group_id)

    def process_msg(self, payload):
        MobioLogging().info("HandleDataHistoryConsumer :: payload :: %s" % payload)
        HandleDataHistoryConsumer.handler_record(payload)

    @staticmethod
    def handler_record(message):
        action = message.get("action")
        data_before = message.get("data_before")
        data_after = message.get("data_after")
        merchant_id = message.get("merchant_id")
        connect_config_id = message.get("connect_config_id")
        webpush_config_id = message.get("webpush_config_id")

        record_type = message.get("type")

        connect_config_information = {}
        if isinstance(connect_config_id, str) and not connect_config_id.isdigit():
            connect_config_information = ConnectConfigModel().get_current_config(message.get("connect_config_id"))
        else:
            connect_config_id = int(connect_config_id)
        if action == "CREATE":
            data_after.update({ThirdPartyConfigField.CONNECT_INFORMATION_CONFIG: connect_config_information})
        elif action == "DELETE":
            data_before.update({ThirdPartyConfigField.CONNECT_INFORMATION_CONFIG: connect_config_information})
        elif action == "UPDATE":
            data_before.update({ThirdPartyConfigField.CONNECT_INFORMATION_CONFIG: connect_config_information})
            data_after.update({ThirdPartyConfigField.CONNECT_INFORMATION_CONFIG: connect_config_information})
        message.update({"data_before": data_before, "data_after": data_after, "connect_config_id": connect_config_id})
        id_history = DataHistoryModel().insert_document(message).inserted_id
        MobioLogging().info("HandleDataHistoryConsumer :: id_history :: %s" % id_history)
        if record_type == "data_flow":
            mapping_action = {
                "CREATE": "add_connector",
                "DELETE": "delete_connector",
                "UPDATE": "edit_connector",
            }
            HandleDataHistoryConsumer.handle_history_change_type_data_flow(message)

            data_get_connector = {}
            if data_before and data_after:
                data_get_connector = copy.deepcopy(data_before)
                data_get_connector.update(copy.deepcopy(data_after))
            elif data_before:
                data_get_connector = copy.deepcopy(data_before)
            elif data_after:
                data_get_connector = copy.deepcopy(data_after)

            if data_get_connector:
                data_type = data_get_connector.get("data_type")
                source_key = data_get_connector.get("source_key")
                source_type = data_get_connector.get("source_type")
                if (
                    (data_type and data_type == "data_out")
                    and (source_key and source_key == "webhooks")
                    and (source_type and source_type == "server")
                ):
                    config_connect = data_get_connector.get("config_connect")
                    if config_connect and config_connect.get("app_id") and action.upper() in mapping_action:
                        HandleDataHistoryConsumer.push_trigger_change_config_data_out(
                            merchant_id,
                            config_connect.get("app_id"),
                            mapping_action[action.upper()],
                            connect_config_id,
                        )

        elif record_type == "webpush":
            HandleDataHistoryConsumer.handle_history_change_type_webpush(merchant_id, webpush_config_id)
        return

    @staticmethod
    def handle_history_change_type_data_flow(message):
        data_before = message.get("data_before")
        merchant_id = message.get("merchant_id")
        data_after = message.get("data_after")
        connect_config_id = message.get("connect_config_id")
        account_id = message.get("account_id")

        action_time = message.get("time_action")

        if data_before and data_after:
            if not account_id:
                account_id = data_after.get("updated_by")
            before_status_connect = data_before.get("status_connect")
            after_status_connect = data_after.get("status_connect")

            if before_status_connect == "on" and after_status_connect == "off":
                detail_connector = ConfigConnectorsModel().detail_connector_by_id(merchant_id, int(connect_config_id))
                if not detail_connector:
                    MobioLogging().error("Not exits connector with id {}".format(connect_config_id))
                    return
                connector_name = detail_connector.get("name")
                source_key = detail_connector.get("source_key")
                config_sync_calendar = detail_connector.get("config_sync_calendar")

                if not config_sync_calendar:
                    MobioLogging().error("Could not find config_sync_calendar")
                    return
                is_notification = config_sync_calendar.get("is_notification")
                if not is_notification:
                    MobioLogging().error("Could not find is_notification")
                    return

                lst_email_send = []
                lst_account_id_send = []
                contact_info = config_sync_calendar.get("contact_info")
                for contact in contact_info:
                    if contact.get("type") == "email":
                        lst_email_send.extend(contact.get("values"))
                    elif contact.get("type") == "account_id":
                        lst_account_id_send.extend(contact.get("values"))
                if not lst_email_send and not lst_account_id_send:
                    MobioLogging().error("Could not find contact_info")
                    return

                account_name = "Hệ thống"

                if account_id:
                    detail_account = AdminInternal.get_detail_user_information_by_account_id(merchant_id, account_id)
                    if detail_account:
                        account_name = "{} ({})".format(
                            detail_account.get("fullname", ""), detail_account.get("email", "")
                        )

                data_type = detail_connector.get("data_type")
                source_key = detail_connector.get("source_key")

                object_primary = detail_connector.get("object")

                object_primary_name = SettingObjectHandleModel().get_object_handled_name(
                    merchant_id, data_type, object_primary, "vi", source_key
                )

                connector_type = SourceModel().get_name_source_type(data_type, source_key, "vi")

                # Convert string to datetime object
                date_obj = datetime.strptime(action_time, "%Y-%m-%dT%H:%MZ") + timedelta(hours=7)

                # Format the datetime object to desired format
                format_datetime_action = date_obj.strftime("%H:%M %d/%m/%Y")
                mobio_notify_sdk.send_message_notify_email(
                    merchant_id=merchant_id,
                    key_config="market_place_disconnect_data_sync_process_result",
                    account_ids=lst_account_id_send,
                    other_emails=lst_email_send,
                    module_name=object_primary_name,
                    connector_name=connector_name,
                    connector_type=connector_type,
                    updated_by=account_name,
                    disconnect_time=format_datetime_action,
                )

    @staticmethod
    def handle_history_change_type_webpush(merchant_id, webpush_config_id):
        third_party_config = ThirdPartyConfigModel().find_by_id(merchant_id, webpush_config_id)

        if not third_party_config:
            MobioLogging().error("handle_history_change_type_webpush :: not connect_config_information")
            return
        merchant_id = third_party_config.get("merchant_id")
        connect_information_config_id = third_party_config.get(ThirdPartyConfigField.CONNECT_INFORMATION_CONFIG_ID)
        if not connect_information_config_id:
            MobioLogging().error("handle_history_change_type_webpush :: not connect_information_config_id")
            return
        connect_information_config_detail = ConnectConfigModel().get_current_config_by_type(
            connect_information_config_id, AppConnectType.FIREBASE
        )
        if not connect_information_config_detail:
            MobioLogging().error(
                "handle_history_change_type_webpush :: not connect_information_config_detail by id :: {}".format(
                    connect_information_config_id
                )
            )
            return
        domain = third_party_config.get("website_url")
        status_config_nm = ConstantNM.StatusConfig.DISABLED
        status_third_party_config = third_party_config.get("status")
        information_create = third_party_config.get("information_create")
        if status_third_party_config == ConstantThirdPartyConfigStatus.ENABLE and not information_create:
            status_config_nm = ConstantNM.StatusConfig.ENABLE

        status_sync_nm = NMHelper.update_config_push_firebase_nm(
            merchant_id, connect_information_config_detail, domain, status_config_nm
        )
        MobioLogging().info("handle_history_change_type_webpush :: status_sync_nm :: {}".format(status_sync_nm))

    @staticmethod
    def push_trigger_change_config_data_out(merchant_id, app_id, type_change, connector_id):
        data_send = {
            "app_id": app_id,
            "type_change": type_change,
            "connector_ids": [connector_id],
        }
        DataOutHelper.change_config_data_out(merchant_id, data_send)


if __name__ == "__main__":
    from datetime import datetime

    data_send = {
        "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
        "account_id": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
        "connect_config_id": "2880",
        "type": "data_flow",
        "data_before": {
            "_id": 2880,
            "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
            "created_by": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
            "status": "active",
            "updated_by": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
            "created_time": "2025-07-21T07:36Z",
            "updated_time": "2025-07-21T07:43Z",
            "source_key": "webhooks",
            "source_type": "server",
            "config_connect": {
                "methods": ["POST", "GET"],
                "content_type": "application/json",
                "url": "https://webhook.site/d766c1b7-a564-4bec-96c6-c23c00fbfc40",
                "app_id": "1a60dfc5-5648-11f0-b3f1-d7dedde76b12",
                "param_headers": [
                    {
                        "created_by": "system",
                        "key": "X-mevent-signature",
                        "type": "formula",
                        "value": "sha256(app_id + data_string + app_secret)",
                    }
                ],
            },
            "name": "Trang verify ng\u1eaft k\u1ebft n\u1ed1i c\u1ea5u h\u00ecnh ",
            "keywords": "trang verify ngat ket noi cau hinh",
            "name_ascii": "trang verify ngat ket noi cau hinh",
            "log_connection_information": {
                "status_connect": "success",
                "information": [{"name": "M\u00e3: 200", "status": "success"}],
                "time_check": "2025-07-21T07:36Z",
            },
            "data_type": "data_out",
            "connector_identification": "",
            "version": 2,
            "status_connect": "on",
            "action_time_change_status_connect": "2025-07-21T07:43Z",
            "auto_connection_check_interval": 10,
            "config_attribute": None,
            "config_information_out": [
                {
                    "module": "profile",
                    "event_keys": [
                        "profile_create_new",
                        "profile_update",
                        "profile_sent_email",
                        "profile_open_email",
                        "profile_reply_email",
                        "profile_dynamic_event",
                        "profile_event_tracking",
                    ],
                    "config_profile_event_tracking": [
                        {
                            "connector_code": "MWP-20d59c9d-9020-11ef-ac61-a5cc13275747",
                            "type_tracking": "all",
                            "list_specific_event": [],
                        }
                    ],
                    "config_profile_dynamic_event": {"type_tracking": "all", "list_specific_event": []},
                },
                {
                    "module": "company",
                    "event_keys": [
                        "company_create",
                        "company_update",
                        "company_sent_email",
                        "company_open_email",
                        "company_reply_email",
                        "company_update_profile",
                        "company_update_sale",
                        "company_update_company",
                        "company_update_media",
                        "company_update_ticket",
                    ],
                },
                {
                    "module": "sale",
                    "event_keys": [
                        "sale_create_deal",
                        "sale_update_deal",
                        "sale_update_status",
                        "sale_update_contact",
                        "sale_update_company",
                        "sale_update_media",
                        "sale_update_product_line",
                        "sale_update_product_types",
                        "sale_update_product_categories_main",
                        "sale_update_product_categories_sub",
                        "sale_update_products_bank",
                    ],
                },
                {
                    "module": "ticket",
                    "event_keys": [
                        "ticket_create",
                        "ticket_update",
                        "ticket_send_email_profile",
                        "ticket_update_info_profile",
                        "ticket_update_info_company",
                        "ticket_update_info_sale",
                        "ticket_update_info_media",
                        "ticket_update_info_product",
                    ],
                },
                {
                    "module": "task",
                    "event_keys": [
                        "task_create",
                        "task_edit",
                        "task_delete",
                        "task_comment_create",
                        "task_comment_update",
                        "task_comment_delete",
                    ],
                },
                {
                    "module": "note",
                    "event_keys": [
                        "note_create",
                        "note_update_data",
                        "note_delete",
                        "note_comment_create",
                        "note_comment_update_data",
                        "note_comment_delete",
                    ],
                },
            ],
            "config_sync_calendar": {"mode": "streaming", "auto_retry_w_error": {"status": 0}},
            "contact_info": [{"type": "account_id", "values": ["e659ab59-b4ae-48fc-94b3-4f59ea9fe317"]}],
            "max_notification_count": 6,
            "object_attribute": None,
            "sync_limit": {"http_connection_timeout": 100},
            "status_sync": "running",
            "last_datetime_sync_data": "2025-07-21T08:15Z",
        },
        "data_after": {
            "updated_by": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
            "updated_time": "2025-07-21T08:46Z",
            "status_connect": "off",
            "status_sync": "stopped",
        },
        "action": "update_and_synced_orchestration",
        "time_action": "2025-07-21T08:46Z",
    }
    HandleDataHistoryConsumer.handler_record(message=data_send)
