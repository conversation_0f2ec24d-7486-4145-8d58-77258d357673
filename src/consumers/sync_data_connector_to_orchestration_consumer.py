#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 01/04/2024
"""

from mobio.libs.logging import MobioLogging
from mobio.sdks.base.common.mobio_exception import CustomError

from src.common import ConstantStatusHandleSyncData
from src.common.data_flow_constant import (
    ConstantModeConfigSyncCalendar,
    ConstantStatusConnect,
)
from src.helpers.confluent_kafka_base import ConfluentKafkaConsumer
from src.helpers.orchestration_build import OrchestrationBuildHelper
from src.internal_module.orchestration import OrchestrationApiHelper
from src.models.data_flow.config_connectors_model import ConfigConnectorsModel
from src.models.log_sync_data_to_module_other_model import LogSyncDataToModuleOtherModel


class SyncDataConnectorToOrchestrationConsumer(ConfluentKafkaConsumer):

    def __init__(self, topic_name, group_id):
        super().__init__(topic_name, group_id)

    def process_msg(self, payload):
        SyncDataConnectorToOrchestrationConsumer.sync_data(payload)

    @staticmethod
    def sync_data(payload):
        MobioLogging().info("Start handling message :: {}".format(payload))
        connect_config_id = payload.get("connect_config_id")
        merchant_id = payload.get("merchant_id")
        data_after = payload.get("data_after")
        if not data_after:
            data_after = {}
        data_before = payload.get("data_before")
        if not data_before:
            data_before = {}

        status_connect = None
        status_connect = data_after.get("status_connect")
        if not status_connect:
            status_connect = data_before.get("status_connect")
        MobioLogging().info("Handle status_connect :: {}".format(status_connect))
        if not status_connect or status_connect == ConstantStatusConnect.OFF:
            return

        data_after_state = data_after.get("state", {})

        state_status_handle_sync = data_after_state.get("status_handle_sync")
        if state_status_handle_sync == ConstantStatusHandleSyncData.DONE:
            return
        after_mode_sync_data = data_after.get("config_sync_calendar", {}).get("mode")
        before_mode_sync_data = data_before.get("config_sync_calendar", {}).get("mode")

        need_stop_connect = False
        if after_mode_sync_data and before_mode_sync_data and after_mode_sync_data != before_mode_sync_data:
            need_stop_connect = True

        orchestration_id = OrchestrationBuildHelper().handle_sync_data(
            merchant_id, connect_config_id, need_stop_connect
        )

        if orchestration_id:

            MobioLogging().info("Starting Orchestration")
            detail_log_sync_data = LogSyncDataToModuleOtherModel().find_one(
                {"merchant_id": merchant_id, "connector_id": int(connect_config_id)}
            )
            if not detail_log_sync_data:
                raise CustomError("Not found log_sync_data")
            module_data = detail_log_sync_data.get("module_data")
            if not module_data:
                raise CustomError("Not found module_data")
            session_id = module_data.get("session_id")
            if not session_id:
                connector_detail = ConfigConnectorsModel().detail_connector_by_id(merchant_id, int(connect_config_id))

                type_sync_data = connector_detail.get("config_sync_calendar", {}).get("mode")

                if type_sync_data == ConstantModeConfigSyncCalendar.STREAMING:
                    on_pipeline = OrchestrationApiHelper().start_pipeline(merchant_id, orchestration_id)
                    if not on_pipeline:
                        raise CustomError("Not sync information orchestration")
                    MobioLogging().info(
                        "DataFlowController :: update_status_connect :: on_pipeline :: {}".format(on_pipeline)
                    )
                    pipeline_session_id = on_pipeline.get("session_id")
                    LogSyncDataToModuleOtherModel().update_by_set(
                        {
                            "merchant_id": merchant_id,
                            "connector_id": int(connect_config_id),
                        },
                        {"module_data.session_id": pipeline_session_id},
                    )


if __name__ == "__main__":
    msg = {
        "merchant_id": "783cadcb-03a7-11f0-93c0-739ff8599579",
        "account_id": None,
        "connect_config_id": "1544",
        "type": "data_flow",
        "data_before": {
            "_id": 1544,
            "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
            "created_by": "b122802d-fe0d-4e9e-bdc1-4bcfc1ccf5eb",
            "status": "active",
            "updated_by": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
            "created_time": "2024-12-20T04:05Z",
            "updated_time": "2024-12-23T05:32Z",
            "config_connect": {
                "host": "*************",
                "port": 1521,
                "database_name": "mobio",
                "database_username": "mobio",
                "database_password": "mobio",
            },
            "source_key": "oracle",
            "source_type": "databases",
            "name": "[PH] test 3",
            "keywords": "[ph] test 3",
            "name_ascii": "[ph] test 3",
            "log_connection_information": {
                "status_connect": "success",
                "information": [
                    {"name": "K\u1ebft n\u1ed1i \u0111\u1ebfn database", "status": "success"},
                    {"name": "Authorization", "status": "success"},
                    {"name": "Database", "status": "success"},
                    {"name": "Permission", "status": "success"},
                ],
                "time_check": "2024-12-20T04:05Z",
            },
            "data_type": "data_in",
            "connector_identification": "",
            "version": 2,
            "action_time_change_status_connect": "2024-12-23T05:32Z",
            "config_attribute": "product_holding",
            "config_data_default_of_primary_object": [
                {"field_key": "product_line_id", "value": "673d49e7e3142a45a6bcef1a", "is_personalization": False},
                {"field_key": "product_line_code", "value": "tina12345", "is_personalization": False},
            ],
            "config_mapping_data": {
                "fields": [
                    {
                        "object": "product_holding",
                        "action": "overwrite",
                        "required": True,
                        "field_target": "product_code",
                        "display_type": "single_line",
                        "field_property": 2,
                        "field_source": "PRODUCT_CODE",
                        "field_source_type": "VARCHAR2",
                        "field_source_schema_type": "VARCHAR2(100)",
                        "value_type": "record",
                    },
                    {
                        "object": "profiles",
                        "action": "overwrite_and_ignore_value_null",
                        "field_source": "CIF",
                        "field_source_type": "VARCHAR2",
                        "field_source_schema_type": "VARCHAR2(100)",
                        "value_type": "record",
                        "field_target": "cif",
                        "display_type": "multi_line",
                        "field_property": 2,
                        "format": None,
                        "format_value": None,
                    },
                    {
                        "object": "profiles",
                        "action": "overwrite_and_ignore_value_null",
                        "field_source": "NAME",
                        "field_source_type": "VARCHAR2",
                        "field_source_schema_type": "VARCHAR2(100)",
                        "value_type": "record",
                        "field_target": "name",
                        "display_type": "single_line",
                        "field_property": 2,
                        "format": None,
                        "format_value": None,
                    },
                    {
                        "object": "product_holding",
                        "action": "overwrite",
                        "field_source": "STATUS",
                        "field_source_type": "VARCHAR2",
                        "field_source_schema_type": "VARCHAR2(100)",
                        "value_type": "record",
                        "field_target": "product_status",
                        "display_type": "dropdown_single_line",
                        "field_property": 2,
                    },
                    {
                        "object": "product_holding",
                        "action": "overwrite",
                        "field_source": "PRODUCT_NAME",
                        "field_source_type": "VARCHAR2",
                        "field_source_schema_type": "VARCHAR2(100)",
                        "value_type": "record",
                        "field_target": "name",
                        "display_type": "single_line",
                        "field_property": 2,
                    },
                ],
                "table": "TDT_PRODUCT_HOLDING_04",
            },
            "config_rule_unification": {
                "data_recording_rules": {},
                "data_update_rules": {
                    "operators": [
                        {
                            "priority": 1,
                            "fields": {
                                "name": {"match_type": "exact", "normalized_type": "string"},
                                "product_code": {"match_type": "exact", "normalized_type": "string"},
                            },
                        }
                    ]
                },
            },
            "config_rule_unification_secondary_object": [
                {
                    "consent": {
                        "tracking_consent": "C\u00f3",
                        "analytics_consent": "C\u00f3",
                        "mkt_consent": "C\u00f3",
                    },
                    "data_recording_rules": {},
                    "data_update_rules": {
                        "operators": [
                            {
                                "priority": 1,
                                "fields": {
                                    "cif": {"match_type": "exact", "normalized_type": "string"},
                                    "name": {"match_type": "exact", "normalized_type": "string"},
                                },
                            }
                        ]
                    },
                    "secondary_object": "profiles",
                }
            ],
            "config_sync_calendar": {
                "mode": "snapshot",
                "schedule": {"type": "manually"},
                "auto_retry_w_error": {"status": 0},
            },
            "is_trust_source": False,
            "is_type_sync_manually": False,
            "object": "profiles",
            "object_attribute": "product_holding",
            "query_jq": '{\\n    "product_holding_data": {\\n        "product_code": .PRODUCT_CODE,\\n        "product_status": .STATUS,\\n        "name": .PRODUCT_NAME,\\n    },\\n    "profiles_data": {\\n        "cif": .CIF,\\n        "name": .NAME,\\n    }\\n}',
            "status_connect": "on",
            "reason_connect": "",
            "status_sync": "done",
            "last_datetime_sync_data": "2024-12-23T03:14Z",
        },
        "data_after": {"status_connect": "on", "reason_connect": "", "status_sync": "running"},
        "action": "update",
        "time_action": "2024-12-23T05:32Z",
    }
    SyncDataConnectorToOrchestrationConsumer.sync_data(msg)
