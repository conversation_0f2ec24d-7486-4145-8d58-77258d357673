#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 01/04/2024
"""

from mobio.libs.logging import MobioLogging
from src.internal_module.notify_management import NotifyManagementHelper
from src.helpers.confluent_kafka_base import ConfluentKafkaConsumer


class UpsertProviderConfigNotifiManagement(ConfluentKafkaConsumer):

    def __init__(self, topic_name, group_id):
        super().__init__(topic_name, group_id)

    def process_msg(self, payload):
        UpsertProviderConfigNotifiManagement.sync_data(payload)

    @staticmethod
    def sync_data(payload):
        MobioLogging().info("Start handling message :: {}".format(payload))
        merchant_id = payload.get('merchant_id')
        payload_ses = payload.get('payload')
        NotifyManagementHelper().upsert_provider_config(
            merchant_id, payload_ses
        )