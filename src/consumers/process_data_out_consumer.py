import datetime

from mobio.libs.logging import MobioLogging

from configs import MarketPlaceApplicationConfig
from src.common import ConstProcessRequestDataOut, ConstSourceKey
from src.common.data_flow_constant import ConstantDataType
from src.common.utils import convert_string_datetime_to_datetime
from src.helpers.confluent_kafka_base import ConfluentKafkaConsumer
from src.models.data_flow.config_connectors_model import ConfigConnectorsModel
from src.models.data_flow.process_data_out_model import ProcessDataOutModel
from src.sdks.notify_sdk import mobio_notify_sdk


class ProcessDataOutHelper:

    @staticmethod
    def build_cem_link_detail(process_request_id, connector_id):
        q = int(datetime.datetime.now(datetime.UTC).timestamp())
        const_license = "operation"
        link = f"{MarketPlaceApplicationConfig.CEM_HOST}setting/tenant/integration/sync-data/data-flow?id={process_request_id}&connector_id={connector_id}&q={q}&license={const_license}"
        return link


class ProcessDataOutMsExcelHandler:

    @staticmethod
    def handle(message):
        result = None
        log_prefix = "ProcessDataOutMsExcelHandler::handle"

        # Required data
        merchant_id = message["merchant_id"]
        request_id = message["request_id"]
        module_name = message["module_name"]
        area_code = message["area_code"]
        status_process = message["status_process"]
        source_key = ConstSourceKey.MS_EXCEL
        action_time = convert_string_datetime_to_datetime(message["action_time"], format_time="%Y-%m-%d %H:%M:%S.%f")
        object_id = None
        process_request = ProcessDataOutModel().get_request(merchant_id, request_id)

        status_process = message["status_process"]

        last_datetime_sync_data = datetime.datetime.now(datetime.UTC)

        # Upsert
        if not process_request:
            estimate_row = message["estimate_number_row"]
            estimate_start_time = convert_string_datetime_to_datetime(
                message["estimate_start_time"], format_time="%Y-%m-%d %H:%M:%S.%f"
            )
            estimate_completion_time = convert_string_datetime_to_datetime(
                message["estimate_completion_time"], format_time="%Y-%m-%d %H:%M:%S.%f"
            )

            data_insert = {
                "merchant_id": merchant_id,
                "request_id": request_id,
                "module_name": module_name,
                "source_key": source_key,
                "area_code": area_code,
                "status_process": status_process,
                "account_id": message["account_id"],
                "estimate_number_row": estimate_row,
                "estimate_start_time": estimate_start_time,
                "estimate_completion_time": estimate_completion_time,
            }

            if status_process == ConstProcessRequestDataOut.StatusProcess.DONE:

                for key in message:
                    if key in ProcessDataOutModel.LIST_FIELD_DATETIME:
                        data_insert[key] = convert_string_datetime_to_datetime(message[key], "%Y-%m-%d %H:%M:%S.%f")

                data_insert.update(
                    {
                        "actual_number_row": message.get("actual_number_row"),
                        "result_file": message.get("result_file"),
                        "processing_speed": message.get("processing_speed"),
                        "current_process_row": message.get("actual_number_row"),
                    }
                )

            # Optional key
            list_key_optional = ["condition_filters", "search_filter", "extract_data_area"]
            for key in list_key_optional:
                if key in message:
                    data_insert.update({key: message[key]})

            # Action info
            data_insert.update(
                {"request_time": action_time, "created_time": action_time, "updated_time": action_time},
            )

            MobioLogging().info(f"{log_prefix}::data_insert::{data_insert}")
            result = ProcessDataOutModel().create_request(merchant_id, data_insert)
            object_id = str(result.inserted_id)

            process_request = data_insert
            # Update last_datetime_sync_data

        else:
            object_id = str(process_request["_id"])
            # Clean message:
            if message.get("result_file", {}).get("local_path"):
                message["result_file"].pop("local_path")

            # Parse
            list_key_update = [
                "status_process",
                "actual_number_row",
                "processing_speed",
                "result_file",
                "retry_number",
                "current_process_row",
            ]
            data_update = {}
            for key in message:
                # Basic field type
                if key in list_key_update:
                    data_update[key] = message[key]
                # Datetime field
                if key in ProcessDataOutModel.LIST_FIELD_DATETIME:
                    data_update[key] = convert_string_datetime_to_datetime(message[key], "%Y-%m-%d %H:%M:%S.%f")

            # Action info
            data_update.update(
                {"updated_time": action_time},
            )

            MobioLogging().info(f"{log_prefix}::data_update::{data_update}")
            if data_update:
                result = ProcessDataOutModel().update_by_request_id(merchant_id, request_id, data_update)

        MobioLogging().info(f"{log_prefix}::result::{str(result)}")
        status_process_update = ConfigConnectorsModel().update_by_set(
            {"merchant_id": merchant_id, "source_key": source_key, "data_type": ConstantDataType.DATA_OUT},
            {"last_datetime_sync_data": action_time, "sort_time": action_time, "updated_time": action_time},
        )
        MobioLogging().info(f"{log_prefix}::status_process_update::{status_process_update}")

        # ================================================== Push notify ================================================== #
        account_id = process_request.get("account_id") if process_request else message.get("account_id")
        request_time = process_request.get("request_time", "") if process_request else message.get("action_time", "")
        if request_time and isinstance(request_time, datetime.datetime):
            request_time = request_time + datetime.timedelta(hours=7)
            request_time = request_time.strftime("%H:%M %d/%m/%Y")

        if (
            status_process
            in (
                ConstProcessRequestDataOut.StatusProcess.DONE,
                ConstProcessRequestDataOut.StatusProcess.STOP_PROCESS,
            )
            and account_id
        ):
            # Get connector_id
            connector_id = None
            ms_excel_connector = list(
                ConfigConnectorsModel().get_connector_by_source_key(
                    merchant_id=merchant_id,
                    source_key=ConstSourceKey.MS_EXCEL,
                    search={},
                    data_type=ConstantDataType.DATA_OUT,
                )
            )
            if len(ms_excel_connector) > 0:
                connector_id = int(ms_excel_connector[0]["_id"])
            access_url = ProcessDataOutHelper.build_cem_link_detail(
                process_request_id=object_id, connector_id=connector_id
            )
            if status_process == ConstProcessRequestDataOut.StatusProcess.DONE:
                access_url = message.get("result_file", {}).get("url")


            kw_send_notify = {
                "merchant_id": merchant_id,
                "module": "market-place",
                "account_ids": [account_id],
                "object_id": str(object_id),
                "key_config": "market_place_result_export_file_module",
                "area_name": ConstProcessRequestDataOut.AreaCodes.MAPPING.get(f"{module_name}.{area_code}", {}).get(
                    "area_name", "--"
                ),
                "data_export_request_time": request_time,
                "status_process_name": ConstProcessRequestDataOut.StatusProcess.get_status_process_name(status_process),
                "status_process": status_process,
                "url_file": access_url,
                "connector_id": connector_id,
            }
            # Mail
            mobio_notify_sdk.send_message_notify_email(**kw_send_notify)
            # Socket
            mobio_notify_sdk.send_message_notify_socket(**kw_send_notify)


class ProcessDataOutConsumer(ConfluentKafkaConsumer):

    def __init__(self, topic_name, group_id):
        super().__init__(topic_name, group_id)

    def process_msg(self, message):
        log_prefix = "ProcessDataOutConsumer::process_msg"
        MobioLogging().info(f"{log_prefix}::message::{message}")
        source_key = message.get("source_key", ConstSourceKey.MS_EXCEL)
        if source_key == ConstSourceKey.MS_EXCEL:
            ProcessDataOutMsExcelHandler.handle(message)
        MobioLogging().info(f"{log_prefix}::done")


if __name__ == "__main__":
    ProcessDataOutMsExcelHandler.handle(
        {
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "request_id": "6870d414b044ccde69f75c67",
            "source_key": "microsoft_excel",
            "module_name": "CALLCENTER",
            "area_code": "CALL_IN_LIST",
            "status_process": "done",
            "action_time": "2025-07-11 09:06:28.167782",
            "actual_number_row": 2,
            "actual_completion_time": "2025-07-11 09:06:28.167555",
            "processing_speed": 2,
            "result_file": {
                "url": "https://t1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/upload/DanhSachCuocGoi_bf535c6c-5e05-41a4-a94d-8daa42f6876a.xlsx",
                "local_path": "/media/data/public_resources/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/upload/DanhSachCuocGoi_bf535c6c-5e05-41a4-a94d-8daa42f6876a.xlsx",
                "filename": "DanhSachCuocGoi_bf535c6c-5e05-41a4-a94d-8daa42f6876a.xlsx",
                "format": "application/zip",
                "capacity": "5.2 KB",
                "expire_time": "2025-08-10T09:06:27Z",
            },
            "current_process_row": 2,
            "actual_start_time": "2025-07-11 09:06:27.458537",
            "condition_filters": {
                "staff_id": "",
                "call_status": ["miss"],
                "process_miss_call_stt": ["success", "processing", "unknow"],
                "miss_call_reason": ["agent_no_answer", "customer_hangup", "system_error"],
                "call_type": "inbound",
                "pbx_query": [{"pbxnumber_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924_NU11AO6K", "line": []}],
                "search": "**********",
                "start": "********",
                "end": "********",
                "sort": "time_start_call",
                "order": "desc",
                "paging": {"page": 1, "per_page": 15},
                "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            },
            "search_filter": "",
            "estimate_number_row": 2,
            "estimate_start_time": "2025-07-11 09:06:27.458537",
            "estimate_completion_time": "2025-07-11 09:06:28.167555",
            "account_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        }
    )
