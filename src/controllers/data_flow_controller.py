#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 27/03/2024
"""
import copy
import datetime
import json
import re
from collections import defaultdict

from flask import request
from mobio.libs.logging import MobioLogging
from mobio.libs.validator import In, InstanceOf, Length, Required
from mobio.sdks.base.common.mobio_exception import CustomError
from mobio.sdks.base.controllers import BaseController

from src.common import LANG
from src.common.data_flow_constant import (
    ConstantActionSyncData,
    ConstantConnectorStateCode,
    ConstantDataType,
    ConstantModeConfigSyncCalendar,
    ConstantObjectHandle,
    ConstantParamApi,
    ConstantParameterConnector,
    ConstantScheduleType,
    ConstantSourceKey,
    ConstantStatusConnect,
    ConstantStatusConnectionDatabases,
    ConstantStatusSyncData,
    ConstantVersion,
)
from src.common.handle_headers import get_param_value_temp, validate_merchant_header
from src.common.handle_queue import HandleQueue
from src.common.json_encoder import JSONEncoder
from src.common.utils import (
    convert_string_datetime_to_datetime,
    json_compare,
    utf8_to_ascii,
)
from src.helpers.data_flow.check_connect_to_config_connect import HelperCheckConnection
from src.helpers.data_flow.get_information_from_database import (
    HelperGetInformationFromDatabase,
)
from src.helpers.general_param_default.handle_general_param_default import (
    HandlerGeneralParamDefault,
)
from src.helpers.orchestration_build import OrchestrationBuildHelper
from src.helpers.validators.handle_validator import HandlerValidator
from src.internal_module.data_out import DataOutHelper
from src.internal_module.orchestration import OrchestrationApiHelper
from src.models.data_flow.config_app_model import ConfigAppField, ConfigAppModel
from src.models.data_flow.config_connectors_model import (
    ConfigConnectorsModel,
    ConstantConfigConnectorsModel,
)
from src.models.data_flow.event_config_model import InformationEventDataOutModel
from src.models.data_flow.general_configuration_parameters_model import (
    GeneralConfigurationParametersModel,
)
from src.models.data_flow.object_handle_model import SettingObjectHandleModel
from src.models.data_flow.session_model import ConfigSessionModel
from src.models.data_flow.setting_add_on_connector_model import (
    SettingAddOnConnectorModel,
)
from src.models.data_flow.source_model import SourceModel
from src.models.data_flow.source_type_model import SourceTypeModel
from src.models.data_history_model import DataHistoryModel
from src.models.log_sync_data_to_module_other_model import LogSyncDataToModuleOtherModel
from src.models.reports.mongodb.daily_reports_model import DailyReportsModel


class DataFlowController(BaseController):
    FROM_FORMAT_DATETIME = "%Y-%m-%dT%H:%MZ"
    FROM_FORMAT_DATETIME_WITH_SECOND = "%Y-%m-%dT%H:%M:%SZ"
    TO_FORMAT_DATETIME = "%Y-%m-%d %H:%M:%S"
    FROM_FORMAT_DATETIME_UTC = "%Y-%m-%dT%H:%MZ"

    def __init__(self):
        super().__init__()
        self.handler_validator = HandlerValidator()

    def get_source_types(self):
        """
        Lấy danh sách source types
        """
        data_type = request.args.get("data_type", ConstantDataType.DATA_IN)
        source_types = SourceTypeModel().get_list_source_type(data_type)
        results = []
        for source_type in source_types:
            results.append(
                {
                    "key": source_type.get("key"),
                    "name": source_type.get("name", {}).get(self.language),
                    "order": source_type.get("order"),
                }
            )
        return {"data": results}

    def get_sources(self):
        """
        Lấy danh sách source
        """
        data_type = request.args.get("data_type", ConstantDataType.DATA_IN)
        search = request.args.get("search")
        source_type = request.args.get("source_type")
        source_key = request.args.get("source_key")
        if source_key:
            source_key = source_key.split(",")
        if source_type:
            source_type = source_type.split(",")
        sources = SourceModel().get_list_source_by_type_key(source_type, source_key, search, data_type)
        results = []
        for source in sources:
            results.append(JSONEncoder().json_loads(source))
        return results

    def get_ip_whitelist(self):
        """
        Lấy danh sách ip whitelist
        """

        results = GeneralConfigurationParametersModel().get_ip_whitelist()
        return {"data": results}

    def get_object_handled(self):
        """
        Lấy danh sách source
        """
        merchant_id = validate_merchant_header()
        results = []
        data_type = request.args.get("data_type", ConstantDataType.DATA_IN)

        source_key_default = "postgres"
        if data_type == ConstantDataType.DATA_OUT:
            source_key_default = "webhooks"

        source_key = request.args.get("source_key", source_key_default)
        source_handled = SettingObjectHandleModel().get_list_source_handled(merchant_id, data_type, source_key)
        for source in source_handled:
            results.append(
                {
                    "name": source["name"].get(self.language),
                    "key": source.get("key"),
                    "status": source.get("status"),
                    "order": source.get("order"),
                    "config_attributes": source.get("config_attributes", []),
                }
            )
        results = sorted(results, key=lambda x: x["order"])
        return {"data": results}

    def upsert_information_object_handed(self):
        pass

    #     """ """
    #     body = request.get_json()

    #     objects = body.get("objects")
    #     ignore_objects = body.get("ignore_objects")
    #     data_type = request.args.get("data_type", ConstantDataType.DATA_IN)

    #     data_upsert = []
    #     source_handled = GeneralConfigurationParametersModel().get_list_source_handled(data_type)
    #     for source in source_handled:
    #         source_key = source.get("key")
    #         if objects and source_key in objects:
    #             source["status"] = 1
    #         if ignore_objects and source_key in ignore_objects:
    #             source["status"] = 0
    #         data_upsert.append(source)
    #     GeneralConfigurationParametersModel().upsert_source_handled(data_upsert, data_type)
    #     return {"data": data_upsert}

    def _validate_check_connection(self, data_validate, data_type):
        connector_id = data_validate.get(ConstantParamApi.BodyDatabaseCheckConnection.CONNECTOR_ID)
        if not connector_id:
            source_key = data_validate.get(ConstantParamApi.BodyDatabaseCheckConnection.SOURCE_KEY)
            source_type = data_validate.get(ConstantParamApi.BodyDatabaseCheckConnection.SOURCE_TYPE)
            self.handler_validator.call(data_type, source_type, source_key, "check_connection", data_validate)

    def check_connection(self):
        merchant_id = validate_merchant_header()
        body = request.get_json()
        data_type = request.args.get("data_type", ConstantDataType.DATA_IN)
        self._validate_check_connection(body, data_type=data_type)

        connector_id = body.get(ConstantParamApi.BodyDatabaseCheckConnection.CONNECTOR_ID)
        if connector_id:
            detail_connector = ConfigConnectorsModel().detail_connector_by_id(merchant_id, connector_id)
            if not detail_connector:
                raise CustomError(self.lang.get(LANG.NOT_FOUND).get("message"))

            config_connect_database = detail_connector.get("config_connect")
            config_connect_body = body.get("config_connect")
            body_new = {
                "source_key": (body.get("source_key") if "source_key" in body else detail_connector.get("source_key")),
                "source_type": (
                    body.get("source_type") if "source_type" in body else detail_connector.get("source_type")
                ),
            }

            if config_connect_body:
                config_connect_database.update(config_connect_body)
            body_new["config_connect"] = config_connect_database
            body = body_new

        config_connect = body[ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT]
        source_key = body.get(ConstantParamApi.BodyDatabaseCheckConnection.SOURCE_KEY)
        source_type = body.get(ConstantParamApi.BodyDatabaseCheckConnection.SOURCE_TYPE)
        (
            status_connect,
            connect_information,
        ) = HelperCheckConnection().call_check_connection(
            source_key, source_type, data_type, self.lang, config_connect, merchant_id
        )
        result = {"status": status_connect, "information": connect_information}
        return {"data": result}

    def _validate_create_connectors(self, data_validate, data_type):
        source_key = data_validate.get(ConstantParamApi.BodyCreateConnectors.SOURCE_KEY)
        source_type = data_validate.get(ConstantParamApi.BodyCreateConnectors.SOURCE_TYPE)
        self.handler_validator.call(data_type, source_type, source_key, "create_connectors", data_validate)

    def _check_connector_name_exist(
        self,
        merchant_id,
        connector_name,
        data_type=None,
        ignore_connector_id=None,
    ):
        connectors = ConfigConnectorsModel().get_connector_by_connector_name(
            merchant_id, connector_name, ignore_connector_id
        )
        if connectors:
            raise CustomError(
                self.lang.get(LANG.CONNECTOR_NAME_EXIST.format(data_type)).get("message").format(connector_name)
            )

    def _build_keyword_search(self, name, description):
        keywords = []
        if name:
            keywords.append(name)
        if description:
            keywords.append(description)
        keyword = ",".join(keywords)
        return utf8_to_ascii(keyword.lower())

    def _build_connector_config_attribute(self, object_primary, object_attribute):
        if not object_attribute:
            if object_primary == ConstantObjectHandle.Object.PROFILES:
                return ConstantObjectHandle.ObjectAttribute.PROFILE_ATTRIBUTE
            return object_primary
        if object_primary in [ConstantObjectHandle.Object.TICKET, ConstantObjectHandle.Object.SALE]:
            object_attribute = ""
        return object_attribute

    def _build_connector_identification(self, config_connector):
        if not config_connector:
            return ""

        param_headers = config_connector.get("param_headers")
        if param_headers:
            for header in param_headers:
                if header.get("key") == "Mobio-Connector-Identifier":
                    return header.get("value")
        form_id = config_connector.get("form_id")
        if form_id:
            return form_id
        return ""

    def _build_connector_name_ascii(self, connector_name):
        connector_name_ascii = utf8_to_ascii(connector_name.lower())
        return connector_name_ascii

    def _build_description_ascii(self, description):
        return utf8_to_ascii(description.lower())

    def _validate_config_connect(self, source_key, source_type, config_connect, data_type, merchant_id):
        (
            status_connect,
            connect_information,
        ) = HelperCheckConnection().call_check_connection(
            source_key, source_type, data_type, self.lang, config_connect, merchant_id
        )
        log_connection_information = {
            "status_connect": status_connect,
            "information": connect_information,
            "time_check": datetime.datetime.now(datetime.UTC),
        }
        if status_connect == ConstantStatusConnectionDatabases.FAIL:
            raise CustomError(self.lang.get(LANG.INFORMATION_CONNECT_CONFIG_FAIL).get("message"))
        return log_connection_information

    def create_connectors(self):
        merchant_id = validate_merchant_header()
        account_id = get_param_value_temp("id")
        body = request.get_json()
        data_type = request.args.get("data_type", ConstantDataType.DATA_IN)
        self._validate_create_connectors(body, data_type)
        debug = request.args.get("debug", False, type=bool)

        data_insert = copy.deepcopy(body)

        name = body.get(ConstantParamApi.BodyCreateConnectors.NAME)
        name = name.strip()

        description = body.get(ConstantParamApi.BodyCreateConnectors.DESCRIPTION, "")
        keyword = self._build_keyword_search(name, description)
        data_insert["keywords"] = keyword
        name_ascii = self._build_connector_name_ascii(name)
        self._check_connector_name_exist(merchant_id, name_ascii, data_type)
        data_insert["name_ascii"] = name_ascii

        source_key = body.get(ConstantParamApi.BodyCreateConnectors.SOURCE_KEY)
        source_type = body.get(ConstantParamApi.BodyCreateConnectors.SOURCE_TYPE)
        config_connect = body[ConstantParamApi.BodyCreateConnectors.CONFIG_CONNECT]
        log_connection_information = self._validate_config_connect(
            source_key, source_type, config_connect, data_type, merchant_id
        )
        data_insert["log_connection_information"] = log_connection_information
        data_insert["data_type"] = data_type

        connector_identification = self._build_connector_identification(config_connect)
        data_insert["connector_identification"] = connector_identification
        data_insert["version"] = ConstantVersion.NEW
        data_insert["status_connect"] = ConstantStatusConnect.OFF

        MobioLogging().info("create_connectors :: body :: {}".format(body))
        data_init_insert = ConfigConnectorsModel().insert_connector(merchant_id, account_id, **data_insert)
        MobioLogging().info("create_connectors :: data_init_insert :: {}".format(data_init_insert))
        data_init_insert.update({"state": {"type": ConstantConnectorStateCode.INIT_CONNECTOR}})
        HandleQueue.push_message_connector_config(
            data_init_insert["_id"],
            {},
            data_init_insert,
            "create",
            merchant_id,
            account_id,
        )

        result = self._convert_detail_connector(data_init_insert, debug)
        return {"data": result}

    def list_connectors(self):
        merchant_id = validate_merchant_header()
        args = request.args
        data_type = args.get("data_type", ConstantDataType.DATA_IN)
        search = args.get("search")
        is_trust_source = args.get("is_trust_source")
        status_sync = args.get("status_sync")
        status_connect = args.get("status_connect")
        created_by = args.get("created_by")
        source_keys = args.get("source_keys")
        source_types = args.get("source_types")
        object_attribute = args.get("object_attribute")
        status_sync_conn = args.get("status_sync_conn")
        last_time_sync_end_time = request.args.get("last_time_sync_end_time")
        last_time_sync_start_time = request.args.get("last_time_sync_start_time")

        sort_by = args.get("sort_by", "sort_time")
        order_by = args.get("order_by", -1, type=int)

        per_page = int(args.get("per_page", default=10, type=int))
        after_token = args.get("after_token", default=None)

        if is_trust_source is not None:
            is_trust_source = (
                False if is_trust_source.lower() == "false" else True if is_trust_source.lower() == "true" else None
            )
        # Thong nhat voi FE la gio UTC
        if last_time_sync_start_time:
            last_time_sync_start_time = convert_string_datetime_to_datetime(
                last_time_sync_start_time, self.FROM_FORMAT_DATETIME_UTC
            )
        if last_time_sync_end_time:
            last_time_sync_end_time = convert_string_datetime_to_datetime(
                last_time_sync_end_time, self.FROM_FORMAT_DATETIME_UTC
            )

        (
            connectors,
            new_after_token,
        ) = ConfigConnectorsModel().get_connectors_by_condition(
            merchant_id,
            search,
            is_trust_source,
            status_connect,
            status_sync,
            source_keys,
            source_types,
            created_by,
            order_by,
            sort_by,
            after_token,
            per_page,
            data_type,
            object_attribute,
            status_sync_conn,
            last_time_sync_start_time,
            last_time_sync_end_time,
        )
        results = []
        for connector in connectors:
            result = self._convert_detail_connector(connector, False)
            results.append(result)

        return {
            "data": results,
            "paging": {"cursors": {"after_token": new_after_token, "before_token": after_token}},
        }

    def _validate_connector_id(self, connector_id):
        if not connector_id.isdigit():
            raise ValueError("Connector ID error type")

    def get_list_table_in_database(self, connector_id):
        merchant_id = validate_merchant_header()

        self._validate_connector_id(connector_id)

        detail_connector = ConfigConnectorsModel().detail_connector_by_id(merchant_id, connector_id)
        if not detail_connector:
            raise CustomError(self.lang.get(LANG.NOT_FOUND).get("message"))

        data_type = detail_connector.get("data_type")
        source_type = detail_connector.get("source_type")
        source_key = detail_connector.get("source_key")
        config_connect = detail_connector.get("config_connect")

        func_name = HelperGetInformationFromDatabase().get_func_get_information_from_database(
            "list_table", source_key, source_type, data_type
        )
        tables = HelperGetInformationFromDatabase().call_get(func_name, self.lang, config_connect, merchant_id)
        return {"data": tables}

    def _validate_get_data_sample_table_in_database(self, data_validate):
        rule_validate = {
            ConstantParamApi.BodyGetDataSampleTables.TABLE_NAME: [
                Required,
                InstanceOf(str),
                Length(1),
            ],
            ConstantParamApi.BodyGetDataSampleTables.SCHEMA: [],
        }
        self.abort_if_validate_error(rule_validate, data_validate)

    def get_data_sample_table_in_database(self, connector_id):
        merchant_id = validate_merchant_header()
        body = request.get_json()
        self._validate_connector_id(connector_id)
        self._validate_get_data_sample_table_in_database(body)
        table_name = body.get(ConstantParamApi.BodyGetDataSampleTables.TABLE_NAME)
        schema = (
            body.get(ConstantParamApi.BodyGetDataSampleTables.SCHEMA)
            if body.get(ConstantParamApi.BodyGetDataSampleTables.SCHEMA)
            else None
        )
        detail_connector = ConfigConnectorsModel().detail_connector_by_id(merchant_id, connector_id)
        if not detail_connector:
            raise CustomError(self.lang.get(LANG.NOT_FOUND).get("message"))

        number_item = request.args.get("number_item", 1000)
        if number_item and int(number_item) > 1000:
            number_item = 1000
        data_type = detail_connector.get("data_type")
        source_type = detail_connector.get("source_type")
        source_key = detail_connector.get("source_key")
        config_connect = detail_connector.get("config_connect")

        func_name = HelperGetInformationFromDatabase().get_func_get_information_from_database(
            "data_sample", source_key, source_type, data_type
        )
        column_names, results, total_row = HelperGetInformationFromDatabase().call_get(
            func_name, self.lang, config_connect, merchant_id, table_name, number_item, schema
        )
        return {"data": {"columns": column_names, "rows": results, "total_row": total_row}}

    def _validate_get_schema_of_table(self, data_validate):
        rule_validate = {
            ConstantParamApi.BodyGetDataSampleTables.TABLE_NAME: [
                Required,
                InstanceOf(str),
                Length(1),
            ]
        }
        self.abort_if_validate_error(rule_validate, data_validate)

    def get_schema_of_table(self, connector_id):
        merchant_id = validate_merchant_header()
        body = request.get_json()
        self._validate_connector_id(connector_id)
        self._validate_get_data_sample_table_in_database(body)
        table_name = body.get(ConstantParamApi.BodyGetDataSampleTables.TABLE_NAME)
        schema = (
            body.get(ConstantParamApi.BodyGetDataSampleTables.SCHEMA)
            if body.get(ConstantParamApi.BodyGetDataSampleTables.SCHEMA)
            else None
        )
        detail_connector = ConfigConnectorsModel().detail_connector_by_id(merchant_id, connector_id)
        if not detail_connector:
            raise CustomError(self.lang.get(LANG.NOT_FOUND).get("message"))
        data_type = detail_connector.get("data_type")
        source_type = detail_connector.get("source_type")
        source_key = detail_connector.get("source_key")
        config_connect = detail_connector.get("config_connect")

        func_name = HelperGetInformationFromDatabase().get_func_get_information_from_database(
            "schema_of_table", source_key, source_type, data_type
        )
        schemas = HelperGetInformationFromDatabase().call_get(
            func_name, self.lang, config_connect, merchant_id, table_name, schema
        )
        return {"data": schemas}

    def _validate_and_smooth_update_connectors(
        self, data_validate, data_type, source_type, source_key, detail_connector
    ):
        return HandlerValidator().call(
            data_type,
            source_type,
            source_key,
            "update_connectors",
            data_validate,
            detail_connector,
        )

    def _generate_conditional_build_JQ(self, path_str):
        parts = path_str.split(".")

        if len(parts) < 2:
            return "." + path_str

        root = parts[0]
        condition_parts = []
        current_path = f".{root}"
        quote_chars = ['"', "'"]
        quote_index = 0

        for part in parts[1:]:
            quote = quote_chars[quote_index % 2]
            condition_parts.append(f"{current_path} | has({quote}{part}{quote})")
            current_path += f".{part}"
            quote_index += 1

        full_condition = " and ".join(condition_parts)
        full_path = "." + ".".join(parts)
        conditional_str = f"""if ({full_condition}) then {full_path} else "ignore_value_when_field_not_exist" end"""

        return conditional_str

    def _build_jq_query_by_mapping_field(self, mapping_field):
        mapping_object_to_key = {
            ConstantObjectHandle.Object.PROFILES: "profile",
            ConstantObjectHandle.Object.SALE: "deal",
        }
        objects = defaultdict(
            lambda: {"fields": {}, "arrays": defaultdict(lambda: {"source_path": "", "subfields": {}})}
        )

        for item in mapping_field:
            field_source = item.get("field_source")
            if "data[*]" in field_source:
                field_source = re.sub(r"^data\[\*\]\.", "", field_source)
            if "data." in field_source:
                field_source = re.sub(r"^data\.", "", field_source)

            field_target = item.get("field_target")
            object_name = item.get("object")
            object_name = mapping_object_to_key.get(object_name, object_name)
            value_type = item.get("value_type")
            default_value = (
                item.get("value_by_type_fixed") if value_type == "fixed" else item.get("default_value", None)
            )

            if "[*]" in field_target:
                # Trường hợp field_target là mảng
                try:
                    array_name_target, inner_field_target = field_target.split("[*].", 1)
                except ValueError:
                    raise ValueError(f"Invalid field_target format: {field_target}")

                if "[*]" in field_source:
                    # Trường hợp field_source cũng là mảng
                    try:
                        array_name_source, inner_field_source = field_source.split("[*].", 1)
                    except ValueError:
                        raise ValueError(f"Invalid field_source format for array: {field_source}")
                    inner_field_source = self._generate_conditional_build_JQ(inner_field_source)
                    # Định nghĩa đường dẫn nguồn
                    source_path = f".{array_name_source}"

                    # Xử lý biểu thức subfield
                    if value_type == "fixed":
                        if isinstance(default_value, str):
                            subfield_expr = f'"{default_value}"'
                        else:
                            subfield_expr = f"{default_value}"
                    else:
                        if default_value is not None:
                            subfield_expr = f".{inner_field_source} // {json.dumps(default_value)}"
                        else:
                            subfield_expr = f"{inner_field_source}"

                    # Gán biểu thức subfield
                    objects[object_name]["arrays"][array_name_target]["subfields"][inner_field_target] = subfield_expr

                    # Lưu đường dẫn nguồn
                    if not objects[object_name]["arrays"][array_name_target]["source_path"]:
                        objects[object_name]["arrays"][array_name_target]["source_path"] = f".{array_name_source}"
                else:
                    # Trường hợp field_source không phải mảng
                    field_source = self._generate_conditional_build_JQ(field_source)
                    if value_type == "fixed":
                        if isinstance(default_value, str):
                            value_expr = f'"{default_value}"'
                        else:
                            value_expr = f"{default_value}"
                    else:
                        if default_value is not None:
                            value_expr = f".{field_source} // {json.dumps(default_value)}"
                        else:
                            value_expr = f"{field_source}"

                    # Gán biểu thức subfield
                    objects[object_name]["arrays"][array_name_target]["subfields"][inner_field_target] = value_expr

                    # Đặt đường dẫn nguồn trống (fixed mapping)
                    objects[object_name]["arrays"][array_name_target]["source_path"] = ""
            else:
                # Trường hợp field_target không phải mảng
                if "[*]" in field_source:
                    # Trường hợp field_source là mảng
                    try:
                        array_name_source, inner_field_source = field_source.split("[*].", 1)
                    except ValueError:
                        raise ValueError(f"Invalid field_source format for array: {field_source}")
                    inner_field_source = self._generate_conditional_build_JQ(inner_field_source)
                    # Định nghĩa đường dẫn nguồn
                    adjusted_source = f".{array_name_source}[]?.{inner_field_source}"

                    if value_type == "fixed":
                        if isinstance(default_value, str):
                            adjusted_source = f'"{default_value}"'
                        else:
                            adjusted_source = f"{default_value}"
                    elif default_value is not None:
                        adjusted_source += f" // {json.dumps(default_value)}"

                    # Gán biểu thức vào trường đơn
                    objects[object_name]["fields"][field_target] = adjusted_source
                else:
                    # Trường hợp cả field_source và field_target đều không phải mảng
                    field_source = self._generate_conditional_build_JQ(field_source)
                    field_expr = f"{field_source}"
                    if value_type == "fixed":
                        if isinstance(default_value, str):
                            field_expr = f'"{default_value}"'
                        else:
                            field_expr = f"{default_value}"
                    elif default_value is not None:
                        field_expr = f"{field_expr} // {json.dumps(default_value)}"

                    # Gán biểu thức vào trường đơn
                    objects[object_name]["fields"][field_target] = field_expr

        jq_parts = []
        for object_name, content in objects.items():
            object_key = f"{object_name}_data"
            jq_parts.append(f'    "{object_key}": {{')

            field_lines = []

            # Xử lý các trường không phải mảng
            for field_target, field_expr in content["fields"].items():
                field_lines.append(f'        "{field_target}": {field_expr}')

            # Xử lý các trường là mảng
            for array_name, array_content in content["arrays"].items():
                source_path = array_content["source_path"]
                subfields = array_content["subfields"]

                if source_path:
                    # Trường hợp nguồn là mảng
                    subfields_jq = ", ".join([f'"{k}": {v}' for k, v in subfields.items()])
                    array_jq = f'        "{array_name}": [ {source_path}[]? | {{ {subfields_jq} }} ]'
                else:
                    # Trường hợp nguồn không phải mảng (fixed mapping)
                    subfields_jq = ", ".join([f'"{k}": {v}' for k, v in subfields.items()])
                    array_jq = f'        "{array_name}": [{{ {subfields_jq} }}]'

                field_lines.append(array_jq)

            # Kết hợp các trường với dấu phẩy
            fields_str = ",\n".join(field_lines)
            jq_parts.append(fields_str)
            jq_parts.append(f"    }}")  # Đóng đối tượng

        # Kết hợp tất cả các đối tượng vào một biểu thức jq
        jq_query = "{\n" + ",\n".join(jq_parts) + "\n}"
        jq_query = jq_query.replace("{,", "{")
        return jq_query

    def _validate_start_connector(self, connector_detail):
        source_key = connector_detail.get("source_key")
        source_type = connector_detail.get("source_type")
        data_type = connector_detail.get("data_type", ConstantDataType.DATA_IN)
        config_connect = connector_detail.get("config_connect")
        merchant_id = connector_detail.get("merchant_id")

        config_mapping_data = connector_detail.get("config_mapping_data")
        source_key = connector_detail.get("source_key")
        type_sync_data = connector_detail.get("config_sync_calendar", {}).get("mode")
        if source_key not in [
            ConstantSourceKey.DataTypeIn.MYSQL,
            ConstantSourceKey.DataTypeIn.POSTGRES,
            ConstantSourceKey.DataTypeIn.ORACLE,
            ConstantSourceKey.DataTypeIn.DB2,
            ConstantSourceKey.DataTypeIn.SQLSERVER,
        ]:
            return
        if not config_mapping_data:
            return
        table_name = config_mapping_data.get("table")
        schema = config_mapping_data.get("schema")
        if not table_name:
            return
        func_name = HelperGetInformationFromDatabase().get_func_get_information_from_database(
            "information_column_table", source_key, source_type, data_type
        )
        try:
            result_columns = HelperGetInformationFromDatabase().call_get(
                func_name, self.lang, config_connect, merchant_id, table_name, schema
            )
        except Exception as e:
            MobioLogging().error("DataflowController :: _validate_start_connector :: {}".format(str(e)))
            raise CustomError(str(e))
        # check primary key postgres mode streaming
        if (
            source_key in [ConstantSourceKey.DataTypeIn.POSTGRES, ConstantSourceKey.DataTypeIn.SQLSERVER]
            and type_sync_data == ConstantModeConfigSyncCalendar.STREAMING
        ):
            is_primary_key = False
            for column in result_columns:
                is_primary_key = column.get("is_primary_key")
                if is_primary_key:
                    return
            if not is_primary_key:
                raise CustomError(self.lang.get(LANG.STREAMING_MUST_HAVE_PRIMARY_KEY).get("message"))

    def update_connectors(self, connector_id):
        merchant_id = validate_merchant_header()
        account_id = get_param_value_temp("id")
        data_type = request.args.get("data_type", ConstantDataType.DATA_IN)
        body = request.get_json()
        self._validate_connector_id(connector_id)

        detail_connector = ConfigConnectorsModel().detail_connector_by_id(merchant_id, connector_id)
        if not detail_connector:
            raise CustomError(self.lang.get(LANG.NOT_FOUND).get("message"))

        source_key = detail_connector.get("source_key")
        source_type = detail_connector.get("source_type")

        data_handle = self._validate_and_smooth_update_connectors(
            body, data_type, source_type, source_key, detail_connector
        )

        mode_current_db_status_connect = detail_connector.get("status_connect")
        mode_current_db_connector = detail_connector.get("config_sync_calendar", {}).get("mode")
        mode_current_body_connector = data_handle.get("config_sync_calendar", {}).get("mode")
        if (
            mode_current_body_connector
            and mode_current_db_connector
            and mode_current_body_connector != mode_current_db_connector
            and mode_current_db_status_connect == ConstantStatusConnect.ON
        ):
            raise CustomError(
                self.lang.get(LANG.NEED_STOP_CONNECT_CHANGE_MODE).get("message").format(mode_current_body_connector)
            )

        compare_data_change = json_compare(detail_connector, body)
        MobioLogging().info("update_connectors :: compare_data_change:: {}".format(compare_data_change))
        data_handle = self._delete_key_not_update_from_fe(data_handle, detail_connector)
        source_key = (
            data_handle.get("source_key") if data_handle.get("source_key") else detail_connector.get("source_key")
        )
        source_type = (
            data_handle.get("source_type") if data_handle.get("source_type") else detail_connector.get("source_type")
        )
        if "config_connect" in data_handle:
            log_connection_information = self._validate_config_connect(
                source_key,
                source_type,
                data_handle["config_connect"],
                data_type,
                merchant_id,
            )
            data_handle["log_connection_information"] = log_connection_information

        name_in_detail = detail_connector.get("name")
        description_in_detail = detail_connector.get("description")
        name = name_in_detail
        description = description_in_detail
        if data_handle.get("name"):
            name = data_handle.get("name")

        object_primary = (
            data_handle.get(ConstantParamApi.BodyUpdateConnectors.OBJECT)
            if data_handle.get(ConstantParamApi.BodyUpdateConnectors.OBJECT)
            else detail_connector.get(ConstantParamApi.BodyUpdateConnectors.OBJECT)
        )
        object_attribute = (
            data_handle.get(ConstantParamApi.BodyUpdateConnectors.OBJECT_ATTRIBUTE)
            if data_handle.get(ConstantParamApi.BodyUpdateConnectors.OBJECT_ATTRIBUTE)
            else detail_connector.get(ConstantParamApi.BodyUpdateConnectors.OBJECT_ATTRIBUTE)
        )

        new_config_attribute = self._build_connector_config_attribute(object_primary, object_attribute)
        data_handle["config_attribute"] = new_config_attribute
        data_handle["object_attribute"] = new_config_attribute

        name = name.strip()
        if data_handle.get("description"):
            description = data_handle.get("description")
        keyword = self._build_keyword_search(name, description)
        data_handle["keywords"] = keyword

        config_connect = (
            data_handle.get("config_connect")
            if data_handle.get("config_connect")
            else detail_connector.get("config_connect")
        )

        connector_identification = self._build_connector_identification(config_connect)
        data_handle["connector_identification"] = connector_identification
        name_ascii = self._build_connector_name_ascii(name)
        self._check_connector_name_exist(merchant_id, name_ascii, data_type, connector_id)
        data_handle["name_ascii"] = name_ascii
        action_time_change_status_connect = detail_connector.get("action_time_change_status_connect")
        mode_current_body_status_connect = body.get("status_connect")
        # if mode_current_body_status_connect == ConstantStatusConnect.ON:
        # streaming postges phai co pk
        data_validate_start_connector = copy.deepcopy(data_handle)
        data_validate_start_connector["config_connect"] = config_connect
        self._validate_start_connector(data_validate_start_connector)

        if (
            mode_current_db_status_connect == ConstantStatusConnect.OFF
            and mode_current_body_status_connect == ConstantStatusConnect.ON
        ):
            action_time_change_status_connect = datetime.datetime.now(datetime.UTC)
        data_handle.update({"action_time_change_status_connect": action_time_change_status_connect})

        config_mapping_data = data_handle.get("config_mapping_data")
        if config_mapping_data:
            fields = config_mapping_data.get("fields")
            if fields:
                query_jq = self._build_jq_query_by_mapping_field(fields)
                MobioLogging().info("modified_content_connector :: query_jq:: {}".format(query_jq))
                data_handle["query_jq"] = query_jq

        sort_time_current = detail_connector.get("sort_time")
        status_update = ConfigConnectorsModel().update_config_connector_by_id(connector_id, data_handle, account_id, sort_time_current)
        MobioLogging().info("update_connectors :: status_update:: {}".format(status_update))
        body.update({"state": {"type": ConstantConnectorStateCode.UPDATE_CONNECTOR}})

        self._update_session_connector_to_mode(
            merchant_id, connector_id, mode_current_db_connector, mode_current_body_connector
        )

        HandleQueue.push_message_connector_config(
            connector_id,
            detail_connector,
            body,
            "update",
            merchant_id,
            account_id,
        )
        detail_connector = ConfigConnectorsModel().detail_connector_by_id(merchant_id, connector_id)
        detail_connector = self._convert_detail_connector(detail_connector)
        return {"data": detail_connector}

    def _update_session_connector_to_mode(self, merchant_id, connector_id, mode_old, mode_new):
        if mode_old == mode_new:
            return
        mode_update = mode_new
        if mode_new == ConstantModeConfigSyncCalendar.SNAPSHOT:
            mode_update = "batch"
        status_update = DailyReportsModel().update_by_set(
            {"merchant_id": merchant_id, "connector_id": connector_id}, {"mode": mode_update}
        )
        MobioLogging().info("update_session_connector_to_mode :: status_update:: {}".format(status_update))
        return status_update

    def _validate_and_smooth_modified_content_connectors(
        self, data_validate, detail_connector, data_type, source_type, source_key
    ):
        return HandlerValidator().call(
            data_type,
            source_type,
            source_key,
            "modified_content_connectors",
            data_validate,
            detail_connector,
        )

    def _delete_key_not_update_from_fe(self, data_update, detail_connector):
        for k in [
            "created_by",
            "updated_by",
            "created_time",
            "updated_time",
            "config_connect",
        ]:
            if k in data_update:
                if k == "config_connect":
                    body_config_connect = data_update.get(k)
                    database_config_connect = detail_connector.get(k)
                    result_config_connect = any(value == "*" * 15 for value in body_config_connect.values())
                    if result_config_connect:
                        del data_update[k]
                    else:
                        database_config_connect.update(body_config_connect)
                        data_update[k] = database_config_connect
                else:
                    del data_update[k]
        return data_update

    def modified_content_connector(self, connector_id):
        merchant_id = validate_merchant_header()
        account_id = get_param_value_temp("id")
        body = request.get_json()
        self._validate_connector_id(connector_id)
        data_type = request.args.get("data_type")

        detail_connector = ConfigConnectorsModel().detail_connector_by_id(merchant_id, connector_id)
        if not detail_connector:
            raise CustomError(self.lang.get(LANG.NOT_FOUND).get("message"))
        source_type = detail_connector.get("source_type")
        source_key = detail_connector.get("source_key")

        data_handle = self._validate_and_smooth_modified_content_connectors(
            body, detail_connector, data_type, source_type, source_key
        )

        mode_current_db_status_connect = detail_connector.get("status_connect")
        mode_current_db_connector = detail_connector.get("config_sync_calendar", {}).get("mode")
        mode_current_body_connector = (
            body.get("config_sync_calendar", {}).get("mode") if "config_sync_calendar" in body else None
        )
        if (
            mode_current_body_connector
            and mode_current_db_connector
            and mode_current_body_connector != mode_current_db_connector
            and mode_current_db_status_connect == ConstantStatusConnect.ON
        ):
            raise CustomError(
                self.lang.get(LANG.NEED_STOP_CONNECT_CHANGE_MODE).get("message").format(mode_current_body_connector)
            )

        compare_data_change = json_compare(detail_connector, body)
        MobioLogging().info("modified_content_connector :: compare_data_change:: {}".format(compare_data_change))

        name_in_body = body.get(ConstantParamApi.BodyCreateConnectors.NAME)
        description_in_body = body.get(ConstantParamApi.BodyCreateConnectors.DESCRIPTION, "")

        name_in_detail = detail_connector.get("name")
        description_in_detail = detail_connector.get("description")
        name = name_in_detail
        description = description_in_detail
        if name_in_body:
            name = name_in_body
        if description_in_body:
            description = description_in_body
        keyword = self._build_keyword_search(name, description)
        data_handle["keywords"] = keyword
        name_ascii = self._build_connector_name_ascii(name)
        self._check_connector_name_exist(merchant_id, name_ascii, data_type, connector_id)
        data_handle["name_ascii"] = name_ascii

        data_handle = self._delete_key_not_update_from_fe(data_handle, detail_connector)
        source_key = (
            data_handle.get("source_key") if data_handle.get("source_key") else detail_connector.get("source_key")
        )
        source_type = (
            data_handle.get("source_type") if data_handle.get("source_type") else detail_connector.get("source_type")
        )
        if "config_connect" in data_handle:
            log_connection_information = self._validate_config_connect(
                source_key,
                source_type,
                data_handle["config_connect"],
                data_type,
                merchant_id,
            )
            data_handle["log_connection_information"] = log_connection_information
        config_connect = (
            data_handle.get("config_connect")
            if data_handle.get("config_connect")
            else detail_connector.get("config_connect")
        )

        connector_identification = self._build_connector_identification(config_connect)
        data_handle["connector_identification"] = connector_identification

        mode_current_body_status_connect = body.get("status_connect")

        action_time_change_status_connect = detail_connector.get("action_time_change_status_connect")

        if (
            mode_current_db_status_connect == ConstantStatusConnect.OFF
            and mode_current_body_status_connect == ConstantStatusConnect.ON
        ):
            action_time_change_status_connect = datetime.datetime.now(datetime.UTC)
        data_handle.update({"action_time_change_status_connect": action_time_change_status_connect})

        object_primary = (
            data_handle.get(ConstantParamApi.BodyUpdateConnectors.OBJECT)
            if data_handle.get(ConstantParamApi.BodyUpdateConnectors.OBJECT)
            else detail_connector.get(ConstantParamApi.BodyUpdateConnectors.OBJECT)
        )
        object_attribute = (
            data_handle.get(ConstantParamApi.BodyUpdateConnectors.OBJECT_ATTRIBUTE)
            if data_handle.get(ConstantParamApi.BodyUpdateConnectors.OBJECT_ATTRIBUTE)
            else detail_connector.get(ConstantParamApi.BodyUpdateConnectors.OBJECT_ATTRIBUTE)
        )

        new_config_attribute = self._build_connector_config_attribute(object_primary, object_attribute)
        data_handle["config_attribute"] = new_config_attribute
        data_handle["object_attribute"] = new_config_attribute

        config_mapping_data = data_handle.get("config_mapping_data")
        if config_mapping_data:
            fields = config_mapping_data.get("fields")
            if fields:
                query_jq = self._build_jq_query_by_mapping_field(fields)
                MobioLogging().info("modified_content_connector :: query_jq:: {}".format(query_jq))
                data_handle["query_jq"] = query_jq

        mode_current_body_status_connect = body.get("status_connect")
        if mode_current_body_status_connect == ConstantStatusConnect.ON:
            data_validate_start_connector = copy.deepcopy(data_handle)
            data_validate_start_connector["config_connect"] = config_connect
            self._validate_start_connector(data_validate_start_connector)

        sort_time_current = detail_connector.get("sort_time")
        status_update = ConfigConnectorsModel().update_config_connector_by_id(connector_id, data_handle, account_id, sort_time_current)
        MobioLogging().info("modified_content_connector :: status_update:: {}".format(status_update))
        body.update({"state": {"type": ConstantConnectorStateCode.UPDATE_CONNECTOR}})

        # mode_current_db_status_connect = detail_connector.get("status_connect")

        self._update_session_connector_to_mode(
            merchant_id, connector_id, mode_current_db_connector, mode_current_body_connector
        )

        HandleQueue.push_message_connector_config(
            connector_id,
            detail_connector,
            body,
            "update",
            merchant_id,
            account_id,
        )
        detail_connector = ConfigConnectorsModel().detail_connector_by_id(merchant_id, connector_id)
        detail_connector = self._convert_detail_connector(detail_connector)
        return {"data": detail_connector}

    def detail_connectors(self, connector_id):
        merchant_id = validate_merchant_header()
        self._validate_connector_id(connector_id)

        detail_connector = ConfigConnectorsModel().detail_connector_by_id(merchant_id, connector_id)
        if not detail_connector:
            raise CustomError(self.lang.get(LANG.NOT_FOUND).get("message"))
        detail_connector = self._convert_detail_connector(detail_connector)
        return {"data": detail_connector}

    def _convert_detail_connector(self, connector, debug=False):
        created_time = connector["created_time"]
        if isinstance(created_time, datetime.datetime):
            created_time = created_time.strftime("%Y-%m-%dT%H:%MZ")
        connector["created_time"] = created_time
        updated_time = connector["updated_time"]
        if isinstance(updated_time, datetime.datetime):
            updated_time = updated_time.strftime("%Y-%m-%dT%H:%MZ")
        connector["updated_time"] = updated_time

        last_datetime_sync_data = connector.get("last_datetime_sync_data")
        if last_datetime_sync_data and isinstance(last_datetime_sync_data, datetime.datetime):
            last_datetime_sync_data = last_datetime_sync_data.strftime("%Y-%m-%dT%H:%MZ")
            connector["last_datetime_sync_data"] = last_datetime_sync_data

        if not debug:
            if ConstantParameterConnector.LOG_CONNECTION_INFORMATION in connector:
                connector.pop(ConstantParameterConnector.LOG_CONNECTION_INFORMATION)

        config_connect = connector.get("config_connect", {})
        if config_connect:
            if "database_password" in config_connect:
                config_connect["database_password"] = "*" * 15
            connector["config_connect"] = config_connect

        if "state" in connector:
            connector.pop("state")
        connector[ConstantParameterConnector.IS_TRUST_SOURCE] = connector.get(
            ConstantParameterConnector.IS_TRUST_SOURCE, False
        )

        connector[ConstantParameterConnector.IS_TYPE_SYNC_MANUALLY] = False
        status_pipeline = connector.get("status_pipeline")
        if ConstantParameterConnector.CONFIG_SYNC_CALENDAR in connector:
            config_sync_calendar = connector.get(ConstantParameterConnector.CONFIG_SYNC_CALENDAR)
            if (
                config_sync_calendar
                and config_sync_calendar.get("schedule", {}).get("type") == ConstantScheduleType.MANUALLY
            ):
                connector[ConstantParameterConnector.IS_TYPE_SYNC_MANUALLY] = True
                if status_pipeline and status_pipeline == ConstantStatusSyncData.RUNNING:
                    connector[ConstantParameterConnector.IS_TYPE_SYNC_MANUALLY] = False
        if "config_information_out" in connector and connector["config_information_out"]:
            object_configs = [
                config["module"] for config in connector["config_information_out"] if config.get("module")
            ]
            connector["object_configs"] = object_configs

        if "status_connect" not in connector:
            connector["status_connect"] = ConstantStatusConnect.OFF
        if "keywords" in connector:
            connector.pop("keywords")
        if "data_type" in connector:
            connector.pop("data_type")
        if "name_ascii" in connector:
            connector.pop("name_ascii")
        if "sort_time" in connector:
            connector.pop("sort_time")

        mapping_status_sync = {
            "done": "done",
            "running": "running",
            "stop": "stop",
            "stopped": "stop",
            "failed": "failed",
            "prepare": "prepare",
        }
        if "status_sync" in connector:
            connector["status_sync"] = mapping_status_sync.get(connector["status_sync"], connector["status_sync"])
        return connector

    def _validate_connector_action_sync(self, data_validate):
        rule_validate = {
            "action": [
                Required,
                InstanceOf(str),
                Length(1),
                In(ConstantActionSyncData.get_all_attribute()),
            ]
        }
        self.abort_if_validate_error(rule_validate, data_validate)

    def connector_action_sync(self, connector_id):
        merchant_id = validate_merchant_header()
        account_id = get_param_value_temp("id")
        body = request.get_json()
        self._validate_connector_id(connector_id)
        self._validate_connector_action_sync(body)

        detail_connector = ConfigConnectorsModel().detail_connector_by_id(merchant_id, connector_id)
        if not detail_connector:
            raise CustomError(self.lang.get(LANG.NOT_FOUND).get("message"))
        data_type = detail_connector.get("data_type")

        action_sync = body.get("action")

        # pre_status_sync = detail_connector.get("status_sync")
        state_sync_orchestration = detail_connector.get("state_sync_orchestration")
        if state_sync_orchestration == "processing":
            raise CustomError("Connector is syncing data, please wait for it to finish.")
        ConfigConnectorsModel().update_by_set(
            {"merchant_id": merchant_id, "connector_id": connector_id}, {"state_sync_orchestration": "processing"}
        )

        data_sync = None
        if action_sync == ConstantActionSyncData.OFF:
            config_sync_calendar = detail_connector.get("config_sync_calendar")
            if not config_sync_calendar or (
                data_type == ConstantDataType.DATA_IN
                and config_sync_calendar.get("mode") != ConstantModeConfigSyncCalendar.SNAPSHOT
            ):
                ConfigConnectorsModel().update_by_set(
                    {"merchant_id": merchant_id, "connector_id": connector_id}, {"state_sync_orchestration": "done"}
                )
                raise CustomError(self.lang.get(LANG.NOT_ON_SYNC_DATA).get("message"))
            schedule_config = config_sync_calendar.get("schedule", {})
            schedule_config_type = schedule_config.get("type")
            if data_type == ConstantDataType.DATA_IN and schedule_config_type != ConstantScheduleType.MANUALLY:
                raise CustomError(self.lang.get(LANG.NOT_ON_SYNC_DATA).get("message"))
            orchestration_id = LogSyncDataToModuleOtherModel().get_orchestration_id_by_connector_id(
                merchant_id=merchant_id, connector_id=connector_id
            )
            if not orchestration_id:
                ConfigConnectorsModel().update_by_set(
                    {"merchant_id": merchant_id, "connector_id": connector_id}, {"state_sync_orchestration": "done"}
                )
                raise CustomError(self.lang.get(LANG.NOT_ON_SYNC_DATA).get("message"))
            off_pipeline = OrchestrationApiHelper().stop_pipeline(merchant_id, orchestration_id)
            if not off_pipeline:
                ConfigConnectorsModel().update_by_set(
                    {"merchant_id": merchant_id, "connector_id": connector_id}, {"state_sync_orchestration": "done"}
                )
                raise CustomError("Not sync information orchestration")

            MobioLogging().info("DataFlowController :: connector_action_sync :: on_pipeline :: {}".format(off_pipeline))
            data_sync = off_pipeline

        if action_sync == ConstantActionSyncData.ON:
            self._validate_start_connector(detail_connector)
            config_sync_calendar = detail_connector.get("config_sync_calendar")
            if not config_sync_calendar or (
                data_type == ConstantDataType.DATA_IN
                and config_sync_calendar.get("mode") != ConstantModeConfigSyncCalendar.SNAPSHOT
            ):
                ConfigConnectorsModel().update_by_set(
                    {"merchant_id": merchant_id, "connector_id": connector_id}, {"state_sync_orchestration": "done"}
                )
                raise CustomError(self.lang.get(LANG.NOT_ON_SYNC_DATA).get("message"))
            schedule_config = config_sync_calendar.get("schedule", {})
            schedule_config_type = schedule_config.get("type")
            if data_type == ConstantDataType.DATA_IN and schedule_config_type != ConstantScheduleType.MANUALLY:
                ConfigConnectorsModel().update_by_set(
                    {"merchant_id": merchant_id, "connector_id": connector_id}, {"state_sync_orchestration": "done"}
                )
                raise CustomError(self.lang.get(LANG.NOT_ON_SYNC_DATA).get("message"))
            orchestration_id = LogSyncDataToModuleOtherModel().get_orchestration_id_by_connector_id(
                merchant_id=merchant_id, connector_id=connector_id
            )
            if not orchestration_id:
                ConfigConnectorsModel().update_by_set(
                    {"merchant_id": merchant_id, "connector_id": connector_id}, {"state_sync_orchestration": "done"}
                )
                raise CustomError(self.lang.get(LANG.NOT_ON_SYNC_DATA).get("message"))
            on_pipeline = OrchestrationApiHelper().start_pipeline(merchant_id, orchestration_id)
            if not on_pipeline:
                ConfigConnectorsModel().update_by_set(
                    {"merchant_id": merchant_id, "connector_id": connector_id}, {"state_sync_orchestration": "done"}
                )
                raise CustomError("Not sync information orchestration")

            MobioLogging().info("DataFlowController :: connector_action_sync :: on_pipeline :: {}".format(on_pipeline))
            data_sync = on_pipeline
            session_data = {
                "merchant_id": merchant_id,
                "connector_id": connector_id,
                "session_id": data_sync.get("session_id"),
                "mode": ConstantModeConfigSyncCalendar.SNAPSHOT,
                "schedule_config_type": ConstantScheduleType.MANUALLY,
                "agent": account_id,
            }
            detail_session = ConfigSessionModel().detail_session_by_id(merchant_id, data_sync.get("session_id"))
            if not detail_session:
                ConfigSessionModel().insert_new_session(**session_data)
            else:
                ConfigSessionModel().update_session_by_id(detail_session.get("_id"), session_data)
        # Tra them cho FE detail connector
        ConfigConnectorsModel().update_by_set(
            {"merchant_id": merchant_id, "connector_id": connector_id}, {"state_sync_orchestration": "done"}
        )
        detail_connector = ConfigConnectorsModel().detail_connector_by_id(merchant_id, connector_id)
        connector = self._convert_detail_connector(detail_connector, False)
        data_sync.update({"detail_connector": connector})
        return {"data": data_sync}

    @classmethod
    def validate_schedule_type_manually(cls, detail_connector):
        config_sync_calendar = detail_connector.get("config_sync_calendar")
        mode_sync = config_sync_calendar.get("mode")
        if not config_sync_calendar:
            return False
        schedule_config = config_sync_calendar.get("schedule", {})
        schedule_config_type = schedule_config.get("type")
        if mode_sync and schedule_config_type == ConstantScheduleType.MANUALLY:
            return True
        return False

    def _validate_update_status_sync_data(self, data_validate):
        rule_validate = {
            "status_sync": [
                Required,
                InstanceOf(str),
                Length(1),
                In(ConstantStatusSyncData.get_all_attribute()),
            ],
            "orchestration_id": [Required, InstanceOf(str), Length(1)],
            "reason": [],
            "session_id": [],
        }
        self.validate_optional_err(rule_validate, data_validate)

    def update_status_sync_data(self, connector_id):
        merchant_id = validate_merchant_header()
        body = request.get_json()

        self._validate_update_status_sync_data(body)

        status_sync = body["status_sync"]
        reason = body.get("reason")
        session_id = body.get("session_id")

        detail_connector = ConfigConnectorsModel().detail_connector_by_id(merchant_id, connector_id)
        if not detail_connector:
            raise CustomError(self.lang.get(LANG.NOT_FOUND).get("message"))
        status_connect_in_database = detail_connector.get("status_connect")
        status_pipeline = status_sync
        # status_connect = ConstantStatusConnect.OFF
        if status_sync in [
            ConstantStatusSyncData.NOT_DATA_SYNC,
            ConstantStatusSyncData.RUNNING,
            ConstantStatusSyncData.PENDING,
            ConstantStatusSyncData.PREPARE,
        ]:
            status_connect_in_database = ConstantStatusConnect.ON

        data_update = {
            "status_connect": status_connect_in_database,
            "reason_connect": reason,
            "status_sync": status_sync,
            "status_pipeline": status_pipeline,
        }

        status_update = ConfigConnectorsModel().update_status_connect_by_orchestration(
            merchant_id=merchant_id,
            connector_id=connector_id,
            status_connect=status_connect_in_database,
            status_sync=status_sync,
            reason=reason,
        )

        if not status_update:
            raise CustomError("Update status connect failed")

        # lưu agent cho loai interval
        config_sync_calendar = detail_connector.get("config_sync_calendar", {})
        if (
            session_id
            and config_sync_calendar
            and config_sync_calendar.get("mode") == ConstantModeConfigSyncCalendar.SNAPSHOT
        ):
            schedule = config_sync_calendar.get("schedule", {})
            if schedule and schedule.get("type") == ConstantScheduleType.INTERVAL:
                session_data = {
                    "merchant_id": merchant_id,
                    "connector_id": connector_id,
                    "session_id": session_id,
                    "mode": ConstantModeConfigSyncCalendar.SNAPSHOT,
                    "schedule_config_type": ConstantScheduleType.INTERVAL,
                    "schedule": schedule,
                }
                detail_session = ConfigSessionModel().detail_session_by_id(merchant_id, session_id)
                if not detail_session:
                    ConfigSessionModel().insert_new_session(**session_data)
                else:
                    ConfigSessionModel().update_session_by_id(detail_session.get("_id"), session_data)

        HandleQueue.push_message_connector_config(
            connector_id,
            detail_connector,
            data_update,
            "update",
            merchant_id,
            None,
        )
        # Tra them cho FE detail connector
        data_sync = {}
        detail_connector = ConfigConnectorsModel().detail_connector_by_id(merchant_id, connector_id)
        connector = self._convert_detail_connector(detail_connector, False)
        data_sync.update({"detail_connector": connector})
        return {"data": data_sync}

    def get_state_connector(self, connector_id):
        merchant_id = validate_merchant_header()
        per_page = int(request.args.get("per_page", 10))
        after_token = request.args.get("after_token")
        history_data, after_token = list(
            DataHistoryModel().get_state_connector_by_id(connector_id, merchant_id, per_page, after_token)
        )
        results = []
        for history in history_data:
            data_after = history.get("data_after")
            results.append(
                JSONEncoder("%Y-%m-%dT%H:%MZ").json_loads(
                    {
                        "merchant_id": merchant_id,
                        "connector_id": int(connector_id),
                        "state": data_after.get("state"),
                        "action_time": history.get("time_action"),
                        "account_id": history.get("account_id"),
                    }
                )
            )
        return {
            "data": results,
            "paging": {"cursors": {"after_token": after_token, "before_token": ""}},
        }

    def get_sources_connections(self):
        merchant_id = validate_merchant_header()
        search = request.args.get("search", None)
        data_type = request.args.get("data_type", ConstantDataType.DATA_IN)
        aggregate_source_connections = ConfigConnectorsModel().aggregate_source_connections(
            merchant_id, search, data_type
        )
        results = []

        count_number_by_source_key = ConfigConnectorsModel().count_connector_by_source_keys(
            merchant_id, [], search, data_type
        )
        mapping_source_key_number = {}
        for count_number in count_number_by_source_key:
            mapping_source_key_number[count_number.get("source_key")] = count_number.get("count")
        for aggregate_source_connection in [*aggregate_source_connections]:
            detail_connector = aggregate_source_connection.get("detail_connector")
            source_key = aggregate_source_connection.get("source_key")
            source_type = aggregate_source_connection.get("source_type")
            number_connector = mapping_source_key_number.get(source_key, 0)
            item = {
                "source_key": source_key,
                "source_type": source_type,
                "number_connector": number_connector,
            }

            if detail_connector and number_connector:
                item.update({"detail_connector": self._convert_detail_connector(detail_connector)})
            results.append(item)

        return {
            "data": results,
        }

    def get_connector_of_sources_connections_by_source_type(self, source_key):
        merchant_id = validate_merchant_header()
        search = request.args.get("search", None)
        data_type = request.args.get("data_type", ConstantDataType.DATA_IN)
        connectors = ConfigConnectorsModel().get_connector_by_source_key(merchant_id, source_key, search, data_type)
        results = []
        for connector in connectors:
            results.append(self._convert_detail_connector(connector))

        return {
            "data": results,
        }

    def get_related_objects_by_object_primary(self, object_primary_key):
        merchant_id = validate_merchant_header()
        object_attribute = request.args.get("object_attribute")
        key_get_object_relate = (
            "{}-{}".format(object_primary_key, object_attribute) if object_attribute else object_primary_key
        )
        config_result = {
            "profiles": [
                {"key": "profiles", "name": {"vi": "Profiles", "en": "Profiles"}, "order": 1, "status": 1},
                {"key": "company", "name": {"vi": "Công ty", "en": "Company"}, "order": 2, "status": 0},
            ],
            "profiles-{}".format(ConstantObjectHandle.ObjectAttribute.PROFILE_ATTRIBUTE): [
                {"key": "profiles", "name": {"vi": "Profiles", "en": "Profiles"}, "order": 1, "status": 1},
                {"key": "company", "name": {"vi": "Công ty", "en": "Company"}, "order": 2, "status": 0},
            ],
            "company": [
                {"key": "profiles", "name": {"vi": "Profiles", "en": "Profiles"}, "order": 1, "status": 1},
                {"key": "company", "name": {"vi": "Công ty", "en": "Company"}, "order": 2, "status": 1},
            ],
            "sale": [
                {"key": "sale", "name": {"vi": "Cơ hội bán", "en": "Oppty"}, "order": 1, "status": 1},
                {"key": "profiles", "name": {"vi": "Profiles", "en": "Profiles"}, "order": 2, "status": 1},
                {"key": "company", "name": {"vi": "Công ty", "en": "Company"}, "order": 3, "status": 0},
            ],
            "ticket": [
                {"key": "ticket", "name": {"vi": "Ticket", "en": "Ticket"}, "order": 1, "status": 1},
                {"key": "profiles", "name": {"vi": "Profiles", "en": "Profiles"}, "order": 2, "status": 1},
                {"key": "company", "name": {"vi": "Công ty", "en": "Company"}, "order": 3, "status": 0},
            ],
            "profiles-dynamic_event": [
                {"key": "profiles", "name": {"vi": "Profiles", "en": "Profiles"}, "order": 1, "status": 1},
                {"key": "company", "name": {"vi": "Công ty", "en": "Company"}, "order": 2, "status": 0},
                {
                    "key": "dynamic_event",
                    "name": {"vi": "Event tuỳ biến", "en": "Dynamic Event"},
                    "order": 3,
                    "status": 1,
                },
            ],
            "profiles-product_holding": [
                {"key": "profiles", "name": {"vi": "Profiles", "en": "Profiles"}, "order": 1, "status": 1},
                {"key": "company", "name": {"vi": "Công ty", "en": "Company"}, "order": 2, "status": 0},
                {
                    "key": "product_holding",
                    "name": {"vi": "Product Holding", "en": "Product Holding"},
                    "order": 3,
                    "status": 1,
                },
            ],
        }
        result = config_result.get(key_get_object_relate)
        if not result:
            raise CustomError("Object {} not supported.".format(object_primary_key))
        nomalize_result = []
        for item in result:
            item["name"] = item["name"].get(self.language)
            nomalize_result.append(item)
        return {
            "data": nomalize_result,
        }

    @classmethod
    def encrypt_information_connect(cls, config_connect):
        pass

    def _validate_update_status_connect(self, data_validate):
        rule_validate = {
            "action": [
                Required,
                InstanceOf(str),
                Length(1),
                In(ConstantStatusConnect.get_all_attribute()),
            ]
        }
        self.abort_if_validate_error(rule_validate, data_validate)

    def update_status_connect(self, connector_id):
        merchant_id = validate_merchant_header()
        account_id = get_param_value_temp("id")
        body = request.get_json()
        self._validate_connector_id(connector_id)
        self._validate_connector_action_sync(body)

        detail_connector = ConfigConnectorsModel().detail_connector_by_id(merchant_id, connector_id)
        if not detail_connector:
            raise CustomError(self.lang.get(LANG.NOT_FOUND).get("message"))

        config_connect = detail_connector.get("config_connect")
        if config_connect:
            config_connect_app_id = config_connect.get("app_id")
            if config_connect_app_id:
                app_detail = ConfigAppModel().app_detail_by_id(config_connect_app_id)
                if not app_detail:
                    raise CustomError(self.lang.get(LANG.APP_NOT_EXISTS).get("message"))

        action_connect = body.get("action")
        orchestration_id = LogSyncDataToModuleOtherModel().get_orchestration_id_by_connector_id(
            merchant_id=merchant_id, connector_id=connector_id
        )
        MobioLogging().info(
            "DataFlowController :: update_status_connect :: orchestration_id :: {}".format(orchestration_id)
        )

        status_sync = detail_connector.get("status_sync")
        if action_connect == ConstantStatusConnect.OFF:
            if orchestration_id:
                off_pipeline = OrchestrationApiHelper().stop_pipeline(merchant_id, orchestration_id)
                if not off_pipeline:
                    raise CustomError("Not sync information orchestration")

                MobioLogging().info(
                    "DataFlowController :: update_status_connect :: off_pipeline :: {}".format(off_pipeline)
                )
            if status_sync == ConstantStatusSyncData.RUNNING:
                status_sync = ConstantStatusSyncData.STOPPED
            # status_sync = None

        if action_connect == ConstantActionSyncData.ON:
            self._validate_start_connector(detail_connector)
            if detail_connector.get("config_sync_calendar"):
                orchestration_id = OrchestrationBuildHelper().handle_sync_data(merchant_id, connector_id)
                MobioLogging().info(
                    "DataFlowController :: update_status_connect :: sync_data :: orchestration_id :: {}".format(
                        orchestration_id
                    )
                )
                if orchestration_id:
                    type_sync_data = detail_connector.get("config_sync_calendar", {}).get("mode")
                    status_sync = None
                    if type_sync_data == ConstantModeConfigSyncCalendar.STREAMING:
                        status_sync = ConstantStatusSyncData.RUNNING
                        on_pipeline = OrchestrationApiHelper().start_pipeline(merchant_id, orchestration_id)
                        if not on_pipeline:
                            raise CustomError("Not sync information orchestration")
                        MobioLogging().info(
                            "DataFlowController :: update_status_connect :: on_pipeline :: {}".format(on_pipeline)
                        )
                        pipeline_session_id = on_pipeline.get("session_id")
                        LogSyncDataToModuleOtherModel().update_by_set(
                            {
                                "merchant_id": merchant_id,
                                "connector_id": int(connector_id),
                            },
                            {"module_data.session_id": pipeline_session_id},
                        )

        sort_time_current = detail_connector.get("sort_time")
        status_query_update = ConfigConnectorsModel().update_status_connect(
            merchant_id, connector_id, account_id, action_connect, status_sync, sort_time_current
        )
        MobioLogging().info(
            "DataFlowController :: update_status_connect :: status_query_update :: {}".format(status_query_update)
        )
        if not status_query_update:
            raise CustomError(self.lang.get(LANG.NOT_ON_SYNC_DATA).get("message"))

        data_update = {
            ConstantConfigConnectorsModel.UPDATED_BY: account_id,
            ConstantConfigConnectorsModel.UPDATED_TIME: datetime.datetime.now(datetime.UTC),
            "status_connect": action_connect,
            "status_sync": status_sync,
        }

        HandleQueue.push_message_connector_config(
            connector_id,
            detail_connector,
            data_update,
            "update_and_synced_orchestration",
            merchant_id,
            account_id,
        )
        # Lay du lieu moi nhat detail connector tra ve cho FE
        detail_connector = ConfigConnectorsModel().detail_connector_by_id(merchant_id, connector_id)
        result = self._convert_detail_connector(detail_connector, False)
        print(detail_connector)
        return {"data": result}

    def _validate_delete_connectors(self, data_validate):
        rule_validate = {"ids": [Required, InstanceOf(list), Length(1)]}
        self.abort_if_validate_error(rule_validate, data_validate)

    def delete_connectors(self):
        merchant_id = validate_merchant_header()
        account_id = get_param_value_temp("id")

        connector_ids = request.args.get("ids", type=str)
        if not connector_ids:
            raise CustomError(self.lang.get(LANG.NOT_FOUND).get("message"))
        if connector_ids:
            connector_ids = connector_ids.split(",")

        self._validate_delete_connectors({"ids": connector_ids})

        parse_connector_ids = []
        for connector_id in connector_ids:
            self._validate_connector_id(connector_id)
            parse_connector_ids.append(int(connector_id))

        connectors = ConfigConnectorsModel().get_connectors_by_ids(merchant_id, parse_connector_ids)
        success_ids = []

        for connector in connectors:
            connector_id = connector.get("_id")
            status_sync = connector.get("status_sync")
            if status_sync == ConstantStatusSyncData.RUNNING:
                raise CustomError(self.lang.get(LANG.DELETE_CONNECTOR_FAIL_BY_CONNECTOR_IN_RUNNING).get("message"))
            success_ids.append(connector_id)
            orchestration_id = LogSyncDataToModuleOtherModel().get_orchestration_id_by_connector_id(
                merchant_id=merchant_id, connector_id=connector_id
            )
            MobioLogging().info(
                "Start delete_connectors :: connector_id :: {}, orchestration_id :: {}".format(
                    connector_id, orchestration_id
                )
            )
            if orchestration_id:
                response_delete = OrchestrationApiHelper().delete_pipeline(
                    merchant_id, orchestration_id=orchestration_id
                )
                MobioLogging().info(
                    "Start delete_connectors :: connector_id :: {}, response_delete :: {}".format(
                        connector_id, response_delete
                    )
                )

                if not response_delete:
                    raise CustomError(self.lang.get(LANG.DELETE_CONNECTOR_FAIL_BY_CONNECTOR_IN_RUNNING).get("message"))
            ConfigConnectorsModel().delete_multi_connector_by_ids(merchant_id, [int(connector_id)])
            HandleQueue.push_message_connector_config(
                connector_id,
                connector,
                {},
                "delete",
                merchant_id,
                account_id,
            )

        return {
            "data": {
                "success": {"number": len(success_ids), "connector_ids": success_ids},
                "fail": {"number": 0},
            }
        }

    def _validate_decrypt_connector_config(self, data_validate):
        rule_validate = {"key": [Required, InstanceOf(list), Length(1)]}
        self.abort_if_validate_error(rule_validate, data_validate)

    def decrypt_connector_config(self, connector_id):
        merchant_id = validate_merchant_header()
        account_id = get_param_value_temp("id")

        self._validate_connector_id(connector_id)

        key_decrypt = request.args.get("key")
        if key_decrypt:
            key_decrypt = [key.strip() for key in key_decrypt.split(",")]
        self._validate_decrypt_connector_config({"key": key_decrypt})

        detail_connector = ConfigConnectorsModel().detail_connector_by_id(merchant_id, connector_id)
        if not detail_connector:
            raise CustomError(self.lang.get(LANG.NOT_FOUND).get("message"))

        config_connect = detail_connector.get("config_connect")
        if not config_connect:
            raise CustomError("Config connect not exist!")
        result = {}
        for k, v in config_connect.items():
            if k not in key_decrypt:
                continue
            result[k] = v
        return {"data": result}

    def _validate_send_data_test_data_out(self, data_validate):
        rule_validate = {
            "module": [Required, InstanceOf(str), Length(1)],
            "event_key": [Required, InstanceOf(str), Length(1)],
        }
        self.abort_if_validate_error(rule_validate, data_validate)

    def send_data_test_data_out(self, connector_id):
        merchant_id = validate_merchant_header()
        self._validate_connector_id(connector_id)

        body = request.get_json()

        self._validate_send_data_test_data_out(body)
        module = body.get("module")
        event_key = body.get("event_key")
        detail_connector = ConfigConnectorsModel().detail_connector_by_id(merchant_id, connector_id)
        if not detail_connector:
            raise CustomError(self.lang.get(LANG.NOT_FOUND).get("message"))

        config_connect = detail_connector.get("config_connect")
        if not config_connect:
            raise CustomError("Config connect not exist!")
        app_id = config_connect.get("app_id")
        param_headers = config_connect.get("param_headers")
        url = config_connect.get("url")
        app_detail = ConfigAppModel().app_detail_by_id(app_id)
        if not app_detail:
            raise CustomError(self.lang.get(LANG.APP_NOT_EXISTS_SET_APP_NEW).get("message"))

        event_detail = InformationEventDataOutModel().get_detail_event_by_event_key(merchant_id, module, event_key)
        if not event_detail:
            raise CustomError(self.lang.get(LANG.NOT_FOUND).get("message"))
        body = {
            "app_id": app_id,
            "app_secret": app_detail.get(ConfigAppField.SECRET_KEY),
            "event_key": event_key,
            "data_test": json.loads(event_detail.get("data_test")),
            "headers": param_headers,
            "webhook_url": url,
            "connector_id": int(connector_id),
        }
        MobioLogging().info("DataFlowController :: send_data_test_data_out :: body :: {}".format(body))
        status_connect, connect_information = DataOutHelper().send_data_test_data_out(merchant_id, body)
        MobioLogging().info(
            "DataFlowController :: send_data_test_data_out :: status_connect :: {}".format(status_connect)
        )
        result = {"status": status_connect, "information": connect_information}

        return {"data": result}

    def _validate_get_url_request_connector(self, data_validate):
        rule_validate = {
            "data_type": [
                Required,
                InstanceOf(str),
                Length(1),
                In(ConstantDataType.get_all_attribute()),
            ],
            "source_key": [Required, InstanceOf(str), Length(1)],
            "source_type": [Required, InstanceOf(str), Length(1)],
        }
        self.abort_if_validate_error(rule_validate, data_validate)

    def data_flow_get_url_request_connector(self):

        merchant_id = validate_merchant_header()
        data_type = request.args.get("data_type", ConstantDataType.DATA_IN)
        source_key = request.args.get("source_key")
        source_type = request.args.get("source_type")
        data_validate = {
            "data_type": data_type,
            "source_type": source_type,
            "source_key": source_key,
        }
        self._validate_get_url_request_connector(data_validate)
        url = HandlerGeneralParamDefault().call(data_type, source_type, source_key, "get_url_request", merchant_id)
        return {"data": {"url": url}}

    def _validate_data_flow_integration_guide(self, data_validate):
        rule_validate = {
            "data_type": [
                Required,
                InstanceOf(str),
                Length(1),
                In(ConstantDataType.get_all_attribute()),
            ],
            "source_key": [Required, InstanceOf(str), Length(1)],
            "source_type": [Required, InstanceOf(str), Length(1)],
            "object": [Required, InstanceOf(str), Length(1)],
            "object_attribute": [],
        }
        self.abort_if_validate_error(rule_validate, data_validate)

    def data_flow_integration_guide(self, connector_id):

        merchant_id = validate_merchant_header()
        data_type = request.args.get("data_type", ConstantDataType.DATA_IN)
        source_key = request.args.get("source_key")
        source_type = request.args.get("source_type")
        object_get_sample = request.args.get("object")
        object_attribute_sample = request.args.get("object_attribute")
        data_validate = {
            "data_type": data_type,
            "source_type": source_type,
            "source_key": source_key,
            "object": object_get_sample,
            "object_attribute": object_attribute_sample,
        }
        self._validate_data_flow_integration_guide(data_validate)

        detail_connector = ConfigConnectorsModel().detail_connector_by_id(merchant_id, connector_id)
        if not detail_connector:
            raise CustomError(self.lang.get(LANG.NOT_FOUND).get("message"))

        url = HandlerGeneralParamDefault().call(
            data_type,
            source_type,
            source_key,
            "get_integration_guide",
            merchant_id,
            object_get_sample,
            object_attribute_sample,
            detail_connector,
        )
        return {"data": url}

    def _validate_data_flow_api_sample_response(self, data_validate):
        rule_validate = {
            "data_type": [
                Required,
                InstanceOf(str),
                Length(1),
                In(ConstantDataType.get_all_attribute()),
            ],
            "source_key": [Required, InstanceOf(str), Length(1)],
            "source_type": [Required, InstanceOf(str), Length(1)],
            "object": [Required, InstanceOf(str), Length(1)],
            "object_attribute": [Required, InstanceOf(str), Length(1)],
        }
        self.abort_if_validate_error(rule_validate, data_validate)

    def data_flow_api_sample_response(self, connector_id):

        merchant_id = validate_merchant_header()
        data_type = request.args.get("data_type", ConstantDataType.DATA_IN)
        source_key = request.args.get("source_key")
        source_type = request.args.get("source_type")
        object_get_sample = request.args.get("object")
        object_attribute_sample = request.args.get(
            "object_attribute", ConstantObjectHandle.ObjectAttribute.PROFILE_ATTRIBUTE
        )
        data_validate = {
            "data_type": data_type,
            "source_type": source_type,
            "source_key": source_key,
            "object": object_get_sample,
            "object_attribute": object_attribute_sample,
        }
        self._validate_data_flow_api_sample_response(data_validate)

        detail_connector = ConfigConnectorsModel().detail_connector_by_id(merchant_id, connector_id)
        if not detail_connector:
            raise CustomError(self.lang.get(LANG.NOT_FOUND).get("message"))

        url = HandlerGeneralParamDefault().call(
            data_type,
            source_type,
            source_key,
            "get_api_sample_response",
            merchant_id,
            object_get_sample,
            object_attribute_sample,
            detail_connector,
        )
        return {"data": url}

    def _validate_detail_connector_by_ids(self, data_validate):
        rule_validate = {
            "connector_ids": [Required, InstanceOf(list), Length(1)],
        }
        self.abort_if_validate_error(rule_validate, data_validate)

    def detail_connector_by_ids(self):
        merchant_id = validate_merchant_header()

        body = request.get_json()
        self._validate_detail_connector_by_ids(body)

        connector_ids = body.get("connector_ids")

        parse_connector_ids = []
        for connector_id in connector_ids:
            if isinstance(connector_id, str):
                self._validate_connector_id(connector_id)
            parse_connector_ids.append(int(connector_id))

        connectors = ConfigConnectorsModel().get_detail_connector_by_ids(merchant_id, parse_connector_ids)

        results = []
        for connector in connectors:
            results.append(self._convert_detail_connector(connector))

        return {
            "data": results,
        }

    def _validate_upsert_setting_add_on_connector(self, data_validate):
        rule_validate = {
            "type_config": [Required, InstanceOf(str), Length(1)],
            "source_type": [Required, InstanceOf(str), Length(1)],
            "source_key": [Required, InstanceOf(str), Length(1)],
            "data_type": [Required, InstanceOf(str), Length(1)],
            "include_fields": [InstanceOf(list)],
            "extract_config": [InstanceOf(dict)],
        }
        self.validate_optional_err(rule_validate, data_validate)

    def upsert_setting_add_on_connector(self):
        # merchant_id = validate_merchant_header()
        body = request.get_json()

        self._validate_upsert_setting_add_on_connector(body)

        type_config = body.get("type_config")
        source_type = body.get("source_type")
        source_key = body.get("source_key")
        data_type = body.get("data_type")

        filter_option_upsert = {
            "type_config": type_config,
            "source_type": source_type,
            "source_key": source_key,
            "data_type": data_type,
        }
        upsert_id = SettingAddOnConnectorModel().upsert_setting(filter_option_upsert, body)

        return {"data": {"upsert_id": upsert_id}}

    def get_list_table_view_in_database(self, connector_id):
        merchant_id = validate_merchant_header()

        self._validate_connector_id(connector_id)
        search = request.args.get("search", "")

        detail_connector = ConfigConnectorsModel().detail_connector_by_id(merchant_id, connector_id)
        if not detail_connector:
            raise CustomError(self.lang.get(LANG.NOT_FOUND).get("message"))

        data_type = detail_connector.get("data_type")
        source_type = detail_connector.get("source_type")
        source_key = detail_connector.get("source_key")
        config_connect = detail_connector.get("config_connect")

        func_name = HelperGetInformationFromDatabase().get_func_get_information_from_database(
            "list_table_view", source_key, source_type, data_type
        )
        tables_views = HelperGetInformationFromDatabase().call_get(
            func_name, self.lang, config_connect, merchant_id, search
        )
        return {"data": tables_views}

    def total_connectors_source(self):
        merchant_id = validate_merchant_header()
        args = request.args
        data_type = args.get("data_type", ConstantDataType.DATA_IN)
        search = args.get("search")
        is_trust_source = args.get("is_trust_source")
        status_sync = args.get("status_sync")
        status_connect = args.get("status_connect")
        created_by = args.get("created_by")
        source_keys = args.get("source_keys")
        source_types = args.get("source_types")
        object_attribute = args.get("object_attribute")
        status_sync_conn = args.get("status_sync_conn")
        last_time_sync_start_time = args.get("last_time_sync_start_time")
        last_time_sync_end_time = args.get("last_time_sync_end_time")

        if is_trust_source is not None:
            is_trust_source = (
                False if is_trust_source.lower() == "false" else True if is_trust_source.lower() == "true" else None
            )
        # Thong nhat voi FE la gio UTC
        if last_time_sync_start_time:
            last_time_sync_start_time = convert_string_datetime_to_datetime(
                last_time_sync_start_time, self.FROM_FORMAT_DATETIME_UTC
            )
        if last_time_sync_end_time:
            last_time_sync_end_time = convert_string_datetime_to_datetime(
                last_time_sync_end_time, self.FROM_FORMAT_DATETIME_UTC
            )
        total_connector = ConfigConnectorsModel().get_total_connectors_by_condition(
            merchant_id,
            search,
            is_trust_source,
            status_connect,
            status_sync,
            source_keys,
            source_types,
            created_by,
            data_type,
            object_attribute,
            status_sync_conn,
            last_time_sync_start_time,
            last_time_sync_end_time,
        )
        results = {"total_connector": total_connector}

        return {"data": results}


if __name__ == "__main__":
    fields = [
        {
            "field_source": "default_value",
            "field_source_type": "string",
            "field_target": "source",
            "field_property": 2,
            "display_type": "single_line",
            "value_type": "fixed",
            "action": "overwrite_and_ignore_value_null",
            "object": "sale",
            "value_by_type_fixed": "Form",
        },
        {
            "field_source": "f_k_61104186519",
            "field_source_type": "string",
            "field_target": "supporter_ids",
            "field_property": 2,
            "display_type": "tags",
            "value_type": "record",
            "action": "overwrite_and_ignore_value_null",
            "object": "sale",
        },
        {
            "field_source": "_dyn_single_line_text_chu_1727838139844",
            "field_source_type": "string",
            "field_target": "assignee_id",
            "field_property": 2,
            "display_type": "dropdown_single_line",
            "value_type": "record",
            "action": "overwrite_and_ignore_value_null",
            "object": "sale",
        },
        {
            "field_source": "f_k_8ebc1944969",
            "field_source_type": "string",
            "field_target": "cif",
            "field_property": 2,
            "display_type": "multi_line",
            "value_type": "record",
            "action": "overwrite_and_ignore_value_null",
            "object": "profiles",
        },
    ]
    print(DataFlowController()._build_jq_query_by_mapping_field(fields))
