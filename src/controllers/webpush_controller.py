from datetime import datetime

from bson import ObjectId
from flask import request
from mobio.libs.logging import MobioLogging
from mobio.libs.validator import In, InstanceOf, Length, Required
from mobio.sdks.base.common.mobio_exception import CustomError
from mobio.sdks.base.controllers import BaseController

from src.common import (
    ConstantThirdPartyConfigStatus,
    ConstantTimeCaching,
    ConstantTypeThirdParty,
    LANG,
)
from src.common.handle_headers import get_param_value_temp, validate_merchant_header
from src.common.handle_queue import HandleQueue
from src.common.json_encoder import J<PERSON>NEncoder
from src.common.utils import json_compare, utf8_to_ascii
from src.helpers.caching_helpers import CachingHelpers
from src.internal_module.admin import GetInternalHost
from src.models.connect_config_model import (
    AppConnectType,
    ConnectConfigField,
    ConnectConfigModel,
    TypeConnectConfig,
)
from src.models.data_history_model import DataHistoryModel
from src.models.file_upload_model import <PERSON><PERSON>p<PERSON><PERSON>ield, FileUploadModel
from src.models.sdk_script_model import SDKScriptModel
from src.models.third_party_config_model import (
    ThirdPartyConfigField,
    ThirdPartyConfigModel,
)


class ACTION:
    CREATE = "CREATE"
    UPDATE = "UPDATE"
    DELETE = "DELETE"
    DISABLE = "DISABLE"
    ENABLE = "ENABLE"


class SourceMarketPlace:
    MOBIO = "mobio"


class WebpushController(BaseController):

    @classmethod
    def _set_key_cache_tracking_code_data_webpush(cls, merchant_id, code, data_set):
        return CachingHelpers().set_value_by_key_not_hash(
            "#".join([merchant_id, code]),
            value=data_set,
            expiration=ConstantTimeCaching.CACHING_CONFIG_TRACKING_CODE,
        )

    @classmethod
    def _set_cache_config_by_tracking_code_data_webpush(cls, tracking_code, data_set):
        key_cache = cls._gen_key_cache_config_webpush_by_tracking_code(tracking_code)
        return CachingHelpers().set_value_by_key_not_hash(
            key_cache,
            value=data_set,
            expiration=ConstantTimeCaching.CACHING_CONFIG_TRACKING_CODE,
        )

    @classmethod
    def _delete_cache_config_tracking_code_data_webpush(cls, tracking_code):
        key_cache = cls._gen_key_cache_config_webpush_by_tracking_code(tracking_code)
        return CachingHelpers().delete_cache_by_key(key_cache)

    def change_status_config(self):
        merchant_id = validate_merchant_header()
        account_id = get_param_value_temp("id")
        body_data = request.get_json()
        config_id = body_data.get("config_id")
        status = body_data.get("status")
        current_config = ThirdPartyConfigModel().find_by_id(
            merchant_id=merchant_id, config_id=config_id
        )
        if not current_config:
            raise CustomError(self.lang.get(LANG.CONFIG_NOT_FOUND).get("message"))
        current_status = current_config.get(ThirdPartyConfigField.STATUS)
        self.validate_change_status_webpush_config(
            {ThirdPartyConfigField.STATUS: status}
        )
        if current_status == status:
            return
        tracking_code = current_config.get(ThirdPartyConfigField.CODE)
        query = {"_id": ObjectId(config_id), "merchant_id": merchant_id}
        data_update = {
            ThirdPartyConfigField.STATUS: status,
            ThirdPartyConfigField.UPDATED_BY: account_id,
            ThirdPartyConfigField.UPDATED_TIME: datetime.utcnow(),
        }
        if status == ConstantThirdPartyConfigStatus.DISABLE:
            action = ACTION.DISABLE
            # status_tracking = 0
        elif status == ConstantThirdPartyConfigStatus.ENABLE:
            action = ACTION.ENABLE
            # status_tracking = 1
        else:
            action = ACTION.DELETE
            # status_tracking = 0
        # tracking_response = DigientyHelper.update_tracking_status(merchant_id, status_tracking, token, code)
        # if not tracking_response:
        #     raise CustomError(self.lang.get(LANG.UPDATE_TRACKING_STATUS_ERROR).get("message"))

        # set caching
        self._set_key_cache_tracking_code_data_webpush(
            merchant_id, tracking_code, {"merchant_id": merchant_id, "status": status}
        )
        # delete cache
        self._delete_cache_config_tracking_code_data_webpush(tracking_code)

        ThirdPartyConfigModel().update_by_set(query, data_update)
        HandleQueue.push_message_webpush_config_change_status(
            status, tracking_code, merchant_id
        )
        HandleQueue.push_message_webpush_config(
            current_config.get(ThirdPartyConfigField.CONNECT_INFORMATION_CONFIG_ID),
            config_id,
            current_config,
            data_update,
            action,
            merchant_id,
            account_id,
        )
        return

    def validate_change_status_webpush_config(self, data):
        rule_input = {
            "status": [InstanceOf(str), In(ConstantThirdPartyConfigStatus.ACCEPT_VALUE)]
        }
        self.abort_if_validate_error(rule_input, data)

    def create_config(self):
        merchant_id = validate_merchant_header()
        account_id = get_param_value_temp("id")
        body_data = request.get_json()
        type = body_data.get("type", ConstantTypeThirdParty.WEBPUSH)
        if type == ConstantTypeThirdParty.WEBPUSH:
            return self._create_config_webpush(merchant_id, account_id, body_data)

        raise CustomError(self.lang.get(LANG.INVALID_TYPE).get("message"))

    def _create_config_webpush(self, merchant_id, account_id, body_data):

        website_name = body_data.get("website_name")
        website_url = body_data.get("website_url")
        website_img_id = body_data.get("website_img_id")
        gtm_id = body_data.get("gtm_id")
        connect_config_information = body_data.get(
            ThirdPartyConfigField.CONNECT_INFORMATION_CONFIG, {}
        )
        validate_data_webpush_config = {
            "website_name": website_name,
            "website_url": website_url,
            "gtm_id": gtm_id,
            "connect_information_config": connect_config_information,
        }
        self.validate_webpush_config(validate_data_webpush_config)
        self.check_duplicate_webpush_website_url(website_url, merchant_id)
        now = datetime.utcnow()
        if (
            connect_config_information.get(
                ConnectConfigField.TYPE, TypeConnectConfig.DEFAULT
            )
            == TypeConnectConfig.DEFAULT
        ):
            connect_config_information = ConnectConfigModel().get_default_config_id(
                merchant_id
            )
            id_connect_config_information = connect_config_information.get(
                ConnectConfigField.ID
            )
            data_insert_connect_config_information = {
                ConnectConfigField.TYPE: TypeConnectConfig.DEFAULT
            }
        else:
            validate_firebase_config = {
                ConnectConfigField.SERVER_KEY: connect_config_information.get(
                    ConnectConfigField.SERVER_KEY
                ),
                ConnectConfigField.API_KEY: connect_config_information.get(
                    ConnectConfigField.API_KEY
                ),
                ConnectConfigField.AUTH_DOMAIN: connect_config_information.get(
                    ConnectConfigField.AUTH_DOMAIN
                ),
                ConnectConfigField.DATABASE_URL: connect_config_information.get(
                    ConnectConfigField.DATABASE_URL
                ),
                ConnectConfigField.PROJECT_ID: connect_config_information.get(
                    ConnectConfigField.PROJECT_ID
                ),
                ConnectConfigField.MESSAGINGSENDER_ID: connect_config_information.get(
                    ConnectConfigField.MESSAGINGSENDER_ID
                ),
                ConnectConfigField.APP_ID: connect_config_information.get(
                    ConnectConfigField.APP_ID
                ),
                ConnectConfigField.STORAGE_BUCKET: connect_config_information.get(
                    ConnectConfigField.STORAGE_BUCKET
                ),
            }
            self.validate_firebase_config(validate_firebase_config)
            id_connect_config_information = ObjectId()
            data_insert_connect_config_information = {
                ConnectConfigField.ID: id_connect_config_information,
                ConnectConfigField.SERVER_KEY: connect_config_information.get(
                    ConnectConfigField.SERVER_KEY
                ),
                ConnectConfigField.API_KEY: connect_config_information.get(
                    ConnectConfigField.API_KEY
                ),
                ConnectConfigField.AUTH_DOMAIN: connect_config_information.get(
                    ConnectConfigField.AUTH_DOMAIN
                ),
                ConnectConfigField.DATABASE_URL: connect_config_information.get(
                    ConnectConfigField.DATABASE_URL
                ),
                ConnectConfigField.PROJECT_ID: connect_config_information.get(
                    ConnectConfigField.PROJECT_ID
                ),
                ConnectConfigField.STORAGE_BUCKET: connect_config_information.get(
                    ConnectConfigField.STORAGE_BUCKET
                ),
                ConnectConfigField.MESSAGINGSENDER_ID: connect_config_information.get(
                    ConnectConfigField.MESSAGINGSENDER_ID
                ),
                ConnectConfigField.APP_ID: connect_config_information.get(
                    ConnectConfigField.APP_ID
                ),
                ConnectConfigField.MEASUREMENT_ID: connect_config_information.get(
                    ConnectConfigField.MEASUREMENT_ID
                ),
                ConnectConfigField.APP_CONNECT_TYPE: AppConnectType.FIREBASE,
                ConnectConfigField.TYPE: TypeConnectConfig.CUSTOM,
                ConnectConfigField.MERCHANT_ID: merchant_id,
                ConnectConfigField.CREATED_BY: account_id,
                ConnectConfigField.UPDATED_BY: account_id,
                ConnectConfigField.CREATED_TIME: now,
                ConnectConfigField.UPDATED_TIME: now,
            }
            ConnectConfigModel().insert(data_insert_connect_config_information)
        id_webpush_config = ObjectId()
        tracking_code = ThirdPartyConfigModel().generate_code_webpush()
        third_party_status = ConstantThirdPartyConfigStatus.ENABLE
        data_insert_webpush_config = {
            ThirdPartyConfigField.ID: id_webpush_config,
            ThirdPartyConfigField.WEBSITE_NAME: website_name,
            ThirdPartyConfigField.KEYWORD: utf8_to_ascii(website_name).lower(),
            ThirdPartyConfigField.WEBSITE_URL: website_url,
            ThirdPartyConfigField.WEBSITE_IMG_ID: website_img_id,
            ThirdPartyConfigField.GTM_ID: gtm_id,
            ThirdPartyConfigField.CONNECT_INFORMATION_CONFIG_ID: str(
                id_connect_config_information
            ),
            ThirdPartyConfigField.STATUS: third_party_status,
            ThirdPartyConfigField.MERCHANT_ID: merchant_id,
            ThirdPartyConfigField.CREATED_BY: account_id,
            ThirdPartyConfigField.UPDATED_BY: account_id,
            ThirdPartyConfigField.CREATED_TIME: now,
            ThirdPartyConfigField.UPDATED_TIME: now,
            ThirdPartyConfigField.TYPE: ConstantTypeThirdParty.WEBPUSH,
            ThirdPartyConfigField.CODE: tracking_code,
            ThirdPartyConfigField.SOURCE: SourceMarketPlace.MOBIO,
        }
        ThirdPartyConfigModel().insert(data_insert_webpush_config)
        self._set_key_cache_tracking_code_data_webpush(
            merchant_id,
            tracking_code,
            {"merchant_id": merchant_id, "status": third_party_status},
        )
        HandleQueue.push_message_webpush_config(
            id_connect_config_information,
            id_webpush_config,
            {},
            data_insert_webpush_config,
            ACTION.CREATE,
            merchant_id,
            account_id,
        )
        data_insert_webpush_config[ThirdPartyConfigField.CONNECT_INFORMATION_CONFIG] = (
            data_insert_connect_config_information
        )
        try:
            img_id = ObjectId(
                data_insert_webpush_config.get(ThirdPartyConfigField.WEBSITE_IMG_ID)
            )
            data_insert_webpush_config[ThirdPartyConfigField.WEBSITE_IMG_INFO] = (
                FileUploadModel().find_one({"_id": img_id})
            )
        except:
            data_insert_webpush_config[ThirdPartyConfigField.WEBSITE_IMG_INFO] = None
        result = JSONEncoder("%Y-%m-%dT%H:%MZ").json_loads(data_insert_webpush_config)

        return {"data": result}

    def check_duplicate_webpush_website_url(self, website_url, merchant_id):
        exist_url = ThirdPartyConfigModel().find_one(
            {"website_url": website_url, "merchant_id": merchant_id}
        )
        if exist_url:
            raise CustomError(self.lang.get(LANG.WEBSITE_URL_EXIST).get("message"))

    def validate_webpush_config(self, input_data):
        rule_input = {
            "website_name": [Required, InstanceOf(str)],
            "website_url": [Required, InstanceOf(str)],
            "gtm_id": [],
            "connect_information_config": [Required, InstanceOf(dict)],
        }
        self.abort_if_validate_error(rule_input, input_data)

    def validate_firebase_config(self, input_data):
        rule_input = {
            "server_key": [Required, InstanceOf(str)],
            "api_key": [Required, InstanceOf(str)],
            "auth_domain": [Required, InstanceOf(str)],
            "database_url": [Required, InstanceOf(str)],
            "project_id": [Required, InstanceOf(str)],
            "storage_bucket": [Required, InstanceOf(str)],
            "messagingsender_id": [Required, InstanceOf(str)],
            "app_id": [Required, InstanceOf(str)],
        }
        self.abort_if_validate_error(rule_input, input_data)

    def get_list_config(self):
        merchant_id = validate_merchant_header()
        body = request.get_json()
        type = body.get("type", ConstantTypeThirdParty.WEBPUSH)
        after_token = request.args.get("after_token")
        per_page = int(request.args.get("per_page", 10))
        search = body.get("search", "")
        search_by_name = body.get("search_by_name", "")
        if type == ConstantTypeThirdParty.WEBPUSH:
            list_config, after_token = ThirdPartyConfigModel().get_by_merchant(
                merchant_id,
                search,
                after_token,
                per_page,
                search_by_name=search_by_name,
            )
            list_website_img_id = [
                ObjectId(config.get(ThirdPartyConfigField.WEBSITE_IMG_ID))
                for config in list_config
                if config.get(ThirdPartyConfigField.WEBSITE_IMG_ID)
            ]
            website_img_info = FileUploadModel().find(
                {"_id": {"$in": list_website_img_id}}
            )
            website_img_info_mapping = {
                str(i.get(FileUploadField.ID)): i for i in website_img_info
            }
            for config in list_config:
                config[ThirdPartyConfigField.WEBSITE_IMG_INFO] = (
                    website_img_info_mapping.get(
                        str(config.get(ThirdPartyConfigField.WEBSITE_IMG_ID))
                    )
                )
            return {
                "data": JSONEncoder("%Y-%m-%dT%H:%MZ").json_loads(list_config),
                "paging": {"cursors": {"after_token": after_token, "before_token": ""}},
            }
        raise CustomError(self.lang.get(LANG.INVALID_TYPE).get("message"))

    def get_history_action(self, config_id):
        merchant_id = validate_merchant_header()
        after_token = request.args.get("after_token")
        per_page = int(request.args.get("per_page", 10))
        history_data, after_token = DataHistoryModel().get_data_by_config_id(
            config_id, merchant_id, per_page, after_token
        )
        return {
            "data": JSONEncoder("%Y-%m-%dT%H:%MZ").json_loads(history_data),
            "paging": {"cursors": {"after_token": after_token, "before_token": ""}},
        }

    def delete_config(self):
        merchant_id = validate_merchant_header()
        account_id = get_param_value_temp("id")
        config_ids = request.args.get("config_ids")
        list_config_ids = config_ids.split(",")
        list_config_ids = [ObjectId(config_id) for config_id in list_config_ids]
        query = {
            "_id": {"$in": list_config_ids},
        }
        data_delete = ThirdPartyConfigModel().find(query)
        if not data_delete:
            raise CustomError(self.lang.get(LANG.CONFIG_NOT_FOUND).get("message"))
        update_data = {
            "$set": {
                ThirdPartyConfigField.STATUS: ConstantThirdPartyConfigStatus.DELETED
            }
        }
        deleted_count = ThirdPartyConfigModel().update_many(query, update_data)
        for data in data_delete:
            tracking_code = data.get(ThirdPartyConfigField.CODE)
            self._delete_cache_config_tracking_code_data_webpush(tracking_code)
            HandleQueue.push_message_webpush_config(
                data.get(ThirdPartyConfigField.CONNECT_INFORMATION_CONFIG_ID),
                data.get(ThirdPartyConfigField.ID),
                data,
                {},
                ACTION.DELETE,
                merchant_id,
                account_id,
            )
        return

    def detail_config(self, config_id):
        merchant_id = validate_merchant_header()
        data = ThirdPartyConfigModel().find_by_id(merchant_id, config_id)
        if not data:
            raise CustomError(self.lang.get(LANG.CONFIG_NOT_FOUND).get("message"))
        img_id = data.get(ThirdPartyConfigField.WEBSITE_IMG_ID)
        if img_id:
            data[ThirdPartyConfigField.WEBSITE_IMG_INFO] = FileUploadModel().find_one(
                {"_id": ObjectId(img_id)}
            )
        connect_config_info = ConnectConfigModel().find_one(
            {
                "_id": ObjectId(
                    data.get(ThirdPartyConfigField.CONNECT_INFORMATION_CONFIG_ID)
                )
            }
        )
        if connect_config_info.get("type") != TypeConnectConfig.DEFAULT:
            data[ThirdPartyConfigField.CONNECT_INFORMATION_CONFIG] = connect_config_info
        else:
            data[ThirdPartyConfigField.CONNECT_INFORMATION_CONFIG] = {
                ThirdPartyConfigField.TYPE: TypeConnectConfig.DEFAULT
            }
        return {"data": JSONEncoder("%Y-%m-%dT%H:%MZ").json_loads(data)}

    def update_config(self, config_id):
        merchant_id = validate_merchant_header()
        account_id = get_param_value_temp("id")
        new_data = request.get_json()
        type = new_data.get("type", ConstantTypeThirdParty.WEBPUSH)
        now = datetime.utcnow()
        if type == ConstantTypeThirdParty.WEBPUSH:
            website_name = new_data.get("website_name")
            website_url = new_data.get("website_url")
            gtm_id = new_data.get("gtm_id")
            new_connect_information_config = new_data.get("connect_information_config")
            validate_data_webpush_config = {
                "website_name": website_name,
                "website_url": website_url,
                "gtm_id": gtm_id,
                "connect_information_config": new_connect_information_config,
            }
            self.validate_webpush_config(validate_data_webpush_config)
            old_data = ThirdPartyConfigModel().get_data_to_compare(config_id)
            if (
                new_connect_information_config.get(
                    ConnectConfigField.TYPE, TypeConnectConfig.DEFAULT
                )
                == TypeConnectConfig.DEFAULT
            ):
                new_connect_information_config = (
                    ConnectConfigModel().get_default_config(merchant_id)
                )
                new_data.pop("connect_information_config")
            else:
                validate_firebase_config = {
                    ConnectConfigField.SERVER_KEY: new_connect_information_config.get(
                        ConnectConfigField.SERVER_KEY
                    ),
                    ConnectConfigField.API_KEY: new_connect_information_config.get(
                        ConnectConfigField.API_KEY
                    ),
                    ConnectConfigField.AUTH_DOMAIN: new_connect_information_config.get(
                        ConnectConfigField.AUTH_DOMAIN
                    ),
                    ConnectConfigField.DATABASE_URL: new_connect_information_config.get(
                        ConnectConfigField.DATABASE_URL
                    ),
                    ConnectConfigField.PROJECT_ID: new_connect_information_config.get(
                        ConnectConfigField.PROJECT_ID
                    ),
                    ConnectConfigField.MESSAGINGSENDER_ID: new_connect_information_config.get(
                        ConnectConfigField.MESSAGINGSENDER_ID
                    ),
                    ConnectConfigField.APP_ID: new_connect_information_config.get(
                        ConnectConfigField.APP_ID
                    ),
                    ConnectConfigField.STORAGE_BUCKET: new_connect_information_config.get(
                        ConnectConfigField.STORAGE_BUCKET
                    ),
                }
                self.validate_firebase_config(validate_firebase_config)
                new_connect_information_config.update(
                    {ConnectConfigField.TYPE: TypeConnectConfig.CUSTOM}
                )
            old_connect_information_config = ConnectConfigModel().get_current_config(
                old_data.get(ThirdPartyConfigField.CONNECT_INFORMATION_CONFIG_ID)
            )
            compare_connect_information_config = json_compare(
                old_connect_information_config, new_connect_information_config
            )
            new_connect_config_id, connect_config_info = self.get_new_connect_config_id(
                compare_connect_information_config,
                new_connect_information_config,
                merchant_id,
                account_id,
                now,
            )
            old_data.pop(ThirdPartyConfigField.CONNECT_INFORMATION_CONFIG_ID)
            compare_config = json_compare(old_data, new_data)
            data_update = compare_config.get("after", {})
            if new_connect_config_id:
                data_update.update(
                    {
                        ThirdPartyConfigField.CONNECT_INFORMATION_CONFIG_ID: new_connect_config_id,
                        ThirdPartyConfigField.CONNECT_INFORMATION_CONFIG: connect_config_info,
                    }
                )
            result = {
                "_id": config_id,
            }
            if data_update:
                result.update(data_update)
                if ThirdPartyConfigField.WEBSITE_IMG_ID in data_update.keys():
                    img_id = data_update.get(ThirdPartyConfigField.WEBSITE_IMG_ID)
                    result.update({ThirdPartyConfigField.WEBSITE_IMG_ID: None})
                    if img_id:
                        img_info = FileUploadModel().find_one(
                            {
                                "_id": ObjectId(
                                    data_update.get(
                                        ThirdPartyConfigField.WEBSITE_IMG_ID
                                    )
                                )
                            }
                        )
                        result.update(
                            {ThirdPartyConfigField.WEBSITE_IMG_INFO: img_info}
                        )

                if ThirdPartyConfigField.WEBSITE_URL in data_update.keys():
                    raise CustomError(
                        self.lang.get(LANG.NOT_PERMISSION_UPDATE_WEBSITE_URL_ERROR).get(
                            "message"
                        )
                    )
                data_update.update(
                    {
                        ThirdPartyConfigField.KEYWORD: utf8_to_ascii(
                            website_name
                        ).lower(),
                        ThirdPartyConfigField.UPDATED_TIME: now,
                    }
                )
                ThirdPartyConfigModel().update_by_set(
                    {"_id": ObjectId(config_id)}, data_update
                )
                HandleQueue.push_message_webpush_config(
                    new_connect_config_id,
                    config_id,
                    old_data,
                    data_update,
                    ACTION.UPDATE,
                    merchant_id,
                    account_id,
                )
                return {"data": JSONEncoder("%Y-%m-%dT%H:%MZ").json_loads(result)}
            result.update(data_update)
            detail_third_party_config = ThirdPartyConfigModel().find_by_id(
                merchant_id, config_id
            )
            tracking_code = detail_third_party_config.get(ThirdPartyConfigField.CODE)
            third_party_status = detail_third_party_config.get(
                ThirdPartyConfigField.STATUS
            )
            self._set_key_cache_tracking_code_data_webpush(
                merchant_id,
                tracking_code,
                {"merchant_id": merchant_id, "status": third_party_status},
            )
            self._delete_cache_config_tracking_code_data_webpush(tracking_code)
            return {"data": JSONEncoder("%Y-%m-%dT%H:%MZ").json_loads(result)}
        raise CustomError(self.lang.get(LANG.INVALID_TYPE).get("message"))

    @staticmethod
    def get_new_connect_config_id(
        data_compare, new_connect_information_config, merchant_id, account_id, now
    ):
        after_data = data_compare.get("after", {})
        if after_data:
            if after_data.get(ConnectConfigField.TYPE) == TypeConnectConfig.CUSTOM:
                data_insert = {
                    **new_connect_information_config,
                    ConnectConfigField.APP_CONNECT_TYPE: AppConnectType.FIREBASE,
                    ConnectConfigField.TYPE: TypeConnectConfig.CUSTOM,
                    ConnectConfigField.MERCHANT_ID: merchant_id,
                    ConnectConfigField.CREATED_BY: account_id,
                    ConnectConfigField.UPDATED_BY: account_id,
                    ConnectConfigField.CREATED_TIME: now,
                    ConnectConfigField.UPDATED_TIME: now,
                }
                new_connect_config_id = (
                    ConnectConfigModel().insert_document(data_insert).inserted_id
                )
                return str(new_connect_config_id), {
                    **new_connect_information_config,
                    ConnectConfigField.TYPE: TypeConnectConfig.CUSTOM,
                }
            elif after_data.get(ConnectConfigField.TYPE) == TypeConnectConfig.DEFAULT:
                connect_config_information = ConnectConfigModel().get_default_config_id(
                    merchant_id
                )
                new_connect_config_id = connect_config_information.get(
                    ConnectConfigField.ID
                )
                return str(new_connect_config_id), {
                    ConnectConfigField.TYPE: TypeConnectConfig.DEFAULT
                }
            else:
                return None, None
        return None, None

    def sdk_script_upsert(self):
        merchant_id = validate_merchant_header()
        data_body = request.get_json()
        now = datetime.utcnow()
        script = data_body.get("script")
        old_script = SDKScriptModel().get_by_merchant(merchant_id)
        if not old_script:
            SDKScriptModel().insert_document(
                {
                    "merchant_id": "DEFAULT",
                    "script": script,
                    "created_time": now,
                    "updated_time": now,
                }
            )
            return
        SDKScriptModel().update_by_set(
            {"merchant_id": "DEFAULT"}, {"script": script, "updated_time": now}
        )
        return

    def sdk_script_get(self):
        merchant_id = validate_merchant_header()
        website_url = request.args.get("website_url")
        if not website_url:
            raise CustomError(self.lang.get(LANG.WEBSITE_URL_NOT_FOUND).get("message"))
        sdk_script_info = SDKScriptModel().get_by_merchant("DEFAULT")
        if not sdk_script_info:
            raise CustomError(self.lang.get(LANG.SCRIPT_NOT_FOUND).get("message"))
        sdk_script = sdk_script_info.get("script")
        config_data = ThirdPartyConfigModel().find_by_website_url(
            merchant_id, website_url
        )
        code = config_data.get(ThirdPartyConfigField.CODE)
        merchant_id = config_data.get(ThirdPartyConfigField.MERCHANT_ID)

        public_host = GetInternalHost.get_internal_host_by_merchant(
            merchant_id, "public-host"
        )
        sdk_script = (
            sdk_script.replace("#code", code)
            .replace("#merchant_id", merchant_id)
            .replace("#DOMAIN_RESOURCES_PUBLIC#", public_host)
            if sdk_script
            else ""
        )
        return {"data": JSONEncoder("%Y-%m-%dT%H:%MZ").json_loads(sdk_script)}

    def get_connect_config(self):
        merchant_id = validate_merchant_header()
        website_url = request.args.get("website_url")
        config_data = ThirdPartyConfigModel().find_by_website_url(
            merchant_id, website_url
        )
        if not config_data:
            raise CustomError(self.lang.get(LANG.CONFIG_NOT_FOUND).get("message"))
        connect_config_id = config_data.get(
            ThirdPartyConfigField.CONNECT_INFORMATION_CONFIG_ID
        )
        query = {ConnectConfigField.ID: ObjectId(connect_config_id)}
        field_select = {
            ConnectConfigField.ID: 0,
            ConnectConfigField.SERVER_KEY: 1,
            ConnectConfigField.API_KEY: 1,
            ConnectConfigField.DATABASE_URL: 1,
            ConnectConfigField.PROJECT_ID: 1,
            ConnectConfigField.STORAGE_BUCKET: 1,
            ConnectConfigField.MESSAGINGSENDER_ID: 1,
            ConnectConfigField.APP_ID: 1,
            ConnectConfigField.MEASUREMENT_ID: 1,
            ConnectConfigField.APP_CONNECT_TYPE: 1,
        }
        connect_information_config = ConnectConfigModel().find_one(query, field_select)
        return {
            "data": JSONEncoder("%Y-%m-%dT%H:%MZ").json_loads(
                connect_information_config
            )
        }

    def list_config_by_ids(self):
        body = request.get_json()
        merchant_id = validate_merchant_header()
        list_code = body.get("list_code", [])
        after_token = request.args.get("after_token")
        per_page = int(request.args.get("per_page", 10))
        validate_data = {"list_code": list_code}
        self.validate_list_config_by_ids(validate_data)
        if list_code:
            list_config, after_token = ThirdPartyConfigModel().get_by_list_code(
                list_code, after_token, per_page, merchant_id
            )
            list_website_img_id = [
                ObjectId(config.get(ThirdPartyConfigField.WEBSITE_IMG_ID))
                for config in list_config
                if config.get(ThirdPartyConfigField.WEBSITE_IMG_ID)
            ]
            website_img_info = FileUploadModel().find(
                {"_id": {"$in": list_website_img_id}}
            )
            website_img_info_mapping = {
                str(i.get(FileUploadField.ID)): i for i in website_img_info
            }
            for config in list_config:
                config[ThirdPartyConfigField.WEBSITE_IMG_INFO] = (
                    website_img_info_mapping.get(
                        str(config.get(ThirdPartyConfigField.WEBSITE_IMG_ID))
                    )
                )
            return {
                "data": JSONEncoder("%Y-%m-%dT%H:%MZ").json_loads(list_config),
                "paging": {"cursors": {"after_token": after_token, "before_token": ""}},
            }
        return {"data": []}

    def validate_list_config_by_ids(self, data):
        rule_input = {"list_code": [InstanceOf(list)]}
        self.abort_if_validate_error(rule_input, data)

    def get_information_webpush_by_tracking_code(self, tracking_code):
        request_merchant_id = validate_merchant_header()
        data_cache = CachingHelpers().get_value_by_key_not_hash(tracking_code)
        if data_cache:
            return {"data": data_cache}
        detail_config_webpush = (
            ThirdPartyConfigModel().get_information_webpush_by_tracking_code(
                request_merchant_id, tracking_code
            )
        )
        status_config = ConstantThirdPartyConfigStatus.NOT_EXIST
        merchant_id = ""
        app_id = ""

        if detail_config_webpush:
            merchant_id = detail_config_webpush.get(ThirdPartyConfigField.MERCHANT_ID)
            status_config = detail_config_webpush.get(ThirdPartyConfigField.STATUS)
            app_id = detail_config_webpush.get(ThirdPartyConfigField.NM_APP_ID)
        data_return = {
            "merchant_id": merchant_id,
            "status": status_config,
            "app_id": app_id,
        }
        self._set_key_cache_tracking_code_data_webpush(
            request_merchant_id, tracking_code, data_return
        )
        return {"data": data_return}

    @classmethod
    def _gen_key_cache_config_webpush_by_tracking_code(cls, tracking_code):
        return "marketplace#config_webpush_by_tracking_code#{}".format(tracking_code)

    def config_webpush_by_tracking_code(self, tracking_code):
        request_merchant_id = validate_merchant_header()
        # data_cache = CachingHelpers().get_value_by_key_not_hash(
        #     self._gen_key_cache_config_webpush_by_tracking_code(tracking_code)
        # )
        # if data_cache:
        #     return {"data": data_cache}
        detail_config_webpush = (
            ThirdPartyConfigModel().get_information_webpush_by_tracking_code(
                request_merchant_id,
                tracking_code,
                field_select={
                    ThirdPartyConfigField.CONNECT_INFORMATION_CONFIG_ID: 1,
                    ThirdPartyConfigField.STATUS: 1,
                    ThirdPartyConfigField.WEBSITE_URL: 1,
                },
            )
        )
        if not detail_config_webpush:
            return
        connect_config_id = detail_config_webpush.get(
            ThirdPartyConfigField.CONNECT_INFORMATION_CONFIG_ID
        )
        if not connect_config_id:
            return
        connect_information_config = ConnectConfigModel().get_current_config_by_type(
            connect_config_id, AppConnectType.FIREBASE
        )
        if not connect_information_config:
            return

        data_return = {
            "connect_type": connect_information_config.get(
                ConnectConfigField.APP_CONNECT_TYPE
            ),
            "status": detail_config_webpush.get(ThirdPartyConfigField.STATUS),
            "website_url": detail_config_webpush.get(ThirdPartyConfigField.WEBSITE_URL),
        }
        information = {}
        if (
            detail_config_webpush.get(ThirdPartyConfigField.STATUS)
            == ConstantThirdPartyConfigStatus.ENABLE
        ):
            information = {
                "serverKey": connect_information_config.get(
                    ConnectConfigField.SERVER_KEY
                ),
                "apiKey": connect_information_config.get(ConnectConfigField.API_KEY),
                "databaseURL": connect_information_config.get(
                    ConnectConfigField.DATABASE_URL
                ),
                "projectId": connect_information_config.get(
                    ConnectConfigField.PROJECT_ID
                ),
                "storageBucket": connect_information_config.get(
                    ConnectConfigField.STORAGE_BUCKET
                ),
                "messagingSenderId": connect_information_config.get(
                    ConnectConfigField.MESSAGINGSENDER_ID
                ),
                "appId": connect_information_config.get(ConnectConfigField.APP_ID),
                "measurementId": connect_information_config.get(
                    ConnectConfigField.MEASUREMENT_ID
                ),
                "authDomain": connect_information_config.get(
                    ConnectConfigField.AUTH_DOMAIN
                ),
            }
        data_return.update({"information": information})

        self._set_cache_config_by_tracking_code_data_webpush(tracking_code, data_return)
        return {"data": data_return}

    def _validate_upsert_landing_page(self, data_validate):
        rule_validate = {
            "object_id": [Required, InstanceOf(str), Length(1)],
            "name": [Required, InstanceOf(str), Length(1)],
            "public_url": [Required, InstanceOf(str), Length(1)],
        }
        self.abort_if_validate_error(rule_validate, data_validate)

    def _check_duplicate_website_url_from_landing_page(
        self, merchant_id, landing_page_id, landing_page_url
    ):
        webpush_detail = ThirdPartyConfigModel().get_webpush_detail_by_landing_page_id(
            merchant_id, landing_page_id
        )
        find_duplicate_url = {
            "website_url": landing_page_url,
            "merchant_id": merchant_id,
        }
        if webpush_detail:
            find_duplicate_url.update({"_id": {"$ne": webpush_detail.get("_id")}})
        detail_by_url = ThirdPartyConfigModel().find_one(find_duplicate_url)
        if detail_by_url:
            raise CustomError(self.lang.get(LANG.WEBSITE_URL_EXIST).get("message"))
        return webpush_detail

    def upsert_landing_page(self):
        merchant_id = validate_merchant_header()

        body_data = request.get_json()
        self._validate_upsert_landing_page(body_data)
        landing_page_id = body_data.get("object_id")
        website_name = body_data.get("name")
        website_url = body_data.get("public_url")
        account_id = get_param_value_temp("id")
        gtm_id = None
        connect_config_information = body_data.get(
            ThirdPartyConfigField.CONNECT_INFORMATION_CONFIG, {}
        )
        detail_webpush = self._check_duplicate_website_url_from_landing_page(
            merchant_id, landing_page_id, website_url
        )
        now = datetime.utcnow()
        third_party_status = ConstantThirdPartyConfigStatus.ENABLE
        if not detail_webpush:
            connect_config_information = ConnectConfigModel().get_default_config_id(
                merchant_id
            )
            id_connect_config_information = connect_config_information.get(
                ConnectConfigField.ID
            )
            id_webpush_config = ObjectId()
            tracking_code = ThirdPartyConfigModel().generate_code_webpush()

            data_insert_webpush_config = {
                ThirdPartyConfigField.ID: id_webpush_config,
                ThirdPartyConfigField.WEBSITE_NAME: website_name,
                ThirdPartyConfigField.KEYWORD: utf8_to_ascii(website_name).lower(),
                ThirdPartyConfigField.WEBSITE_URL: website_url,
                ThirdPartyConfigField.WEBSITE_IMG_ID: None,
                ThirdPartyConfigField.GTM_ID: gtm_id,
                ThirdPartyConfigField.CONNECT_INFORMATION_CONFIG_ID: str(
                    id_connect_config_information
                ),
                ThirdPartyConfigField.STATUS: third_party_status,
                ThirdPartyConfigField.MERCHANT_ID: merchant_id,
                ThirdPartyConfigField.CREATED_BY: account_id,
                ThirdPartyConfigField.UPDATED_BY: account_id,
                ThirdPartyConfigField.CREATED_TIME: now,
                ThirdPartyConfigField.UPDATED_TIME: now,
                ThirdPartyConfigField.TYPE: ConstantTypeThirdParty.WEBPUSH,
                ThirdPartyConfigField.CODE: tracking_code,
                ThirdPartyConfigField.SOURCE: SourceMarketPlace.MOBIO,
                "information_create": {
                    "source": "landing_page",
                    "object_id": landing_page_id,
                },
            }
            ThirdPartyConfigModel().insert(data_insert_webpush_config)
            HandleQueue.push_message_webpush_config(
                id_connect_config_information,
                id_webpush_config,
                {},
                data_insert_webpush_config,
                ACTION.CREATE,
                merchant_id,
                account_id,
            )

        else:
            id_webpush_config = detail_webpush.get(ThirdPartyConfigField.ID)
            tracking_code = detail_webpush.get(ThirdPartyConfigField.CODE)
            data_update = {
                ThirdPartyConfigField.WEBSITE_URL: website_url,
                ThirdPartyConfigField.WEBSITE_NAME: website_name,
                ThirdPartyConfigField.UPDATED_TIME: now,
                ThirdPartyConfigField.UPDATED_BY: account_id,
            }
            MobioLogging().info("Data update :: {}".format(data_update))
            status_update = ThirdPartyConfigModel().update_by_set(
                {"_id": id_webpush_config}, data_update
            )
            MobioLogging().info(
                "Status update config landing page webpush :: {}".format(status_update)
            )

        self._set_key_cache_tracking_code_data_webpush(
            merchant_id,
            tracking_code,
            {"merchant_id": merchant_id, "status": third_party_status},
        )
        sdk_script_info = SDKScriptModel().get_by_merchant("DEFAULT")
        sdk_script = sdk_script_info.get("script")
        public_host = GetInternalHost.get_internal_host_by_merchant(
            merchant_id, "public-host"
        )
        sdk_script = (
            sdk_script.replace("#code", tracking_code)
            .replace("#merchant_id", merchant_id)
            .replace("#DOMAIN_RESOURCES_PUBLIC#", public_host)
            if sdk_script
            else ""
        )

        result = {
            "id": str(id_webpush_config),
            "website_name": website_name,
            "website_url": website_url,
            "status": "ENABLE",
            "tracking_code": tracking_code,
            "script_sdk_integrated": sdk_script,
        }
        return {"data": result}
