#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 12/09/2024
"""

import json
import string
from datetime import datetime

import flask
from bson import ObjectId
from flask import request
from mobio.libs.logging import MobioLogging
from mobio.libs.validator import Each, In, InstanceOf, Length, Required
from mobio.sdks.base.common.mobio_exception import CustomError
from mobio.sdks.base.controllers import BaseController

from src.common import LANG
from src.common.data_flow_constant import ConstantColumnsShowMappingGoogleSheet
from src.common.handle_headers import get_param_value_temp, validate_merchant_header
from src.common.integration_account_constant import (
    FolderDriveConstant,
    TypeIntegrationAccount,
)
from src.common.json_encoder import JSONEncoder
from src.common.utils import (
    Base64,
    add_url_params,
    convert_isoformat_to_date,
    utf8_to_ascii,
)
from src.helpers.integration_account.get_information_config_app import (
    GetInformationConfigApp,
)
from src.helpers.integration_account.google_hepler import IntegrationAccountGoogleHelper
from src.models.data_flow.config_connectors_model import ConfigConnectorsModel
from src.models.integration_account.integration_account_model import (
    IntegrationAccountModel,
)


class IntegrationAccountController(BaseController):

    @classmethod
    def _validate_list_integration_accounts(cls, args):
        params = {}
        name_search = args.get("name_search")
        if name_search:
            name_search = utf8_to_ascii(name_search).lower().replace(" ", "")
            params.update({"name_search": name_search})
        selected_fields = args.get("selected_filed")
        if selected_fields:
            selected_fields = [x.strip() for x in selected_fields.split(",") if x]
            params.update({"selected_field": selected_fields})
        order_by = args.get("order_by")
        if order_by:
            params.update({"order_by": order_by})
        order_type = args.get("order_type")
        if order_type:
            params.update({"order_type": order_type})

        rule_input = {
            "name_search": [InstanceOf(str), Length(1)],
            "selected_field": [
                InstanceOf(list),
                Each([InstanceOf(str), Length(1)]),
            ],
            "order_by": [InstanceOf(str)],
            "order_type": [InstanceOf(str), In(["asc", "desc"])],
        }
        cls.abort_if_validate_error(rule_input, params)
        return name_search, selected_fields

    def list_integration_accounts(self):
        merchant_id = validate_merchant_header()
        args = request.args
        name_search, selected_fields = self._validate_list_integration_accounts(args)

        per_page = args.get("per_page", default=20, type=int)
        token_in_query = args.get("after_token")
        merchant_id = validate_merchant_header()
        order_by = args.get("order_by", default="updated_time", type=str)
        order_type = args.get("order_type", default="desc", type=str)
        MobioLogging().info(
            "list_integration_accounts :: merchant_id: {} :: query_params: {}".format(merchant_id, args.to_dict())
        )

        projection = {
            "_id": 1,
            "type": 1,
            "name": 1,
            "created_time": 1,
            "updated_time": 1,
            "created_type": 1,
            "created_by": 1,
            "updated_by": 1,
            "config.email": 1,
            "config.name": 1,
            "config.user_id": 1,
        }
        if selected_fields:
            projection = {field: 1 for field in selected_fields.split(",")}

        order_type = 1 if order_type == "asc" else -1
        integration_accounts, after_token = IntegrationAccountModel().get_list_paging_by_condition(
            merchant_id=merchant_id,
            name_search=name_search,
            sort_option=[(order_by, order_type)],
            before_token=token_in_query,
            per_page=per_page,
            projection=projection,
        )
        results = []
        for integration_account in integration_accounts:
            results.append(JSONEncoder().json_loads(integration_account))
        return {"data": results, "paging": {"cursors": {"after_token": after_token, "before_token": token_in_query}}}

    def _validate_get_information_app_config_connect(self, data_validate):
        rule_validate = {
            "type": [Required, InstanceOf(str), Length(1)],
            "page_current_url": [Required, InstanceOf(str), Length(1)],
        }

        self.abort_if_validate_error(rule_validate, data_validate)

    def get_information_app_config_connect(self):
        merchant_id = validate_merchant_header()
        account_id = get_param_value_temp("id")
        request_args = request.args

        config_type = request_args.get("type")
        page_current_url = request_args.get("page_current_url")

        self._validate_get_information_app_config_connect({"type": config_type, "page_current_url": page_current_url})

        # TODO: Implement logic to get information app config connect
        json_data = {
            "merchant_id": merchant_id,
            "page_current_url": page_current_url,
            "account_id": account_id,
            "config_type": config_type,
        }

        MobioLogging().info("get_information_app_config_connect :: json_data: {}".format(json_data))
        state_str = json.dumps(json_data, ensure_ascii=False)
        state_encrypt = Base64.encode(state_str).rstrip("=")
        try:
            cl = GetInformationConfigApp()
            authorization_url = getattr(cl, config_type)(merchant_id, state_encrypt=state_encrypt)
        except Exception as ex:
            MobioLogging().error("get_information_app_config_connect :: error: {}".format(str(ex)))
            raise CustomError(str(ex))
        return {"data": {"link": authorization_url}}

    def delete_integration_account(self, integration_account_id):
        merchant_id = validate_merchant_header()
        integration_account = IntegrationAccountModel().get_by_id(merchant_id, integration_account_id)
        if not integration_account:
            raise CustomError("Integration account not found")

        config_connector = ConfigConnectorsModel().get_connector_by_integration_account_id(
            merchant_id, integration_account_id
        )
        if config_connector:
            raise CustomError(self.lang.get(LANG.DELETE_INTEGRATION_ACCOUNT_FAIL).get("message"))

        status_delete = IntegrationAccountModel().delete_one({"_id": ObjectId(integration_account_id)})
        if status_delete.deleted_count > 0:
            MobioLogging().info("delete_integration_account :: success :: id: {}".format(integration_account_id))
            return

        raise CustomError("Failed to delete Google integration account")

    def detail_integration_account(self, integration_account_id):
        merchant_id = validate_merchant_header()
        projection = {
            "_id": 1,
            "type": 1,
            "name": 1,
            "created_time": 1,
            "updated_time": 1,
            "created_type": 1,
            "created_by": 1,
            "updated_by": 1,
            "config.email": 1,
            "config.name": 1,
            "config.user_id": 1,
        }
        integration_account = IntegrationAccountModel().get_by_id(merchant_id, integration_account_id, projection)
        if not integration_account:
            raise CustomError("Integration account not found")
        return {"data": JSONEncoder().json_loads(integration_account)}

    def google_authorize_callback(self):
        request_args = request.args
        MobioLogging().info("google_authorize_callback :: request_args :: {}".format(request_args))
        state = request_args.get("state")
        error_mess = request_args.get("error")
        state_decrypt = Base64.decode(state)
        json_state = json.loads(state_decrypt)
        merchant_id = json_state.get("merchant_id")
        page_current_url = json_state.get("page_current_url")
        config_type = json_state.get("config_type")
        request_url = flask.request.url

        if error_mess:
            param_error = {"error_mess": "user_not_access_scope", "config_type": config_type}
            {"redirect_url": add_url_params(page_current_url, param_error)}
        user_data = None
        try:
            user_data, param_redirect = IntegrationAccountGoogleHelper().get_user_info(
                merchant_id, request_url, config_type
            )
        except Exception as ex:
            MobioLogging().error("google_authorize_callback :: error: {}".format(str(ex)))
            param_error = {"error_mess": "user_info_not_found", "config_type": config_type}
            {"redirect_url": add_url_params(page_current_url, param_error)}
            return {"redirect_url": add_url_params(page_current_url, param_error)}
        if not user_data:
            return {"redirect_url": add_url_params(page_current_url, param_redirect)}

        integration_account_id = getattr(self, "_upsert_integration_account_{}".format(config_type))(
            json_state, user_data
        )
        param_redirect.update({"integration_account_id": integration_account_id})
        return {"redirect_url": add_url_params(page_current_url, param_redirect)}

    def _upsert_integration_account_google_sheet(self, json_state, user_data):
        merchant_id = json_state.get("merchant_id")
        account_id = json_state.get("account_id")
        dnow_utc = datetime.utcnow()

        search_options = {
            "merchant_id": merchant_id,
            "type": TypeIntegrationAccount.GOOGLE_SHEET,
            "third_party_id": user_data.get("user_id"),
        }

        data_upsert = {
            "merchant_id": merchant_id,
            "type": TypeIntegrationAccount.GOOGLE_SHEET,
            "third_party_id": user_data.get("user_id"),
            "config": user_data,
            "name": user_data.get("email"),
        }

        detail_config_integration = IntegrationAccountModel().find_one(search_options)
        if detail_config_integration:
            integration_account_id = detail_config_integration.get("_id")
            data_upsert.update({"updated_time": dnow_utc, "updated_by": account_id})

            MobioLogging().info("_upsert_integration_account_google_sheet :: data_upsert :: {}".format(data_upsert))
            status_update = IntegrationAccountModel().update_by_set({"_id": integration_account_id}, data_upsert)
            MobioLogging().info("_upsert_integration_account_google_sheet :: status_update :: {}".format(status_update))
        else:
            data_upsert.update(
                {"created_time": dnow_utc, "updated_time": dnow_utc, "updated_by": account_id, "created_by": account_id}
            )
            MobioLogging().info("_upsert_integration_account_google_sheet :: data_upsert :: {}".format(data_upsert))
            integration_account_id = IntegrationAccountModel().insert(data_upsert).inserted_id
            MobioLogging().info(
                "_upsert_integration_account_google_sheet :: integration_account_id :: {}".format(
                    integration_account_id
                )
            )
        return integration_account_id

    def get_folder_default_google_sheet(self):
        merchant_id = validate_merchant_header()
        integration_account_id = request.args.get("account_id")

        integration_account = IntegrationAccountModel().get_by_id(merchant_id, integration_account_id)
        if not integration_account:
            raise CustomError("Integration account not found")

        integration_account_config = integration_account.get("config", {})
        folder_id = integration_account_config.get("folder_drive_create_id")
        new_token = None
        if not folder_id:
            # Create a new folder if folder_id does not exist
            folder_name = FolderDriveConstant.GOOGLE_SHEET.NAME_FOLDER_DEFAULT
            folder_id, new_token = IntegrationAccountGoogleHelper().create_folder_drive_google_sheet(
                merchant_id, integration_account_config, folder_name
            )
            if not folder_id:
                raise CustomError("Failed to create default Google Sheet folder")
            integration_account_config["folder_drive_create_id"] = folder_id
        else:
            # Fetch existing folder details
            response, new_token = IntegrationAccountGoogleHelper().get_folder_drive_google_sheet_by_id(
                folder_id, integration_account_config
            )

            folder_name = response.get("name", "Unknown Folder Name")
        MobioLogging().info("get_folder_default_google_sheet :: new_token :: {}".format(new_token))
        if new_token:
            expiry = convert_isoformat_to_date(new_token.get("expiry"))
            new_token["expiry"] = expiry
            integration_account_config.update(new_token)
        # Log and update the integration account config
        MobioLogging().info("integration_account_config :: {}".format(integration_account_config))
        IntegrationAccountModel().update_by_set(
            {"_id": ObjectId(integration_account_id)}, {"config": integration_account_config}
        )

        return {"data": {"id": folder_id, "name": folder_name}}

    def get_google_sheet_spreadsheets(self):
        merchant_id = validate_merchant_header()
        integration_account_id = request.args.get("account_id")
        search = request.args.get("name_search")
        token_in_query = request.args.get("after_token")
        per_page = request.args.get("per_page", default=5, type=int)

        integration_account = IntegrationAccountModel().get_by_id(merchant_id, integration_account_id)
        if not integration_account:
            raise CustomError("Integration account not found")

        integration_account_config = integration_account.get("config", {})
        folder_id = integration_account_config.get("folder_drive_create_id")

        if not folder_id:
            raise CustomError("Folder ID not found in integration account config")
        spreadsheets, new_token = IntegrationAccountGoogleHelper().get_list_spreadsheet_in_folder(
            folder_id, integration_account_config, token_in_query, per_page, search
        )
        results = []
        after_token = None
        if spreadsheets:
            after_token = spreadsheets.get("nextPageToken", None)
            results = spreadsheets.get("files", [])
        if new_token:
            expiry = convert_isoformat_to_date(new_token.get("expiry"))
            new_token["expiry"] = expiry
            integration_account_config.update(new_token)
        # Log and update the integration account config
        MobioLogging().info("integration_account_config :: {}".format(integration_account_config))
        IntegrationAccountModel().update_by_set(
            {"_id": ObjectId(integration_account_id)}, {"config": integration_account_config}
        )

        return {"data": results, "paging": {"cursors": {"after_token": after_token, "before_token": token_in_query}}}

    def detail_google_sheet_spreadsheets(self, spreadsheet_id):
        merchant_id = validate_merchant_header()
        integration_account_id = request.args.get("account_id")

        integration_account = IntegrationAccountModel().get_by_id(merchant_id, integration_account_id)
        if not integration_account:
            raise CustomError("Integration account not found")

        integration_account_config = integration_account.get("config", {})
        folder_id = integration_account_config.get("folder_drive_create_id")

        if not folder_id:
            raise CustomError("Folder ID not found in integration account config")
        spreadsheet, new_token, status_trashed = IntegrationAccountGoogleHelper().detail_sheets_in_spreadsheet(
            spreadsheet_id, integration_account_config
        )
        if not spreadsheet:
            raise CustomError("SpreadsheetId not found in integration account config")
        result = {}
        if spreadsheet:
            result = {
                "id": spreadsheet.get("spreadsheetId"),
                "name": spreadsheet.get("properties", {}).get("title"),
                "status_trashed": status_trashed,
            }
        if new_token:
            expiry = convert_isoformat_to_date(new_token.get("expiry"))
            new_token["expiry"] = expiry
            integration_account_config.update(new_token)
            # Log and update the integration account config
            MobioLogging().info("integration_account_config :: {}".format(integration_account_config))
            IntegrationAccountModel().update_by_set(
                {"_id": ObjectId(integration_account_id)}, {"config": integration_account_config}
            )

        return {"data": result}

    def _validate_add_google_sheet_spreadsheets(self, data_validate):
        rules = {"name": [Required, InstanceOf(str), Length(1)]}
        self.abort_if_validate_error(rules, data_validate)

    def add_google_sheet_spreadsheets(self):
        merchant_id = validate_merchant_header()
        body = request.get_json()
        integration_account_id = request.args.get("account_id")
        self._validate_add_google_sheet_spreadsheets(body)

        spreadsheet_name = body.get("name")

        integration_account = IntegrationAccountModel().get_by_id(merchant_id, integration_account_id)
        if not integration_account:
            raise CustomError("Integration account not found")

        integration_account_config = integration_account.get("config", {})
        folder_id = integration_account_config.get("folder_drive_create_id")

        if not folder_id:
            raise CustomError("Folder ID not found in integration account config")
        spreadsheet_id, new_token = IntegrationAccountGoogleHelper().add_spreadsheet_in_folder(
            folder_id, integration_account_config, spreadsheet_name
        )
        result = {"id": spreadsheet_id, "name": spreadsheet_name}
        if new_token:
            expiry = convert_isoformat_to_date(new_token.get("expiry"))
            new_token["expiry"] = expiry
            integration_account_config.update(new_token)
        # Log and update the integration account config
        MobioLogging().info("integration_account_config :: {}".format(integration_account_config))
        IntegrationAccountModel().update_by_set(
            {"_id": ObjectId(integration_account_id)}, {"config": integration_account_config}
        )

        return {"data": result}

    def get_sheets_in_google_sheet_spreadsheets(self, spreadsheet_id):
        merchant_id = validate_merchant_header()
        # body = request.get_json()
        integration_account_id = request.args.get("account_id")
        name_search = request.args.get("name_search")
        # self._validate_add_google_sheet_spreadsheets(body)
        integration_account = IntegrationAccountModel().get_by_id(merchant_id, integration_account_id)
        if not integration_account:
            raise CustomError("Integration account not found")

        integration_account_config = integration_account.get("config", {})
        sheets, new_token, _ = IntegrationAccountGoogleHelper().get_sheets_in_spreadsheet(
            spreadsheet_id, integration_account_config
        )
        results = []
        if sheets:
            for sheet in sheets:
                properties = sheet.get("properties", {})
                title = properties.get("title")
                if name_search:
                    name_search = utf8_to_ascii(name_search.lower())
                    sheet_name = utf8_to_ascii(title.lower())
                    if name_search not in sheet_name:
                        continue
                results.append({"id": properties.get("sheetId"), "name": title})

        if new_token:
            expiry = convert_isoformat_to_date(new_token.get("expiry"))
            new_token["expiry"] = expiry
            integration_account_config.update(new_token)
        # Log and update the integration account config
        MobioLogging().info("integration_account_config :: {}".format(integration_account_config))
        IntegrationAccountModel().update_by_set(
            {"_id": ObjectId(integration_account_id)}, {"config": integration_account_config}
        )

        return {"data": results}

    def _validate_add_sheets_in_google_sheet_spreadsheets(self, data_validate):
        rules = {"name": [Required, InstanceOf(str), Length(1)]}
        self.abort_if_validate_error(rules, data_validate)

    def add_sheets_in_google_sheet_spreadsheets(self, spreadsheet_id):
        merchant_id = validate_merchant_header()
        body = request.get_json()
        integration_account_id = request.args.get("account_id")
        self._validate_add_sheets_in_google_sheet_spreadsheets(body)

        sheet_name = body.get("name")
        integration_account = IntegrationAccountModel().get_by_id(merchant_id, integration_account_id)
        if not integration_account:
            raise CustomError("Integration account not found")

        integration_account_config = integration_account.get("config", {})
        sheets, new_token = IntegrationAccountGoogleHelper().add_sheets_in_spreadsheet(
            spreadsheet_id, integration_account_config, sheet_name
        )

        if new_token:
            expiry = convert_isoformat_to_date(new_token.get("expiry"))
            new_token["expiry"] = expiry
            integration_account_config.update(new_token)
            # Log and update the integration account config
            MobioLogging().info("integration_account_config :: {}".format(integration_account_config))
            IntegrationAccountModel().update_by_set(
                {"_id": ObjectId(integration_account_id)}, {"config": integration_account_config}
            )
        if sheets:
            new_sheet_id = sheets["replies"][0]["addSheet"]["properties"]["sheetId"]
            new_sheet_title = sheets["replies"][0]["addSheet"]["properties"]["title"]
            result = {
                "id": new_sheet_id,
                "name": new_sheet_title,
            }
            return {"data": result}
        raise CustomError("Tên trang tính đã tồn tại.")

    def detail_sheets_in_google_sheet_spreadsheets(self, spreadsheet_id, sheet_id):
        merchant_id = validate_merchant_header()
        integration_account_id = request.args.get("account_id")

        integration_account = IntegrationAccountModel().get_by_id(merchant_id, integration_account_id)
        if not integration_account:
            raise CustomError("Integration account not found")

        integration_account_config = integration_account.get("config", {})

        sheets, _, status_trashed = IntegrationAccountGoogleHelper().get_sheets_in_spreadsheet(
            spreadsheet_id, integration_account_config
        )
        result = {}
        sheet_name = None
        for sheet in sheets:
            properties = sheet.get("properties", {})
            if properties.get("sheetId") == int(sheet_id):
                sheet_name = properties.get("title")
                result = {"id": int(sheet_id), "name": sheet_name, "status_trashed": status_trashed}

        if not result:
            raise CustomError("Sheet id not found in spreadsheet")

        return {"data": result}

    def get_columns_in_sheet_within_spreadsheet(self, spreadsheet_id, sheet_id):
        merchant_id = validate_merchant_header()
        integration_account_id = request.args.get("account_id")

        integration_account = IntegrationAccountModel().get_by_id(merchant_id, integration_account_id)
        if not integration_account:
            raise CustomError("Integration account not found")

        integration_account_config = integration_account.get("config", {})

        sheets, new_token, _ = IntegrationAccountGoogleHelper().get_sheets_in_spreadsheet(
            spreadsheet_id, integration_account_config
        )
        sheet_name = None
        for sheet in sheets:
            properties = sheet.get("properties", {})
            if properties.get("sheetId") == int(sheet_id):
                sheet_name = properties.get("title")
                break
        if not sheet_name:
            raise CustomError("Sheet id not found in spreadsheet")

        first_row, new_token = IntegrationAccountGoogleHelper().get_first_row_in_sheet_within_spreadsheet(
            spreadsheet_id, integration_account_config, sheet_name
        )

        if new_token:
            expiry = convert_isoformat_to_date(new_token.get("expiry"))
            new_token["expiry"] = expiry
            integration_account_config.update(new_token)
        # Log and update the integration account config
        MobioLogging().info("integration_account_config :: {}".format(integration_account_config))
        IntegrationAccountModel().update_by_set(
            {"_id": ObjectId(integration_account_id)}, {"config": integration_account_config}
        )
        results = []
        num_columns = 0
        if first_row:
            value_first_row = first_row.get("values", [])
            # Print each column header in the first row with column letter and index
            if value_first_row:
                num_columns = len(value_first_row[0])
                for index, column_value in enumerate(value_first_row[0], start=1):
                    column_letter = self.generate_column_letters(index)
                    results.append(
                        {"range": "{}{}".format(column_letter, str(1)), "title": column_value, "disable_edit": True}
                    )
            # Sinh thêm cột nếu thiếu
            for index in range(
                num_columns + 1,
                num_columns
                + (ConstantColumnsShowMappingGoogleSheet.MAX_COLUMNS_SHOW_MAPPING_GOOGLE_SHEET - num_columns),
            ):
                column_letter = self.generate_column_letters(index)
                results.append({"range": "{}{}".format(column_letter, str(1)), "title": "", "disable_edit": False})
            return {"data": results}
        raise CustomError("Not get column by sheet {}".format(sheet_id))

    @classmethod
    def generate_column_letters(cls, n):
        letters = []
        while n > 0:
            n, remainder = divmod(n - 1, 26)
            letters.append(string.ascii_uppercase[remainder])
        return "".join(reversed(letters))
