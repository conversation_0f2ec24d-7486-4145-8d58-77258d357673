import copy
import math
import random
import string

from flask import request
from mobio.libs.logging import Mo<PERSON>Logging
from mobio.libs.validator import Each, In, InstanceOf, Length, Required
from mobio.sdks.base.common.mobio_exception import CustomError
from mobio.sdks.base.controllers import BaseController

from src.common import LANG
from src.common.data_flow_constant import (
    ConstantDataType,
    ConstantParamApi,
    ConstantStatusConfigConnector,
    ConstantStatusConnect,
    ConstantStatusSyncData,
)
from src.common.handle_headers import get_param_value_temp, validate_merchant_header
from src.common.json_encoder import JSONEncoder
from src.common.utils import utf8_to_ascii
from src.helpers.general_param_default.handle_general_param_default import (
    HandlerGeneralParamDefault,
)
from src.internal_module.admin import AdminInternal
from src.internal_module.data_out import DataOutHelper
from src.models.data_flow.config_app_model import ConfigA<PERSON>Field, ConfigAppModel
from src.models.data_flow.config_connectors_model import (
    ConfigConnectorsModel,
    ConstantConfigConnectorsModel,
)
from src.models.data_flow.general_configuration_parameters_model import (
    GeneralConfigurationParametersModel,
)
from src.models.data_flow.information_event_data_out_model import (
    InformationEventDataOutField,
    InformationEventDataOutModel,
)
from src.models.data_flow.object_handle_model import SettingObjectHandleModel


class AppConfigController(BaseController):

    def create_app(self):
        body = request.json
        rule_input = {
            ConfigAppField.APP_NAME: [InstanceOf(str), Required, Length(minimum=1)],
        }
        app_name = body.get(ConfigAppField.APP_NAME)
        params = {ConfigAppField.APP_NAME: app_name}
        self.abort_if_validate_error(rule_input, params)

        account_id = get_param_value_temp("id")
        merchant_id = validate_merchant_header()
        update_data = body.copy()

        name_search = utf8_to_ascii(app_name).lower().replace(" ", "")
        self._validate_name_search_exists(merchant_id, app_name, name_search)

        update_data.update(
            {
                ConfigAppField.SECRET_KEY: self._random_secret_key(),
                ConfigAppField.NAME_SEARCH: name_search,
            }
        )
        data_insert = ConfigAppModel().insert_new_app(merchant_id, account_id, **update_data)
        MobioLogging().info("App created :: {}".format(data_insert))
        obj_changed = {"app_id": data_insert.get(ConfigAppField.ID), "type_change": "add_app"}
        DataOutHelper.change_config_data_out(merchant_id=validate_merchant_header(), body=obj_changed)
        return {"data": ConfigAppModel().serialize_app_data(data_insert)}

    def get_list_app_configs(self):
        args = request.args
        name_search, selected_fields = self._validate_search_app(args)

        per_page = args.get("per_page", default=20, type=int)
        page = args.get("page", default=1, type=int)
        merchant_id = validate_merchant_header()
        order_by = args.get("order_by", default=ConfigAppField.CREATED_TIME, type=str)
        order_type = args.get("order_type", default="desc", type=str)
        MobioLogging().info(
            "get_list_app_configs :: merchant_id: {} :: query_params: {}".format(merchant_id, args.to_dict())
        )

        order_type = 1 if order_type == "asc" else -1
        apps, total_app = ConfigAppModel().find_list_app(
            merchant_id=merchant_id,
            name_search=name_search,
            order_by=order_by,
            order_type=order_type,
            page=page,
            per_page=per_page,
            selected_field=selected_fields,
        )
        results = []
        app_ids = [x.get(ConfigAppField.ID) for x in apps]
        total_connector_use_app = [*ConfigConnectorsModel().count_connector_use_app_by_app_ids(merchant_id, app_ids)]
        obj_app_total = {}
        for item in total_connector_use_app:
            app_id = item.get("_id").get("app_id")
            data_type = item.get("_id").get("data_type")
            if not obj_app_total.get(app_id):
                obj_app_total.update({app_id: {"data_in": 0, "data_out": 0}})
            obj_app_total[app_id][data_type] = item.get("total")
        for item in apps:
            app_id = item.get(ConfigAppField.ID)
            app = ConfigAppModel().serialize_app_data(item, selected_fields)
            app.update({"total_used": obj_app_total.get(app_id, {"data_in": 0, "data_out": 0})})
            results.append(app)
        return {
            "data": results,
            "paging": {
                "page": page,
                "per_page": per_page,
                "page_count": math.ceil(total_app / per_page),
                "total_count": total_app,
            },
        }

    def update_app_config(self, app_id):
        body = request.get_json()

        rule_input = {
            ConfigAppField.APP_NAME: [InstanceOf(str), Length(minimum=1)],
        }
        MobioLogging().info("update_app_config :: body_data: {}".format(body))
        self.abort_if_validate_error(rule_input, body)

        app_detail = self._app_exists(app_id)
        current_name_search = app_detail.get(ConfigAppField.NAME_SEARCH)
        update_data = copy.deepcopy(body)
        app_name = update_data.get(ConfigAppField.APP_NAME, "")
        name_search = utf8_to_ascii(app_name).lower().replace(" ", "")
        if not update_data or name_search == current_name_search:
            return {"data": ConfigAppModel().serialize_app_data(app_detail)}

        merchant_id = validate_merchant_header()
        self._validate_name_search_exists(merchant_id, app_name, name_search)

        obj_update = {**update_data, ConfigAppField.NAME_SEARCH: name_search}
        account_id = get_param_value_temp("id")
        update_result = ConfigAppModel().update_app(account_id, {ConfigAppField.ID: app_id}, obj_update)
        MobioLogging().info("update_app_config :: update_result: {}".format(update_result))
        if not update_result:
            raise CustomError(self.lang.get(LANG.BAD_REQUEST).get("message"))
        app_detail.update(**update_result)
        return {"data": ConfigAppModel().serialize_app_data(app_detail)}

    def app_detail(self, app_id):
        MobioLogging().info("get_app_detail_by_id: {}".format(app_id))
        return {"data": ConfigAppModel().serialize_app_data(self._app_exists(app_id))}

    def delete_app(self, app_id):
        MobioLogging().info("delete_app: {}".format(app_id))
        self._app_exists(app_id)
        # @todo: Không được xoá khi đang được sử dụng trong connector
        connector_use_app = ConfigConnectorsModel().find_one(
            {
                "config_connect.app_id": app_id,
                "status_connect": "on",
                ConstantConfigConnectorsModel.STATUS: ConstantStatusConfigConnector.ACTIVE,
            }
        )
        if connector_use_app:
            raise CustomError(self.lang.get(LANG.APP_USE_IN_CONNECTOR).get("message"))
        # @todo: call to change config DataOut
        merchant_id = validate_merchant_header()
        config_data = {"app_id": app_id, "type_change": "del_app"}
        # @todo: Xoá app
        delete_status = ConfigAppModel().delete_app_by_id(app_id)
        MobioLogging().info("delete_app :: delete_status:: {}".format(delete_status))
        if not delete_status:
            raise CustomError(self.lang.get(LANG.BAD_REQUEST).get("message"))
        DataOutHelper.change_config_data_out(merchant_id=merchant_id, body=config_data)
        return {"code": 200}

    def generate_new_secret_key(self, app_id):
        app_detail = self._app_exists(app_id)
        new_secret_key = self._random_secret_key()
        obj_update = {ConfigAppField.SECRET_KEY: new_secret_key}
        account_id = get_param_value_temp("id")
        MobioLogging().info("generate_new_secret_key :: account_id: {}".format(account_id))
        update_result = ConfigAppModel().update_app(account_id, {ConfigAppField.ID: app_id}, obj_update)
        if not update_result:
            raise CustomError(self.lang.get(LANG.BAD_REQUEST).get("message"))

        merchant_id = validate_merchant_header()
        list_connector, total = ConfigConnectorsModel().get_list_connector_use_app_by_app_id(
            merchant_id=merchant_id, app_id=app_id, data_type="data_out", page=-1, per_page=None
        )
        list_connector_used_app = [x.get("_id") for x in list_connector]
        obj_changed = {"app_id": app_id, "type_change": "edit_app", "connector_ids": list_connector_used_app}
        DataOutHelper.change_config_data_out(merchant_id=validate_merchant_header(), body=obj_changed)
        app_detail.update(**update_result)
        return {"data": ConfigAppModel().serialize_app_data(app_detail)}

    def get_list_event_by_group(self):
        merchant_id = validate_merchant_header()
        args = request.args
        MobioLogging().info("get_list_event_by_object_handle :: params {}".format(args.to_dict()))
        group_code = args.get("group_code", "").split(",")
        status = args.get(
            InformationEventDataOutField.STATUS, default=InformationEventDataOutField.EventStatus.ACTIVE, type=str
        ).split(",")
        rule_input = {
            "group_code": [
                Required,
                InstanceOf(list),
                Each(
                    [
                        InstanceOf(str),
                        Length(1),
                        In(
                            SettingObjectHandleModel().get_list_key_source_handled_active(
                                merchant_id,
                                ConstantDataType.DATA_OUT,
                                "webhooks",
                            )
                        ),
                    ]
                ),
            ],
            "status": [
                InstanceOf(list),
                Each([InstanceOf(str), Length(1), In(InformationEventDataOutField.EventStatus.STATUS)]),
            ],
        }
        self.abort_if_validate_error(
            rule_input,
            {
                "group_code": group_code,
                "status": status,
            },
        )

        merchant_type = AdminInternal().get_merchant_type_by_merchant_id(merchant_id)
        merchant_type.append(InformationEventDataOutField.ConstantType.ALL)

        list_event = InformationEventDataOutModel().get_list_event_by_group(group_code, status, merchant_type)
        result = []
        mapping_data = {i_group_code: [] for i_group_code in group_code}
        for event in list_event:
            event_name = event.get(InformationEventDataOutField.EVENT_NAME)
            if isinstance(event_name, dict):
                event_name = event_name.get(self.language)
            event.update({InformationEventDataOutField.EVENT_NAME: event_name})
            event.update({InformationEventDataOutField.EVENT_NAME: event.get("name")})
            # result.append(InformationEventDataOutModel.serialize_event_data(event))
            mapping_data[event.get(InformationEventDataOutField.GROUP_CODE)].append(
                InformationEventDataOutModel.serialize_event_data(event)
            )
        # sort by position in mapping_data
        for i_group_code in group_code:
            mapping_data[i_group_code] = sorted(
                mapping_data[i_group_code], key=lambda k: k.get("position", 0), reverse=False
            )

        result = []
        for item in mapping_data.values():
            result.extend(item)
        return {"data": result}

    def update_event_status(self, event_key):
        body = request.json
        self.abort_if_validate_error(
            {
                InformationEventDataOutField.STATUS: [
                    Required,
                    InstanceOf(str),
                    In(InformationEventDataOutField.EventStatus.STATUS),
                ]
            },
            body,
        )
        status = body.get(InformationEventDataOutField.STATUS)
        event_exists = InformationEventDataOutModel().get_event_by_key(event_key)
        if not event_exists:
            raise CustomError(self.lang.get(LANG.NOT_FOUND).get("message"))
        update_status = InformationEventDataOutModel().update_event_status(event_key, status)
        MobioLogging().info("update_event_status :: status_update:: {}".format(update_status))
        if not update_status:
            raise CustomError("Update event status failed")
        return

    def update_is_new_app(self, app_id):
        app_detail = self._app_exists(app_id)
        is_new_app = app_detail.get(ConfigAppField.IS_NEW_APP, False)
        if is_new_app:
            MobioLogging().info("change_new_app_status :: is_new_app: {}".format(is_new_app))
            account_id = get_param_value_temp("id")
            ConfigAppModel().update_app(
                account_id, {ConfigAppField.ID: app_id}, {ConfigAppField.IS_NEW_APP: False}, update_time=False
            )
        return

    def get_list_connector_use_app(self, app_id):
        args = request.args
        data_type = args.get("data_type", "").split(",")
        rule_input = {
            "data_type": [
                Required,
                InstanceOf(list),
                Each([InstanceOf(str), In([ConstantDataType.DATA_IN, ConstantDataType.DATA_OUT])]),
            ]
        }
        MobioLogging().info("get_list_connector_use_app :: params {}".format(args.to_dict()))
        self.abort_if_validate_error(rule_input, {"data_type": data_type})
        self._app_exists(app_id)
        merchant_id = validate_merchant_header()
        page = args.get("page", default=1, type=int)
        per_page = args.get("per_page", default=10, type=int)
        list_connector, total = ConfigConnectorsModel().get_list_connector_use_app_by_app_id(
            merchant_id=merchant_id, app_id=app_id, data_type=data_type, per_page=per_page, page=page
        )
        mapping_object = {"profiles": "profile"}
        results = []
        for item in list_connector:

            data_type = item.get("data_type")
            if data_type == ConstantDataType.DATA_OUT:
                config_information_out = item.get("config_information_out", [])
                module_used = [x.get("module") for x in config_information_out]
            else:
                module_used = [mapping_object.get(item.get("object"), item.get("object"))] if item.get("object") else []
            status_sync = item.get("status_sync")
            if not status_sync:
                status_sync = (
                    ConstantStatusSyncData.RUNNING if item.get("status_connect") == ConstantStatusConnect.ON else None
                )
            results.append(
                {
                    "id": item.get("_id"),
                    ConstantParamApi.BodyWebhookCheckConnection.CONFIG_APP_ID: app_id,
                    ConstantParamApi.BodyCreateConnectors.NAME: item.get(ConstantParamApi.BodyCreateConnectors.NAME),
                    ConstantParamApi.BodyCreateConnectors.SOURCE_TYPE: item.get(
                        ConstantParamApi.BodyCreateConnectors.SOURCE_TYPE
                    ),
                    ConstantParamApi.BodyCreateConnectors.SOURCE_KEY: item.get(
                        ConstantParamApi.BodyCreateConnectors.SOURCE_KEY
                    ),
                    ConstantParamApi.BodyUpdateConnectors.IS_TRUST_SOURCE: item.get(
                        ConstantParamApi.BodyUpdateConnectors.IS_TRUST_SOURCE, False
                    ),
                    ConstantConfigConnectorsModel.CREATED_TIME: item.get(ConstantConfigConnectorsModel.CREATED_TIME),
                    ConstantParamApi.BodyCreateConnectors.DAT_TYPE: item.get(
                        ConstantParamApi.BodyCreateConnectors.DAT_TYPE
                    ),
                    "module_used": module_used,
                    "status_connect": item.get("status_connect", ConstantStatusConnect.OFF),
                    "status_sync": status_sync,
                }
            )
        return {
            "data": JSONEncoder("%Y-%m-%dT%H:%MZ").json_loads(results),
            "paging": {
                "page": page,
                "per_page": per_page,
                "page_count": math.ceil(total / per_page),
                "total_count": total,
            },
        }

    def _app_exists(self, app_id):
        app_detail = ConfigAppModel().app_detail_by_id(app_id)
        if not app_detail:
            raise CustomError(self.lang.get(LANG.NOT_FOUND).get("message"))
        return app_detail

    @classmethod
    def _validate_search_app(cls, args):
        params = {}
        name_search = args.get("name_search")
        if name_search:
            name_search = utf8_to_ascii(name_search).lower().replace(" ", "")
            params.update({"name_search": name_search})
        selected_fields = args.get("selected_filed")
        if selected_fields:
            selected_fields = [x.strip() for x in selected_fields.split(",") if x]
            params.update({"selected_field": selected_fields})
        order_by = args.get("order_by")
        if order_by:
            params.update({"order_by": order_by})
        order_type = args.get("order_type")
        if order_type:
            params.update({"order_type": order_type})

        rule_input = {
            ConfigAppField.NAME_SEARCH: [InstanceOf(str), Length(1)],
            "selected_field": [
                InstanceOf(list),
                Each([InstanceOf(str), Length(1), In(ConfigAppField().get_all_attribute())]),
            ],
            "order_by": [InstanceOf(str), In(ConfigAppField().get_all_attribute())],
            "order_type": [InstanceOf(str), In(["asc", "desc"])],
        }
        cls.abort_if_validate_error(rule_input, params)
        return name_search, selected_fields

    def _validate_name_search_exists(self, merchant_id, app_name, name_search):
        name_exists = ConfigAppModel().find_app_name_exists(merchant_id, name_search)
        if name_exists:
            raise CustomError(self.lang.get(LANG.APP_NAME_EXISTS).get("message").format(app_name=app_name), 409, 409)

    @staticmethod
    def _random_secret_key(length=20, chars=string.ascii_letters + string.digits):
        return "".join(random.choice(chars) for _ in range(length))

    def _validate_get_header_default(self, data_validate):
        rule_validate = {
            "source_key": [Required, InstanceOf(str), Length(1)],
            "data_type": [Required, InstanceOf(str), Length(1), In(ConstantDataType.get_all_attribute())],
        }
        self.abort_if_validate_error(rule_validate, data_validate)

    def get_header_default(self, app_id):
        source_key = request.args.get("source_key")
        data_type = request.args.get("data_type")
        self._validate_get_header_default({"source_key": source_key, "data_type": data_type})
        return {"data": GeneralConfigurationParametersModel().get_header_default_by_type(data_type, source_key)}

    def _validate_get_param_default_config_connect(self, data_validate):
        rule_validate = {
            "source_key": [Required, InstanceOf(str), Length(1)],
            "source_type": [Required, InstanceOf(str), Length(1)],
            "data_type": [Required, InstanceOf(str), Length(1), In(ConstantDataType.get_all_attribute())],
        }
        self.abort_if_validate_error(rule_validate, data_validate)

    def get_param_default_config_connect(self, app_id):
        source_key = request.args.get("source_key")
        data_type = request.args.get("data_type")
        source_type = request.args.get("source_type")
        merchant_id = validate_merchant_header()
        self._validate_get_param_default_config_connect(
            {"source_key": source_key, "data_type": data_type, "source_type": source_type}
        )
        app_detail = self._app_exists(app_id)
        result = HandlerGeneralParamDefault().call(
            data_type, source_type, source_key, "information_config_connect", merchant_id, app_detail
        )

        return {"data": result}
