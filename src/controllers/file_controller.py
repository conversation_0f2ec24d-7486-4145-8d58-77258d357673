import datetime
import shutil

from flask import request
from mobio.libs.logging import <PERSON><PERSON>Logging
from mobio.libs.validator import In, InstanceOf
from mobio.sdks.base.common.mobio_exception import CustomError
from mobio.sdks.base.controllers import BaseController
from mobio.sdks.media.mobio_media_sdk import MobioMediaSDK

from src.common import FILE_UPLOAD
from src.common.handle_headers import validate_merchant_header
from src.common.json_encoder import <PERSON><PERSON><PERSON><PERSON><PERSON>
from src.internal_module.admin import GetInternalHost
from src.models.file_upload_model import (
    FileUploadField,
    FileUploadModel,
    TypeFileUpload,
)


class FileControllers(BaseController):

    def validate_file_upload(self, input_data):
        rule_input = {"type": [InstanceOf(str), In(TypeFileUpload.get_all_attribute())]}
        self.abort_if_validate_error(rule_input, input_data)

    def upload_file(self):
        merchant_id = validate_merchant_header()
        file = request.files.get("file")
        url = request.form.get("url")
        type = request.form.get("type", TypeFileUpload.IMAGE)
        validate_data = {"type": type}
        self.validate_file_upload(validate_data)
        if not url and not file:
            raise CustomError("url or file is required")
        now = datetime.datetime.utcnow()
        if url:
            filepath = MobioMediaSDK().get_path_by_url(url, is_check_path_exists=False)
            new_file_path = shutil.copy(filepath, FILE_UPLOAD)
            info_upload = MobioMediaSDK().upload_with_kafka(
                merchant_id=merchant_id, file_path=new_file_path, do_not_delete=True
            )
            MobioLogging().debug("upload_file :: info_upload %s " % info_upload)
            parts = url.split("/")
            info_upload["filename"] = parts[-1]
            data_insert = {FileUploadField.TYPE: type, FileUploadField.CREATED_TIME: now}
            data_insert.update(info_upload)
        elif file:
            filename_origin = file.filename
            info_upload = MobioMediaSDK().upload_with_kafka(merchant_id=merchant_id, file_data=file, do_not_delete=True)
            MobioLogging().debug("upload_file :: info_upload %s " % info_upload)
            info_upload["filename"] = filename_origin
            data_insert = {FileUploadField.TYPE: type, FileUploadField.CREATED_TIME: now}
            data_insert.update(info_upload)
        if type in [TypeFileUpload.SDK, TypeFileUpload.INSTRUCTIONS_ADD_GOOGLE_TAG_IN_WEBSITE]:
            query = {FileUploadField.TYPE: type}
            file_id = str(FileUploadModel().upsert(query, data_insert))
        else:
            file_id = str(FileUploadModel().insert_document(data_insert).inserted_id)

        MobioLogging().debug("upload_file :: file_id %s " % file_id)

        if not file_id:
            raise CustomError("File upload failed")

        return {"data": JSONEncoder("%Y-%m-%dT%H:%MZ").json_loads(data_insert)}

    def get_file(self):
        merchant_id = validate_merchant_header()
        param_type = request.args.get("type", TypeFileUpload.SDK)
        public_host = GetInternalHost.get_internal_host_by_merchant(merchant_id, "public-host")
        query = {FileUploadField.TYPE: param_type}
        result = FileUploadModel().find_one(query)
        if result:
            if param_type == TypeFileUpload.SDK:
                result[FileUploadField.URL] = result[FileUploadField.URL].replace(
                    "#DOMAIN_RESOURCES_PUBLIC#", public_host
                )
            return {"data": JSONEncoder("%Y-%m-%dT%H:%MZ").json_loads(result)}
        return {"data": {}}
