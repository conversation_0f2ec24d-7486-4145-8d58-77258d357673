import json
from unittest import result

from bson import ObjectId
from flask import request
from mobio.libs.logging import MobioLogging
from mobio.libs.validator import In, InstanceOf, Length, Required
from mobio.sdks.admin import <PERSON><PERSON>AdminSDK
from mobio.sdks.base.common.mobio_exception import CustomError

from configs import MarketPlaceApplicationConfig
from src.common import (
    LANG,
    CommonKeys,
    KeyHostService,
    LogActionKeys,
    ProviderConfigKeys,
    ProviderKeys,
)
from src.common.handle_headers import get_param_value_temp, validate_merchant_header
from src.common.json_encoder import J<PERSON>NEncoder
from src.controllers import MarketPlaceBaseController
from src.internal_module.admin import GetInternalHost
from src.internal_module.journey_builder import JourneyBuilderHelper
from src.internal_module.notify_management import NotifyManagementHelper
from src.models.log_action import LogActionModel
from src.models.provider_config_model import ProviderConfigModel
from src.models.provider_model import ProviderModel


class SmsConfigController(MarketPlaceBaseController):

    def send_request_upsert_provider_config(self, merchant_id, payload):
        status_upsert = NotifyManagementHelper.upsert_provider_config(merchant_id=merchant_id, payload=payload)
        if not status_upsert:
            raise CustomError(
                self.lang.get(LANG.INTERNAL_SERVER_ERROR).get("message"),
                self.lang.get(LANG.INTERNAL_SERVER_ERROR).get("code"),
            )
        return True

    def send_message_request(self, merchant_id, payload):
        status_send_message = NotifyManagementHelper.send_test_message(merchant_id=merchant_id, payload=payload)
        if not status_send_message:
            return 0
        return 1

    def get_send_payload(self, provider, connect_config_info, auth_attachment, config_type, status):
        configs = provider.get(ProviderKeys.CONNECT_CONFIG_INFO)
        provider_type = provider.get(ProviderKeys.PROVIDER_TYPE)
        keys_connect_for_type = provider.get(ProviderKeys.KEYS_CONNECT_FOR_TYPE)

        auth_name = ""
        auth_pass = ""
        api = connect_config_info.pop(ProviderConfigKeys.API)
        if keys_connect_for_type:
            connect_config_info.update(keys_connect_for_type.get(config_type))

        for config in configs:
            if config.get(ProviderConfigKeys.ConfigInfoKeys.FIELD) == "auth_name":
                auth_name = connect_config_info.pop(config.get(ProviderConfigKeys.ConfigInfoKeys.KEY))
            elif config.get(ProviderConfigKeys.ConfigInfoKeys.FIELD) == "auth_pass":
                auth_pass = connect_config_info.pop(config.get(ProviderConfigKeys.ConfigInfoKeys.KEY))

        return {
            ProviderConfigKeys.PROVIDER_TYPE: provider_type,
            ProviderConfigKeys.STATUS: status,
            ProviderConfigKeys.TYPE: config_type,
            ProviderConfigKeys.AUTH_ATTACHMENT: auth_attachment,
            ProviderConfigKeys.AUTH_NAME: auth_name,
            ProviderConfigKeys.AUTH_PASS: auth_pass,
            ProviderConfigKeys.PROVIDER_API: api,
            ProviderConfigKeys.OTHERS: connect_config_info,
        }

    def convert_connect_config_info(self, connect_config_info):
        results = dict()
        for config in connect_config_info:
            results.update(
                {
                    config[ProviderConfigKeys.ConfigInfoKeys.KEY]: config[ProviderConfigKeys.ConfigInfoKeys.VALUE],
                }
            )

        return results

    def validate_sms_config(self, input_data):
        rule_input = {
            ProviderConfigKeys.PROVIDER_TYPE: [Required, InstanceOf(int)],
            ProviderConfigKeys.NAME: [Required, InstanceOf(str)],
            ProviderConfigKeys.TYPE: [Required, InstanceOf(str)],
            ProviderConfigKeys.DESCRIPTION: [Required, InstanceOf(str)],
            ProviderConfigKeys.CONNECT_CONFIG_INFO: [Required, InstanceOf(dict), Length(3)],
        }

        self.abort_if_validate_error(rule_input, input_data)

    def validate_update_status_sms_config(self, input_data):
        rule_input = {
            ProviderConfigKeys.STATUS: [Required, InstanceOf(int), In([0, 1])],
        }
        self.abort_if_validate_error(rule_input, input_data)

    def validate_decode_sms_config(self, input_data):
        rule_input = {
            ProviderConfigKeys.ConfigInfoKeys.KEY: [Required, InstanceOf(str)],
        }
        self.abort_if_validate_error(rule_input, input_data)

    def validate_sync_data(self, input_data):
        rule_input = {
            ProviderConfigKeys.NAME: [Required, InstanceOf(str)],
            ProviderConfigKeys.TYPE: [Required, InstanceOf(str)],
            ProviderConfigKeys.STATUS: [Required, InstanceOf(int), In([0, 1])],
            ProviderConfigKeys.PROVIDER_TYPE: [Required, InstanceOf(int)],
            ProviderConfigKeys.PROVIDER_API: [Required, InstanceOf(str)],
            ProviderConfigKeys.DESCRIPTION: [
                Required,
                InstanceOf(str),
            ],
            ProviderConfigKeys.CONNECT_CONFIG_INFO: [Required, InstanceOf(list), Length(2)],
        }

        self.abort_if_validate_error(rule_input, input_data)

    def validate_send_sms(self, input_data):
        rule_input = {
            "to": [Required, InstanceOf(list)],
        }

        self.abort_if_validate_error(rule_input, input_data)

    def get_channel(self, config_type):
        channel_mapping = {"CSKH": "SMS_CUSTOMER_CARE", "QC": "SMS_ADVERTISEMENT"}

        return channel_mapping.get(config_type)

    def create_sms_config(self):
        func_name = self.create_sms_config.__name__
        merchant_id = validate_merchant_header()
        account_id = get_param_value_temp("id")
        payload = request.get_json()
        self.validate_sms_config(payload)

        provider_config_model: ProviderConfigModel = ProviderConfigModel()
        provider_model: ProviderModel = ProviderModel()
        log_action_model: LogActionModel = LogActionModel()

        name = payload.get(ProviderConfigKeys.NAME)
        connect_config_info = payload.get(ProviderConfigKeys.CONNECT_CONFIG_INFO)
        provider_type = payload.get(ProviderConfigKeys.PROVIDER_TYPE)
        config_type = payload.get(ProviderConfigKeys.TYPE)

        provider = provider_model.get_provider_by_code(provider_type, "sms")
        if not provider:
            raise CustomError("Provider not found", 404)

        if provider_config_model.count_provider_config_in_merchant(merchant_id, provider_type) >= 10:
            raise CustomError(self.lang.get(LANG.CONFIGURATION_LIMIT).get("message"))

        provider_config = provider_config_model.get_provider_config_by_name(
            merchant_id, name, provider_type, config_type
        )
        if provider_config:
            raise CustomError(
                self.lang.get(LANG.SMS_CONFIG_EXISTS).get("message"), self.lang.get(LANG.SMS_CONFIG_EXISTS).get("code")
            )

        connect_config_info_copy = connect_config_info.copy()
        # create sms config to notify management module
        send_payload = self.get_send_payload(
            provider,
            connect_config_info_copy,
            payload.get(ProviderConfigKeys.NAME),
            payload.get(ProviderConfigKeys.TYPE),
            1,
        )
        self.send_request_upsert_provider_config(merchant_id, send_payload)

        connect_config_info_str = json.dumps(connect_config_info)

        data_encode = MobioAdminSDK().encrypt_values(
            merchant_id=merchant_id,
            module="MARKET_PLACE",
            field=ProviderConfigKeys.CONNECT_CONFIG_INFO,
            values=connect_config_info_str,
        )

        payload.update(
            {
                ProviderConfigKeys.CONNECT_CONFIG_INFO: data_encode.get("data").get(connect_config_info_str),
                ProviderConfigKeys.STATUS: 1,
            }
        )

        inserted_id = provider_config_model.create_provider_config(payload, merchant_id, account_id)
        MobioLogging().debug(
            "{} :: {} :: inserted_id :: {}".format(self.__class__.__name__, func_name, str(inserted_id))
        )

        # log
        log_action_model.insert_log_action(merchant_id, account_id, str(inserted_id), "create", 1, "sms")

        sms_config = provider_config_model.get_detail_provider_config_by_id(
            merchant_id,
            str(inserted_id),
            fields_select={
                ProviderConfigKeys.CONNECT_CONFIG_INFO: 0,
                ProviderConfigKeys.LOWER_CASE_NAME: 0,
            },
        )
        sms_config.update(
            {
                ProviderKeys.PROVIDER: provider.get(ProviderKeys.PROVIDER),
                ProviderKeys.PROVIDER_LINK_IMAGE: provider.get(ProviderKeys.PROVIDER_LINK_IMAGE),
            }
        )

        return {"data": self.json_encoder(sms_config)}

    def change_status_sms_config(self, sms_config_id):
        func_name = self.change_status_sms_config.__name__
        merchant_id = validate_merchant_header()
        account_id = get_param_value_temp("id")

        provider_config_model: ProviderConfigModel = ProviderConfigModel()
        provider_model: ProviderModel = ProviderModel()
        log_action_model: LogActionModel = LogActionModel()

        payload = request.get_json()
        self.validate_update_status_sms_config(payload)

        provider_config = provider_config_model.get_detail_provider_config_by_id(merchant_id, sms_config_id)
        if not provider_config:
            raise CustomError("Sms config not found", 404)

        status = payload.get(ProviderConfigKeys.STATUS)
        name = provider_config.get(ProviderConfigKeys.NAME)
        config_type = provider_config.get(ProviderConfigKeys.TYPE)

        if status == 0:
            jb_helper: JourneyBuilderHelper = JourneyBuilderHelper()
            payload_get_campaign_request = {"channel": self.get_channel(config_type), "sender": name}
            data = jb_helper.get_campaigns(merchant_id, payload=payload_get_campaign_request)
            if data:
                raise CustomError("There is a list of campaigns used in the config", 400)

        provider_type = provider_config.get(ProviderConfigKeys.PROVIDER_TYPE)
        connect_config_info_str = provider_config.get(ProviderConfigKeys.CONNECT_CONFIG_INFO)

        provider = provider_model.get_provider_by_code(provider_type, "sms")

        data_decode = MobioAdminSDK().decrypt_values(
            merchant_id=merchant_id,
            module="MARKET_PLACE",
            field=ProviderConfigKeys.CONNECT_CONFIG_INFO,
            values=connect_config_info_str,
        )

        send_payload = self.get_send_payload(
            provider,
            json.loads(data_decode.get("data").get(connect_config_info_str)),
            provider_config.get(ProviderConfigKeys.NAME),
            provider_config.get(ProviderConfigKeys.TYPE),
            1 if status == 1 else 2,
        )

        self.send_request_upsert_provider_config(merchant_id, send_payload)

        matched_count = provider_config_model.update_provider_config_by_id(sms_config_id, payload, account_id)
        if not matched_count:
            raise CustomError("Update sms config failed", 500)
        MobioLogging().debug(
            "{} :: {} :: matched_count :: {}".format(self.__class__.__name__, func_name, matched_count)
        )

        # log
        action = "active" if status == 1 else "inactive"
        log_action_model.insert_log_action(merchant_id, account_id, sms_config_id, action, 1, "sms")
        return

    def get_detail_sms_config(self, sms_config_id):
        merchant_id = validate_merchant_header()

        provider_model: ProviderModel = ProviderModel()
        provider_config_model: ProviderConfigModel = ProviderConfigModel()
        sms_config = provider_config_model.get_detail_provider_config_by_id(
            merchant_id,
            sms_config_id,
            fields_select={
                ProviderConfigKeys.CONNECT_CONFIG_INFO: 0,
                ProviderConfigKeys.LOWER_CASE_NAME: 0,
            },
        )
        if not sms_config:
            raise CustomError("Sms config not found", 404)
        provider = provider_model.get_provider_by_code(sms_config.get(ProviderConfigKeys.PROVIDER_TYPE), "sms")
        sms_config.update(
            {
                ProviderKeys.PROVIDER: provider.get(ProviderKeys.PROVIDER),
                ProviderKeys.PROVIDER_LINK_IMAGE: provider.get(ProviderKeys.PROVIDER_LINK_IMAGE),
                ProviderKeys.CONNECT_CONFIG_INFO: provider.get(ProviderKeys.CONNECT_CONFIG_INFO),
            }
        )
        return {"data": self.json_encoder(sms_config)}

    def decrypt_values(self, sms_config_id):
        merchant_id = validate_merchant_header()

        provider_model: ProviderModel = ProviderModel()
        provider_config_model: ProviderConfigModel = ProviderConfigModel()

        # TODO: check permission

        key = request.args.get(ProviderConfigKeys.ConfigInfoKeys.KEY)

        provider_config = provider_config_model.get_detail_provider_config_by_id(merchant_id, sms_config_id)
        provider_type = provider_config.get(ProviderConfigKeys.PROVIDER_TYPE)
        connect_config_info_str = provider_config.get(ProviderConfigKeys.CONNECT_CONFIG_INFO)

        provider = provider_model.get_provider_by_code(provider_type, "sms")
        connect_config_info = provider.get(ProviderKeys.CONNECT_CONFIG_INFO)

        if not next(filter(lambda x: x.get(ProviderConfigKeys.ConfigInfoKeys.KEY) == key, connect_config_info), ""):
            raise CustomError("Key not found", 404)

        data_decode = MobioAdminSDK().decrypt_values(
            merchant_id=merchant_id,
            module="MARKET_PLACE",
            field=ProviderConfigKeys.CONNECT_CONFIG_INFO,
            values=connect_config_info_str,
        )

        connect_config_info = json.loads(data_decode.get("data").get(connect_config_info_str))

        return {"data": self.json_encoder({key: connect_config_info[key]})}

    def delete_sms_config(self, sms_config_id):
        func_name = self.delete_sms_config.__name__
        merchant_id = validate_merchant_header()

        provider_model: ProviderModel = ProviderModel()
        jb_helper: JourneyBuilderHelper = JourneyBuilderHelper()
        provider_config_model: ProviderConfigModel = ProviderConfigModel()

        provider_config = provider_config_model.get_detail_provider_config_by_id(merchant_id, sms_config_id)
        if not provider_config:
            raise CustomError("Sms config not found", 404)

        status = provider_config.get(ProviderConfigKeys.STATUS)
        name = provider_config.get(ProviderConfigKeys.NAME)
        config_type = provider_config.get(ProviderConfigKeys.TYPE)
        provider_type = provider_config.get(ProviderConfigKeys.PROVIDER_TYPE)
        connect_config_info_str = provider_config.get(ProviderConfigKeys.CONNECT_CONFIG_INFO)

        if status == 1:
            provider = provider_model.get_provider_by_code(provider_type, "sms")
            payload = {"channel": self.get_channel(config_type), "sender": name}
            data = jb_helper.get_campaigns(merchant_id, payload=payload)
            if data:
                raise CustomError("There is a list of campaigns used in the config", 400)

            data_decode = MobioAdminSDK().decrypt_values(
                merchant_id=merchant_id,
                module="MARKET_PLACE",
                field=ProviderConfigKeys.CONNECT_CONFIG_INFO,
                values=connect_config_info_str,
            )

            send_payload = self.get_send_payload(
                provider,
                json.loads(data_decode.get("data").get(connect_config_info_str)),
                name,
                config_type,
                2,
            )

            self.send_request_upsert_provider_config(merchant_id, send_payload)

        deleted_count = provider_config_model.delete_provider_config(merchant_id, sms_config_id)
        MobioLogging().debug(
            "{} :: {} :: deleted_count :: {}".format(self.__class__.__name__, func_name, deleted_count)
        )
        if not deleted_count:
            raise CustomError("Delete sms config failed", 500)
        return

    def get_sms_configs(self):
        merchant_id = validate_merchant_header()

        search = request.args.get("search", "")
        order = int(request.args.get("order", -1))
        sort = request.args.get("sort", CommonKeys.UPDATED_TIME)
        page = int(request.args.get("page", 1))
        per_page = int(request.args.get("per_page", 10))

        provider_model: ProviderModel = ProviderModel()
        provider_config_model: ProviderConfigModel = ProviderConfigModel()

        providers = list(provider_model.find({"service": "sms"}))
        provider_types = [provider.get(ProviderKeys.PROVIDER_TYPE) for provider in providers]

        fields_select = {ProviderConfigKeys.LOWER_CASE_NAME: 0, ProviderConfigKeys.CONNECT_CONFIG_INFO: 0}

        sms_configs, paging = provider_config_model.get_provider_configs(
            merchant_id, search, provider_types, order, sort, page, per_page, fields_select
        )

        # optimize
        for config in sms_configs:
            provider_type = config.get(ProviderConfigKeys.PROVIDER_TYPE)
            provider = next(filter(lambda x: x.get(ProviderKeys.PROVIDER_TYPE) == provider_type, providers), {})
            config.update(
                {
                    ProviderKeys.PROVIDER: provider.get(ProviderKeys.PROVIDER),
                    ProviderKeys.PROVIDER_LINK_IMAGE: provider.get(ProviderKeys.PROVIDER_LINK_IMAGE),
                }
            )
            self.json_encoder(config)

        return {
            "data": self.json_encoder(sms_configs),
            "paging": paging,
        }

    def sync_data(self):
        func_name = self.sync_data.__name__
        merchant_id = validate_merchant_header()

        payload = request.get_json()
        self.validate_sync_data(payload)
        account_id = get_param_value_temp("id")

        provider_config_model: ProviderConfigModel = ProviderConfigModel()

        provider_api = payload.get(ProviderConfigKeys.PROVIDER_API)
        connect_config_info = payload.get(ProviderConfigKeys.CONNECT_CONFIG_INFO)
        connect_config_info.append(
            {
                ProviderConfigKeys.ConfigInfoKeys.KEY: ProviderConfigKeys.API,
                ProviderConfigKeys.ConfigInfoKeys.VALUE: provider_api,
            }
        )

        connect_config_info = self.convert_connect_config_info(connect_config_info)
        connect_config_info_str = json.dumps(connect_config_info)

        data_encode = MobioAdminSDK().encrypt_values(
            merchant_id=merchant_id,
            module="MARKET_PLACE",
            field=ProviderConfigKeys.CONNECT_CONFIG_INFO,
            values=connect_config_info_str,
        )

        payload.update({ProviderConfigKeys.CONNECT_CONFIG_INFO: data_encode.get("data").get(connect_config_info_str)})
        payload.pop(ProviderConfigKeys.PROVIDER_API)

        inserted_id = provider_config_model.create_provider_config(payload, merchant_id, account_id)
        MobioLogging().debug(
            "{} :: {} :: inserted_id :: {}".format(self.__class__.__name__, func_name, str(inserted_id))
        )
        return

    def add_information_user_in_logs(self, logs, merchant_id, account_id):
        account_ids = [log.get(CommonKeys.CREATED_BY) for log in logs]
        users_information = {}
        for account_id in account_ids:
            users_information[account_id] = GetInternalHost.get_detail_user_information_by_account_id(
                merchant_id, account_id
            )
        for log in logs:
            log.update(users_information[log.get(CommonKeys.CREATED_BY)])

        return logs

    def get_logs_sms_config(self, provider_config_id):
        merchant_id = validate_merchant_header()
        account_id = get_param_value_temp("id")

        config_type = request.args.get("type")
        order = int(request.args.get("order", -1))
        sort = request.args.get("sort", CommonKeys.CREATED_TIME)
        page = int(request.args.get("page", 1))
        per_page = int(request.args.get("per_page", 10))

        log_action_model: LogActionModel = LogActionModel()

        log_query = {
            CommonKeys.MERCHANT_ID: merchant_id,
            LogActionKeys.TYPE: config_type,
            LogActionKeys.PROVIDER_CONFIG_ID: provider_config_id,
        }

        logs, paging = log_action_model.get_logs(log_query, order, sort, page, per_page)
        results = [self.json_encoder(log) for log in self.add_information_user_in_logs(logs, merchant_id, account_id)]
        return {"data": results, "paging": paging}

    def send_sms(self, sms_config_id):
        merchant_id = validate_merchant_header()
        payload = request.get_json()
        self.validate_send_sms(payload)
        account_id = get_param_value_temp("id")

        sent_to_information = payload.get("to")

        provider_config_model: ProviderConfigModel = ProviderConfigModel()

        provider_config = provider_config_model.get_detail_provider_config_by_id(merchant_id, sms_config_id)
        if not provider_config:
            raise CustomError("Sms config not found", 404)

        name = provider_config.get(ProviderConfigKeys.NAME)

        send_from = {
            "source": "market_place",
            "merchant_id": merchant_id,
            "brandname": name,
            "anti_spam": True,
            "push_event": True,
        }

        for information in sent_to_information:
            information.update(
                {
                    "push_webhook": "{}/api/v1.0/webhook/sms-configs/response-send-sms?merchant_id={}&account_id={}&sms_config_id={}".format(
                        GetInternalHost.get_internal_host_by_merchant(
                            merchant_id, key=KeyHostService.MARKET_PLACE_SERVICE_HOST
                        ),
                        merchant_id,
                        account_id,
                        sms_config_id,
                    )
                }
            )

        send_payload = {"from": send_from, "to": sent_to_information}
        MobioLogging().debug(
            "{} :: {} :: send_payload :: {}".format(self.__class__.__name__, self.send_sms.__name__, send_payload)
        )
        self.send_message_request(merchant_id, send_payload)
        return

    def response_send_sms(self):

        merchant_id = request.args.get("merchant_id")
        account_id = request.args.get("account_id")
        sms_config_id = request.args.get("sms_config_id")
        status = request.args.get("status")

        log_action_model: LogActionModel = LogActionModel()

        MobioLogging().info(
            "{} :: {} :: merchant_id :: {}:: account_id :: {} :: sms_config_id :: {} :: status :: {}".format(
                self.__class__.__name__, self.response_send_sms.__name__, merchant_id, account_id, sms_config_id, status
            )
        )
        status_log = 0
        if status and int(status) == 200:
            status_log = 1

        log_action_model.insert_log_action(merchant_id, account_id, sms_config_id, "send_message", status_log, "sms")
        return
