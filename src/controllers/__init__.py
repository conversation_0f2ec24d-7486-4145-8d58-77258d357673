from flask import request
from mobio.sdks.base.common.mobio_exception import ParamInvalidError
from mobio.sdks.base.controllers import BaseController

from src.common import LANG
from src.common.json_encoder import JSONEncoder
from src.common.lang_config import LANG_EN, LANG_VI, LangConfig


class MarketPlaceBaseController(BaseController):
    def __init__(self):
        super().__init__()
        try:
            param = request.args.get("lang")
            if param and param not in (LANG_VI, LANG_EN):
                raise ParamInvalidError(LANG.LANG_ERROR)
            self.lang = LangConfig().lang_map(param)
        except Exception as ex:
            print("MainBaseController :: %s " % ex)
            param = None
        self.language = param or LANG_VI

    def json_encoder(self, data):
        return JSONEncoder("%Y-%m-%dT%H:%MZ").json_loads(data)
