import copy
import json

import xlsxwriter
from flask import request
from mobio.libs.logging import MobioLogging
from mobio.libs.validator import In, InstanceOf, Required
from mobio.sdks.admin import MobioAdminSDK
from mobio.sdks.base.common.mobio_exception import CustomError, InputNotFoundError
from mobio.sdks.media.mobio_media_sdk import MobioMediaSDK

from src.common import (
    LANG,
    CommonKeys,
    ConfigTypeChoices,
    KeyHostService,
    LogActionKeys,
    ProviderConfigKeys,
    ProviderKeys,
    ServiceKeys,
    StatusChoice,
    ProviderType
)
from src.common.handle_headers import get_param_value_temp, validate_merchant_header
from src.common.handle_queue import HandleQueue
from src.common.utils import get_time_now
from src.controllers import MarketPlaceBaseController
from src.controllers.sms_config_controller import SmsConfigController
from src.helpers.export.csv_injection import remove_csv_injection_chars
from src.helpers.caching import CachingHelpers
from src.internal_module.admin import GetInternalHost
from src.internal_module.journey_builder import Journey<PERSON><PERSON>erHelper
from src.internal_module.mobio_mailer import MobioMailerHelper
from src.internal_module.notify_management import NotifyManagementHelper
from src.internal_module.workflow import WorkflowHelper
from src.models.log_action import LogActionModel
from src.models.provider_config_model import ProviderConfigModel
from src.models.provider_model import ProviderModel
from src.models.region_ses_model import RegionSesModel
from src.models.config_show_account_ses_model import ConfigShowSesModel, ConfigSesField


class EmailConfigController(MarketPlaceBaseController):

    def validate_create_email_config(self, payload):
        rule_input = {
            ProviderConfigKeys.PROVIDER_TYPE: [Required, InstanceOf(int)],
            ProviderConfigKeys.NAME: [Required, InstanceOf(str)],
            ProviderConfigKeys.TYPE: [InstanceOf(list)],
            ProviderConfigKeys.DESCRIPTION: [Required, InstanceOf(str)],
            ProviderConfigKeys.CONNECT_CONFIG_INFO: [Required, InstanceOf(dict)],
            ProviderConfigKeys.DOMAINS: [InstanceOf(list)]
        }

        self.abort_if_validate_error(rule_input, payload)

    def validate_update_status_email_config(self, payload):
        rule_input = {
            ProviderConfigKeys.STATUS: [Required, InstanceOf(int), In([0, 1, 2])],
        }
        self.abort_if_validate_error(rule_input, payload)

    def validate_add_email(self, payload):
        rule_input = {"email": [Required, InstanceOf(str)]}
        self.abort_if_validate_error(rule_input, payload)

    def validate_update_email_config(self, payload):
        rule_input = {ProviderConfigKeys.TYPE: [Required, InstanceOf(list)]}
        self.abort_if_validate_error(rule_input, payload)

    def validate_update_email_config_aws(self, payload):
        if not isinstance(payload, dict) or ProviderConfigKeys.DOMAINS not in payload:
            raise CustomError("Payload must be a dictionary containing 'domains' key", 400)

        domains = payload.get(ProviderConfigKeys.DOMAINS)
        if not isinstance(domains, list) or len(domains) == 0:
            raise CustomError("Domains must be a non-empty list", 400)

        rule_input = {
            ProviderConfigKeys.DOMAIN: [Required, InstanceOf(str)],
            ProviderConfigKeys.TYPE: [Required, InstanceOf(list)],
            ProviderConfigKeys.STATUS: [Required, InstanceOf(int)],
            ProviderConfigKeys.DESCRIPTION: [Required, InstanceOf(str)]
        }
        for item in domains:
            self.abort_if_validate_error(rule_input, item)

    def validate_send_test_email(self, payload):
        rule_input = {
            "from": [Required, InstanceOf(str)],
            "title": [Required, InstanceOf(str)],
            "to": [Required, InstanceOf(str)],
        }
        self.abort_if_validate_error(rule_input, payload)

    def validate_change_status_domain(self, payload):
        rule_input = {
            "domain": [Required, InstanceOf(str)],
            "status": [Required, InstanceOf(int), In([1,2])]
        }
        self.abort_if_validate_error(rule_input, payload)

    def validate_show_config_account_ses(self, payload):
        rule_input = {
            ProviderConfigKeys.STATUS: [Required, InstanceOf(int), In([0, 1])],
        }
        self.abort_if_validate_error(rule_input, payload)

    def validate_sync_data(self, input_data):
        rule_input = {
            ProviderConfigKeys.NAME: [Required, InstanceOf(str)],
            ProviderConfigKeys.TYPE: [Required, InstanceOf(list)],
            ProviderConfigKeys.STATUS: [Required, InstanceOf(int), In([0, 1])],
            ProviderConfigKeys.PROVIDER_TYPE: [Required, InstanceOf(int)],
            ProviderConfigKeys.DESCRIPTION: [
                Required,
                InstanceOf(str),
            ],
            ProviderConfigKeys.CONNECT_CONFIG_INFO: [Required, InstanceOf(dict)],
        }

        type_config = input_data.get(ProviderConfigKeys.TYPE)
        if not any(["message" in type_config, "notification" in type_config]):
            raise CustomError("Type must contain message or notification or both", 400)

        self.abort_if_validate_error(rule_input, input_data)

    def get_send_payload(self, provider, connect_config_info, auth_attachment, status):
        configs = provider.get(ProviderKeys.CONNECT_CONFIG_INFO)
        provider_type = provider.get(ProviderKeys.PROVIDER_TYPE)

        auth_name = ""
        auth_pass = ""

        for config in configs:
            if config.get(ProviderConfigKeys.ConfigInfoKeys.FIELD) == "auth_name":
                auth_name = connect_config_info.pop(config.get(ProviderConfigKeys.ConfigInfoKeys.KEY))
            elif config.get(ProviderConfigKeys.ConfigInfoKeys.FIELD) == "auth_pass":
                auth_pass = connect_config_info.pop(config.get(ProviderConfigKeys.ConfigInfoKeys.KEY))

        return {
            ProviderConfigKeys.PROVIDER_TYPE: provider_type,
            ProviderConfigKeys.STATUS: status,
            ProviderConfigKeys.AUTH_ATTACHMENT: auth_attachment,
            ProviderConfigKeys.AUTH_NAME: auth_name,
            ProviderConfigKeys.AUTH_PASS: auth_pass,
            ProviderConfigKeys.OTHERS: connect_config_info,
        }

    def add_value_in_connect_config_info(self, provider, email_config, merchant_id):
        connect_config_info = provider.get(ProviderConfigKeys.CONNECT_CONFIG_INFO)
        provider_code = provider.get(ProviderKeys.PROVIDER_TYPE)
        connect_config_info_str = email_config.get(
            ProviderConfigKeys.CONNECT_CONFIG_INFO
        )

        if not connect_config_info:
            return []

        data_decode = MobioAdminSDK().decrypt_values(
            merchant_id=merchant_id,
            module="MARKET_PLACE",
            field=ProviderConfigKeys.CONNECT_CONFIG_INFO,
            values=connect_config_info_str,
        )

        config_detail = json.loads(data_decode.get("data").get(connect_config_info_str))

        config_show_account_ses = {}
        if provider_code == ProviderType.SES:
            config_show_account_ses = ConfigShowSesModel().get_account_ses_config(merchant_id)

        results = []
        for config_info in connect_config_info:
            decrypt = config_info.get(ProviderKeys.DECRYPT)
            # key = config_info.get(ProviderConfigKeys.ConfigInfoKeys.KEY)
            #
            # if provider_code == 203 and key == "config_set":
            #     continue

            if config_show_account_ses and config_show_account_ses.get(ConfigSesField.STATUS):
                config_info["value"] = config_detail.get(config_info.get(ProviderConfigKeys.ConfigInfoKeys.KEY))
            else:
                if not decrypt:
                    config_info["value"] = config_detail.get(config_info.get(ProviderConfigKeys.ConfigInfoKeys.KEY))
                else:
                    config_info["value"] = "*************************"

            results.append(config_info)

        return results

    def add_modules_used_in_email(self, merchant_id, domain, emails):
        results = []

        modules_used_in_domain = NotifyManagementHelper.get_modules_by_domain(merchant_id=merchant_id, domain=domain)

        for email in emails:
            modules = []
            info_modules_used_email = next(filter(lambda x: x.get("email") == email, modules_used_in_domain), {})
            modules_code = info_modules_used_email.get("modules_code", [])

            for module in modules_code:
                modules.append(module.get("name"))

            results.append({"email": email, "modules": modules})
        return results

    def create_email_config(self):
        func_name = self.create_email_config.__name__
        merchant_id = validate_merchant_header()
        account_id = get_param_value_temp("id")
        payload = request.get_json()
        self.validate_create_email_config(payload)

        provider_model: ProviderModel = ProviderModel()
        log_action_model: LogActionModel = LogActionModel()
        provider_config_model: ProviderConfigModel = ProviderConfigModel()
        notify_management_helper: NotifyManagementHelper = NotifyManagementHelper()

        name = payload.get(ProviderConfigKeys.NAME)
        connect_config_info = payload.get(ProviderConfigKeys.CONNECT_CONFIG_INFO)
        provider_type = payload.get(ProviderConfigKeys.PROVIDER_TYPE)
        config_type = payload.get(ProviderConfigKeys.TYPE)

        status = StatusChoice.ACTIVE

        provider = provider_model.get_provider_by_code(provider_type, "email")
        if not provider:
            raise CustomError(self.lang.get(LANG.PROVIDER_NOT_FOUND).get("message"), 404)

        provider_name = provider.get(ProviderKeys.PROVIDER)
        provider_ses_transaction_type = provider.get(ProviderKeys.PROVIDER_SES_TRANSACTION_TYPE)

        if provider_config_model.count_email_provider_config_in_merchant(merchant_id) >= 10:
            raise CustomError(self.lang.get(LANG.CONFIGURATION_EMAIL_LIMIT).get("message"))

        provider_config = provider_config_model.get_provider_config_by_name_in_all_merchant(name, provider_type)
        if provider_config:
            if provider_type == ProviderType.SES:
                raise CustomError(
                self.lang.get(LANG.AWS_CONFIG_EXISTS).get("message") % provider_name,
                    self.lang.get(LANG.AWS_CONFIG_EXISTS).get("code"),
                )
            raise CustomError(
                self.lang.get(LANG.EMAIL_CONFIG_EXISTS).get("message") % provider_name,
                self.lang.get(LANG.EMAIL_CONFIG_EXISTS).get("code"),
            )
        # function check domain đã tồn tại chưa
        domains = [domain.get(ProviderConfigKeys.DOMAIN) for domain in payload.get(ProviderConfigKeys.DOMAINS)]
        self.check_domain_exists(provider_type, name, domains, merchant_id)

        if provider_type == ProviderType.MOBIO_MAILER:  # mobio_mailer
            mobio_mailer_helper: MobioMailerHelper = MobioMailerHelper()
            payload_send = {
                CommonKeys.MERCHANT_ID: merchant_id,
                ProviderConfigKeys.DOMAIN: name,
            }

            token = provider.get(ProviderKeys.TOKEN)
            host = provider.get(ProviderKeys.HOST)

            data = mobio_mailer_helper.send_request_create_domain(host, token, merchant_id, payload_send)

            status = StatusChoice.WAIT_CONFIRM

            payload.update(
                {
                    ProviderConfigKeys.DNS_RECORDS: data.get(ProviderConfigKeys.DNS_RECORDS),
                    ProviderConfigKeys.STATUS: status,
                    "verify_count": 0,
                    "verify_time": get_time_now(),
                }
            )
        elif provider_type == ProviderType.SES:
            # Check list domains nếu tồn tại 2 domain trùng nhau => chặn
            self.check_domain_duplicate(payload.get(ProviderConfigKeys.DOMAINS))
            self.create_email_config_sync_nm(payload,
                                             connect_config_info,
                                             provider_ses_transaction_type,
                                             provider,
                                             merchant_id)

        else:  # TODO new provider
            pass

        connect_config_info_str = json.dumps(connect_config_info)

        data_encode = MobioAdminSDK().encrypt_values(
            merchant_id=merchant_id,
            module="MARKET_PLACE",
            field=ProviderConfigKeys.CONNECT_CONFIG_INFO,
            values=connect_config_info_str,
        )

        payload.update(
            {
                ProviderConfigKeys.CONNECT_CONFIG_INFO: data_encode.get("data").get(connect_config_info_str),
                ProviderConfigKeys.STATUS: status,
            }
        )

        inserted_id = provider_config_model.create_provider_config(payload, merchant_id, account_id)
        MobioLogging().debug(
            "{} :: {} :: inserted_id :: {}".format(self.__class__.__name__, func_name, str(inserted_id))
        )

        log_action_model.insert_log_action(
            merchant_id,
            account_id,
            str(inserted_id),
            "create",
            1,
            provider.get(ProviderKeys.SERVICE),
        )
        email_config = provider_config_model.get_detail_provider_config_by_id(
            merchant_id,
            str(inserted_id),
            fields_select={
                ProviderConfigKeys.CONNECT_CONFIG_INFO: 0,
                ProviderConfigKeys.LOWER_CASE_NAME: 0,
            },
        )
        email_config.update(
            {
                ProviderKeys.PROVIDER: provider.get(ProviderKeys.PROVIDER),
                ProviderKeys.PROVIDER_LINK_IMAGE: provider.get(ProviderKeys.PROVIDER_LINK_IMAGE),
                **GetInternalHost.get_detail_user_information_by_account_id(merchant_id, account_id),
            }
        )

        return {"data": self.json_encoder(email_config)}

    def create_email_config_with_sync(self):
        func_name = self.create_email_config_with_sync.__name__
        merchant_id = validate_merchant_header()

        payload = request.get_json()
        self.validate_sync_data(payload)
        account_id = get_param_value_temp("id")

        provider_config_model: ProviderConfigModel = ProviderConfigModel()

        connect_config_info = payload.get(ProviderConfigKeys.CONNECT_CONFIG_INFO)

        connect_config_info_str = json.dumps(connect_config_info)

        data_encode = MobioAdminSDK().encrypt_values(
            merchant_id=merchant_id,
            module="MARKET_PLACE",
            field=ProviderConfigKeys.CONNECT_CONFIG_INFO,
            values=connect_config_info_str,
        )

        payload.update({ProviderConfigKeys.CONNECT_CONFIG_INFO: data_encode.get("data").get(connect_config_info_str)})

        inserted_id = provider_config_model.create_provider_config(payload, merchant_id, account_id)
        MobioLogging().debug(
            "{} :: {} :: inserted_id :: {}".format(self.__class__.__name__, func_name, str(inserted_id))
        )
        return

    def update_status_email_config(self, email_config_id):
        func_name = self.update_status_email_config.__name__
        merchant_id = validate_merchant_header()
        account_id = get_param_value_temp("id")
        payload = request.get_json()
        self.validate_update_status_email_config(payload)

        log_action_model: LogActionModel = LogActionModel()
        provider_model: ProviderModel = ProviderModel()
        provider_config_model: ProviderConfigModel = ProviderConfigModel()
        mobio_mailer_helper: MobioMailerHelper = MobioMailerHelper()

        provider_config = provider_config_model.get_detail_provider_config_by_id(merchant_id, email_config_id)
        if not provider_config:
            raise InputNotFoundError()

        provider_type = provider_config.get(ProviderConfigKeys.PROVIDER_TYPE)
        domain = provider_config.get(ProviderConfigKeys.NAME)

        provider = provider_model.get_provider_by_code(provider_type, service=ServiceKeys.EMAIL)
        host = provider.get(ProviderKeys.HOST)
        token = provider.get(ProviderKeys.TOKEN)

        payload_send = {
            ProviderConfigKeys.DOMAIN: domain,
            CommonKeys.MERCHANT_ID: merchant_id,
            ProviderConfigKeys.RETRY: True,
        }

        # send request retry domain
        mobio_mailer_helper.send_request_create_domain(
            host=host, token=token, merchant_id=merchant_id, payload=payload_send
        )

        # update status key for job verify retry
        payload.update(
            {
                ProviderConfigKeys.VERIFY_COUNT: 0,
                ProviderConfigKeys.VERIFY_TIME: get_time_now(),
                ProviderConfigKeys.VERIFY_BY: account_id,
            }
        )

        matched_count = provider_config_model.update_provider_config_by_id(email_config_id, payload, account_id)
        MobioLogging().debug(
            "{} :: {} :: matched_count :: {}".format(self.__class__.__name__, func_name, matched_count)
        )
        log_action_model.insert_log_action(merchant_id, account_id, email_config_id, "re-authenticate", 1, "email")
        return

    def get_log_email_config(self, email_config_id):
        merchant_id = validate_merchant_header()
        account_id = get_param_value_temp("id")

        config_type = request.args.get("type", "").strip().lower()
        order = int(request.args.get("order", -1))
        sort = request.args.get("sort", CommonKeys.CREATED_TIME)
        page = int(request.args.get("page", 1))
        per_page = int(request.args.get("per_page", 10))

        log_action_model: LogActionModel = LogActionModel()
        sms_config_controller: SmsConfigController = SmsConfigController()

        log_query = {
            CommonKeys.MERCHANT_ID: merchant_id,
            LogActionKeys.TYPE: config_type,
            LogActionKeys.PROVIDER_CONFIG_ID: email_config_id,
        }

        logs, paging = log_action_model.get_logs(log_query, order, sort, page, per_page)
        results = [
            self.json_encoder(log)
            for log in sms_config_controller.add_information_user_in_logs(logs, merchant_id, account_id)
        ]
        return {"data": results, "paging": paging}

    def export_records(self, email_config_id):
        func_name = self.export_records.__name__
        merchant_id = validate_merchant_header()
        provider_config_model: ProviderConfigModel = ProviderConfigModel()

        email_config = provider_config_model.get_detail_provider_config_by_id(
            merchant_id,
            email_config_id,
        )
        if not email_config:
            raise CustomError(self.lang.get(LANG.NOT_FOUND).get("message"), 404)

        name = email_config.get(ProviderConfigKeys.NAME)
        dns_records = email_config.get(ProviderConfigKeys.DNS_RECORDS)

        path, _ = MobioMediaSDK().create_filepath(merchant_id, "{}_dns_records.xlsx".format(email_config_id), "upload")
        MobioLogging().debug("{} :: {} :: path :: {}".format(self.__class__.__name__, func_name, str(path)))

        result_exports = []
        if dns_records:
            for dns_record in dns_records:
                result_exports.append(
                    {
                        ProviderConfigKeys.RECORD_NAME: remove_csv_injection_chars(
                            dns_record.get(ProviderConfigKeys.RECORD_NAME)
                        ),
                        ProviderConfigKeys.TYPE: remove_csv_injection_chars(dns_record.get(ProviderConfigKeys.TYPE)),
                        ProviderConfigKeys.RECORD_VALUE: remove_csv_injection_chars(
                            dns_record.get(ProviderConfigKeys.RECORD_VALUE)
                        ),
                    }
                )

        path, _ = MobioMediaSDK().create_filepath(merchant_id, "{}_dns_records.xlsx".format(email_config_id), "upload")
        MobioLogging().debug("{} :: {} :: path :: {}".format(self.__class__.__name__, func_name, str(path)))

        fieldnames = [
            ProviderConfigKeys.RECORD_NAME.capitalize(),
            ProviderConfigKeys.TYPE.capitalize(),
            ProviderConfigKeys.RECORD_VALUE.capitalize(),
        ]

        # Tạo workbook và worksheet
        workbook = xlsxwriter.Workbook(path)
        worksheet = workbook.add_worksheet()

        # Định dạng tiêu đề (tuỳ chọn)
        header_format = workbook.add_format({"bold": True, "bg_color": "#F9DA04", "border": 1})

        # Ghi tiêu đề cột vào hàng đầu tiên
        for col_num, header in enumerate(fieldnames):
            worksheet.write(0, col_num, header, header_format)

        # Ghi dữ liệu vào các hàng tiếp theo
        for row_num, data in enumerate(result_exports, start=1):
            for col_num, key in enumerate(fieldnames):
                worksheet.write(row_num, col_num, data.get(key.lower()))

        # Đóng workbook
        workbook.close()

        # Upload file Excel
        data_info = MobioMediaSDK().upload_with_kafka(
            merchant_id=merchant_id,
            file_path=path,
            filename=f"{name}.xlsx",
            expire=None,
        )

        return self.json_encoder({"data_info": data_info})

    def decrypt_values(self, email_config_id):
        email_config_controller: SmsConfigController = SmsConfigController()
        return email_config_controller.decrypt_values(email_config_id)

    def get_email_configs(self):
        merchant_id = validate_merchant_header()

        search = request.args.get("search", "")
        order = int(request.args.get("order", -1))
        sort = request.args.get("sort", CommonKeys.UPDATED_TIME)
        page = int(request.args.get("page", 1))
        per_page = int(request.args.get("per_page", 10))

        provider_model: ProviderModel = ProviderModel()
        provider_config_model: ProviderConfigModel = ProviderConfigModel()

        providers = provider_model.get_providers(service="email")
        provider_types = [provider.get(ProviderKeys.PROVIDER_TYPE) for provider in providers]

        fields_select = {ProviderConfigKeys.LOWER_CASE_NAME: 0}

        email_configs, paging = provider_config_model.get_provider_configs(
            merchant_id,
            search,
            provider_types,
            order,
            sort,
            page,
            per_page,
            fields_select,
        )

        for config in email_configs:
            created_by = config.get(CommonKeys.CREATED_BY)
            provider_type = config.get(ProviderConfigKeys.PROVIDER_TYPE)

            provider_of_config = list(
                filter(
                    lambda x: x.get(ProviderKeys.PROVIDER_TYPE) == provider_type,
                    providers,
                )
            )[0]

            connect_config_info = self.add_value_in_connect_config_info(provider_of_config, config, merchant_id)
            provider = list(
                filter(
                    lambda x: x.get(ProviderKeys.PROVIDER_TYPE) == provider_type,
                    providers,
                )
            )[0]
            config.update(
                {
                    ProviderKeys.PROVIDER: provider.get(ProviderKeys.PROVIDER),
                    ProviderKeys.PROVIDER_LINK_IMAGE: provider.get(ProviderKeys.PROVIDER_LINK_IMAGE),
                    ProviderKeys.CONNECT_CONFIG_INFO: copy.deepcopy(connect_config_info),
                    **GetInternalHost.get_detail_user_information_by_account_id(merchant_id, created_by),
                }
            )
            self.json_encoder(config)

        return {
            "data": self.json_encoder(email_configs),
            "paging": paging,
        }

    def add_email(self, email_config_id):
        func_name = self.add_email.__name__
        merchant_id = validate_merchant_header()
        account_id = get_param_value_temp("id")

        payload = request.get_json()
        self.validate_add_email(payload)

        log_action_model: LogActionModel = LogActionModel()
        provider_config_model: ProviderConfigModel = ProviderConfigModel()
        provider_config = provider_config_model.get_detail_provider_config_by_id(merchant_id, email_config_id)
        if not provider_config:
            raise CustomError(self.lang.get(LANG.NOT_FOUND).get("message"), 404)

        domain = provider_config.get(ProviderConfigKeys.NAME)
        emails = provider_config.get(ProviderConfigKeys.EMAILS, [])

        email_insert = "{}@{}".format(payload.get("email"), domain)
        if email_insert in emails:
            raise CustomError("Email already exists", 400)

        emails.append(email_insert)
        data_update = {ProviderConfigKeys.EMAILS: emails}

        matched_count = provider_config_model.update_provider_config_by_id(email_config_id, data_update, account_id)
        MobioLogging().debug(
            "{} :: {} :: matched_count :: {}".format(self.__class__.__name__, func_name, matched_count)
        )
        log_action_model.insert_log_action(merchant_id, account_id, email_config_id, "add_email", 1, "email")
        return {"data": self.json_encoder({"email": email_insert, "modules": []})}

    def detail_email_config(self, email_config_id):
        merchant_id = validate_merchant_header()

        provider_model: ProviderModel = ProviderModel()
        provider_config_model: ProviderConfigModel = ProviderConfigModel()
        email_config = provider_config_model.get_detail_provider_config_by_id(
            merchant_id,
            email_config_id,
            fields_select={
                ProviderConfigKeys.LOWER_CASE_NAME: 0,
            },
        )
        if not email_config:
            raise CustomError(self.lang.get(LANG.NOT_FOUND).get("message"), 404)

        created_by = email_config.get(CommonKeys.CREATED_BY)
        name = email_config.get(ProviderConfigKeys.NAME)
        emails = email_config.get(ProviderConfigKeys.EMAILS, [])

        emails_detail = self.add_modules_used_in_email(merchant_id, name, emails) if emails else []
        provider = provider_model.get_provider_by_code(email_config.get(ProviderConfigKeys.PROVIDER_TYPE), "email")
        connect_config_info = self.add_value_in_connect_config_info(provider, email_config, merchant_id)

        email_config.update(
            {
                ProviderKeys.PROVIDER: provider.get(ProviderKeys.PROVIDER),
                ProviderKeys.PROVIDER_LINK_IMAGE: provider.get(ProviderKeys.PROVIDER_LINK_IMAGE),
                ProviderKeys.CONNECT_CONFIG_INFO: connect_config_info,
                ProviderConfigKeys.EMAILS: emails_detail,
                **GetInternalHost.get_detail_user_information_by_account_id(merchant_id, created_by),
            }
        )
        return {"data": self.json_encoder(email_config)}

    def delete_email_config(self, email_config_id):
        func_name = self.add_email.__name__
        merchant_id = validate_merchant_header()
        account_id = get_param_value_temp("id")

        # Xoá khi không có jb, nm nào đang chạy, call sang các bên tuỳ từng cấu hình
        provider_model: ProviderModel = ProviderModel()
        jb_helper: JourneyBuilderHelper = JourneyBuilderHelper()
        notify_management_helper: NotifyManagementHelper = NotifyManagementHelper()
        wf_helper: WorkflowHelper = WorkflowHelper()
        provider_config_model: ProviderConfigModel = ProviderConfigModel()

        provider_config = provider_config_model.get_detail_provider_config_by_id(merchant_id, email_config_id)
        if not provider_config:
            raise InputNotFoundError()

        domain = provider_config.get(ProviderConfigKeys.NAME)
        provider_type = provider_config.get(ProviderConfigKeys.PROVIDER_TYPE)
        status = provider_config.get(ProviderConfigKeys.STATUS)
        connect_config_info_str = provider_config.get(ProviderConfigKeys.CONNECT_CONFIG_INFO)
        config_type = provider_config.get(ProviderConfigKeys.TYPE)
        provider_ses_transaction_type = provider_config.get(ProviderKeys.PROVIDER_SES_TRANSACTION_TYPE)

        provider = provider_model.get_provider_by_code(provider_type, "email")
        if status == 1 and provider_type != ProviderType.SES:
            payload_send_get_campaign_in_jb = {"channel": "EMAIL", "sender": domain}
            campaigns_in_jb = jb_helper.get_campaigns(merchant_id, payload=payload_send_get_campaign_in_jb)
            if campaigns_in_jb:
                raise CustomError(self.lang.get(LANG.INTERNAL_SERVER_ERROR).get("message"), 409)

            campaigns_in_noti = notify_management_helper.get_modules_by_domain(merchant_id, domain)
            if campaigns_in_noti:
                raise CustomError(self.lang.get(LANG.INTERNAL_SERVER_ERROR).get("message"), 409)

            # [TODO] check in worflow
            workflows_used_domain = wf_helper.get_wf_by_domain(merchant_id, domain)
            if workflows_used_domain:
                raise CustomError(self.lang.get(LANG.INTERNAL_SERVER_ERROR).get("message"), 409)

            data_decode = MobioAdminSDK().decrypt_values(
                merchant_id=merchant_id,
                module="MARKET_PLACE",
                field=ProviderConfigKeys.CONNECT_CONFIG_INFO,
                values=connect_config_info_str,
            )

            payload_send_update_config_in_jb = self.get_send_payload(
                provider,
                json.loads(data_decode.get("data").get(connect_config_info_str)),
                domain,
                2,
            )

            if provider_type == ProviderType.MOBIO_MAILER:
                mobio_mailer_config_info = provider.get(ProviderKeys.CONNECT_CONFIG_INFO)
                for config in mobio_mailer_config_info:
                    payload_send_update_config_in_jb[config.get(ProviderConfigKeys.ConfigInfoKeys.FIELD)] = config.get(
                        ProviderConfigKeys.ConfigInfoKeys.KEY
                    )
                notify_management_helper.upsert_provider_config(merchant_id, payload_send_update_config_in_jb)

        if provider_type == ProviderType.MOBIO_MAILER:
            mobio_mailer_helper: MobioMailerHelper = MobioMailerHelper()
            payload_send_delete_config_for_mobio_mailer = {
                CommonKeys.MERCHANT_ID: merchant_id,
                ProviderConfigKeys.DOMAIN: domain,
            }

            token = provider.get(ProviderKeys.TOKEN)
            host = provider.get(ProviderKeys.HOST)

            mobio_mailer_helper.send_request_delete_domain(
                host, token, merchant_id, payload_send_delete_config_for_mobio_mailer
            )

        if provider_type == ProviderType.SES:
            if not provider_config.get(ProviderConfigKeys.DOMAINS):
                provider_config[ProviderConfigKeys.DOMAINS] = [{
                    ProviderConfigKeys.DOMAIN: provider_config.get(ProviderConfigKeys.NAME),
                    ProviderConfigKeys.STATUS: provider_config.get(ProviderConfigKeys.STATUS),
                    ProviderConfigKeys.TYPE: provider_config.get(ProviderConfigKeys.TYPE),
                    ProviderConfigKeys.DESCRIPTION: provider_config.get(ProviderConfigKeys.DESCRIPTION)
                }]
            for obj_domain in provider_config.get(ProviderConfigKeys.DOMAINS):
                domain_delete = obj_domain.get(ProviderConfigKeys.DOMAIN)
                config_type_domain = obj_domain.get(ProviderConfigKeys.TYPE)

                self.check_domain_is_active(merchant_id=merchant_id,
                                            domain=domain_delete,
                                            config_type=config_type_domain,
                                            provider_config=provider_config,
                                            provider=provider)

        provider_config = provider_config_model.delete_provider_config(
            merchant_id, email_config_id
        )
        return

    def update_email_config(self, email_config_id):
        func_name = self.add_email.__name__
        merchant_id = validate_merchant_header()
        account_id = get_param_value_temp("id")

        payload = request.get_json()
        self.validate_update_email_config(payload)

        log_action_model: LogActionModel = LogActionModel()
        provider_model: ProviderModel = ProviderModel()
        provider_config_model: ProviderConfigModel = ProviderConfigModel()
        notify_management_helper: NotifyManagementHelper = NotifyManagementHelper()

        # get provider config
        provider_config = provider_config_model.get_detail_provider_config_by_id(merchant_id, email_config_id)
        if not provider_config:
            raise InputNotFoundError()

        domain = provider_config.get(ProviderConfigKeys.NAME)
        provider_type = provider_config.get(ProviderConfigKeys.PROVIDER_TYPE)
        config_type = provider_config.get(ProviderConfigKeys.TYPE)
        connect_config_info_str = provider_config.get(ProviderConfigKeys.CONNECT_CONFIG_INFO)

        if provider_type == ProviderType.SES:

            data_decode = MobioAdminSDK().decrypt_values(
                merchant_id=merchant_id,
                module="MARKET_PLACE",
                field=ProviderConfigKeys.CONNECT_CONFIG_INFO,
                values=connect_config_info_str,
            )

            connect_config_info = json.loads(data_decode.get("data").get(connect_config_info_str))

            # get provider
            provider = provider_model.get_provider_by_code(provider_type, "email")
            config_type_update = payload.get(ProviderConfigKeys.TYPE)
            provider_ses_transaction_type = provider.get(ProviderKeys.PROVIDER_SES_TRANSACTION_TYPE)

            if ConfigTypeChoices.NOTIFICATION in config_type_update:
                payload.update({ProviderConfigKeys.PROVIDER_SES_TRANSACTION_TYPE: provider_ses_transaction_type})
            else:
                payload.update({ProviderConfigKeys.PROVIDER_SES_TRANSACTION_TYPE: None})

            code_mapping = {
                ConfigTypeChoices.MESSAGE: 203,
                ConfigTypeChoices.NOTIFICATION: 204,
            }

            if (
                len(config_type_update) == 1 and config_type_update != config_type
            ):  # Một trong hai cấu hình được bật thì phải tắt cấu hình kia đi
                payload_send = self.get_send_payload(provider, connect_config_info, domain, 1)
                provider_code_active = code_mapping[config_type_update[0]]

                payload_send.update(
                    (
                        {
                            ProviderConfigKeys.PROVIDER_TYPE: provider_code_active,
                        }
                    )
                )
                del code_mapping[config_type_update[0]]
                (provider_code_deactivate,) = code_mapping.values()

                MobioLogging().info("Upsert_email_config :: active_provider_config :: payload_send %s" % payload_send)

                # update config ses or ses transaction
                notify_management_helper.upsert_provider_config(merchant_id, payload_send)

                (provider_code_deactivate,) = code_mapping.values()
                payload_send.update(
                    (
                        {
                            ProviderConfigKeys.PROVIDER_TYPE: provider_code_deactivate,
                            ProviderConfigKeys.STATUS: 2,
                        }
                    )
                )
                MobioLogging().info(
                    "Upsert_email_config :: deactivate_provider_config :: payload_send %s" % payload_send
                )
                # delete config ses or ses transaction
                notify_management_helper.upsert_provider_config(merchant_id, payload_send)

            elif len(config_type_update) == 2 and set(config_type_update).difference(
                config_type
            ):  # bật cả hai cấu hình lên
                payload_send = self.get_send_payload(provider, connect_config_info, domain, 1)
                for key, value in code_mapping.items():
                    payload_send.update(
                        (
                            {
                                ProviderConfigKeys.PROVIDER_TYPE: value,
                            }
                        )
                    )
                    notify_management_helper.upsert_provider_config(merchant_id, payload_send)

        matched_count = provider_config_model.update_provider_config_by_id(email_config_id, payload, account_id)
        log_action_model.insert_log_action(merchant_id, account_id, email_config_id, "update", 1, "email")
        MobioLogging().debug(
            "{} :: {} :: matched_count :: {}".format(self.__class__.__name__, func_name, matched_count)
        )

        return

    def delete_email(self, email_config_id, email):
        func_name = self.delete_email.__name__
        merchant_id = validate_merchant_header()
        account_id = get_param_value_temp("id")

        log_action_model: LogActionModel = LogActionModel()
        provider_config_model: ProviderConfigModel = ProviderConfigModel()
        provider_config = provider_config_model.get_detail_provider_config_by_id(merchant_id, email_config_id)
        if not provider_config:
            raise CustomError(self.lang.get(LANG.NOT_FOUND).get("message"), 404)

        emails = provider_config.get(ProviderConfigKeys.EMAILS, [])
        try:
            emails.remove(email)
        except:
            raise CustomError(self.lang.get(LANG.NOT_FOUND).get("message"), 404)

        data_update = {ProviderConfigKeys.EMAILS: emails}

        matched_count = provider_config_model.update_provider_config_by_id(email_config_id, data_update, account_id)
        MobioLogging().debug(
            "{} :: {} :: matched_count :: {}".format(self.__class__.__name__, func_name, matched_count)
        )

        log_action_model.insert_log_action(merchant_id, account_id, email_config_id, "delete_email", 1, "email")
        return

    def send_test_email(self, email_config_id):
        merchant_id = validate_merchant_header()
        account_id = get_param_value_temp("id")
        lang = request.args.get("lang", "vi")

        payload = request.get_json()
        self.validate_send_test_email(payload)

        provider_config_model: ProviderConfigModel = ProviderConfigModel()
        nm_helper: NotifyManagementHelper = NotifyManagementHelper()

        provider_config = provider_config_model.get_detail_provider_config_by_id(merchant_id, email_config_id)
        if not provider_config:
            raise CustomError(self.lang.get(LANG.NOT_FOUND).get("message"), 404)

        config_type = provider_config.get(ProviderConfigKeys.TYPE)

        send_from = payload.get("from")
        send_to = payload.get("to")
        sender_name = payload.get("title")
        sender_type = "BROAD_CAST"

        if ConfigTypeChoices.NOTIFICATION in config_type:
            sender_type = "NOTIFICATION"

        domain = provider_config.get(ProviderConfigKeys.NAME)
        host_config = GetInternalHost.get_internal_host_by_merchant(
            merchant_id, key=KeyHostService.MARKET_PLACE_SERVICE_HOST
        )
        url_webhook = f"{host_config}/api/v1.0/webhook/email-configs/response-send-email?merchant_id={merchant_id}&account_id={account_id}&email_config_id={email_config_id}"
        # TODO: send test email to nm

        info_file = open("resources/lang/message_email.json")
        info_send = json.load(info_file)
        info_send_success = info_send.get("success")
        content = info_send_success[lang].get("content")
        subject = info_send_success[lang].get("subject")

        payload_send = {
            "from": {
                "source": "market_place",
                "merchant_id": merchant_id,
                "sender_id": send_from,
                "sender_name": sender_name,
                "sender_domain": domain,
                "sender_type": sender_type,
                "anti_spam": False,
                "push_event": True,
            },
            "to": [
                {
                    "push_webhook": url_webhook,
                    "to": [{"email": send_to}],
                    "body": {"subject": subject, "content": content},
                }
            ],
        }
        MobioLogging().debug(
            "{} :: {} :: send_mail :: payload_send :: {}".format(
                self.__class__.__name__, self.send_test_email.__name__, payload_send
            )
        )
        nm_helper.send_email_test(merchant_id, payload_send)
        return

    def get_emails(self, email_config_id):
        merchant_id = validate_merchant_header()
        account_id = get_param_value_temp("id")

        provider_config_model: ProviderConfigModel = ProviderConfigModel()
        provider_config = provider_config_model.get_detail_provider_config_by_id(merchant_id, email_config_id)
        if not provider_config:
            raise CustomError(self.lang.get(LANG.NOT_FOUND).get("message"), 404)

        emails = provider_config.get(ProviderConfigKeys.EMAILS, [])
        results = [{"email": email} for email in emails]
        return {"data": self.json_encoder(results)}

    def response_send_email(self):

        merchant_id = request.args.get("merchant_id")
        account_id = request.args.get("account_id")
        sms_config_id = request.args.get("email_config_id")
        status = request.args.get("status")

        log_action_model: LogActionModel = LogActionModel()

        MobioLogging().info(
            "{} :: {} :: merchant_id :: {}:: account_id :: {} :: email_config_id :: {} :: status :: {}".format(
                self.__class__.__name__,
                self.response_send_email.__name__,
                merchant_id,
                account_id,
                sms_config_id,
                status,
            )
        )
        status_log = 0
        if status and int(status) == 200:
            status_log = 1

        log_action_model.insert_log_action(merchant_id, account_id, sms_config_id, "send_mail", status_log, "email")
        return

    def wf_by_domain(self):
        merchant_id = validate_merchant_header()
        domain = request.args.get("domain")

        data = WorkflowHelper.get_wf_by_domain(merchant_id, domain)
        return self.json_encoder({"data": data})


    def get_list_region_aws(self):
        data_region = CachingHelpers().get_value_by_key_not_hash(key="region")
        if data_region:
            return {"data": data_region.get("region_data")}
        data_region = dict(RegionSesModel().get_region_ses_config())
        if not data_region:
            return {"data": []}
        CachingHelpers().set_value_by_key_not_hash(
            key="region",
            value=data_region,
            expiration=365 * 24 * 60 * 60,
        )
        return {"data": data_region.get("region_data")}


    def create_email_config_sync_nm(self,
                                    payload,
                                    connect_config_info,
                                    provider_ses_transaction_type,
                                    provider,
                                    merchant_id):
        domains = payload.get(ProviderConfigKeys.DOMAINS)
        if not domains:
            raise CustomError(
                self.lang.get(LANG.DOMAINS_NOT_FOUND).get("message"), 413
            )

        for domain in domains:
            config_type = domain.get(ProviderConfigKeys.TYPE)
            if ConfigTypeChoices.NOTIFICATION in config_type:
                payload.update(
                    {
                        ProviderConfigKeys.PROVIDER_SES_TRANSACTION_TYPE: provider_ses_transaction_type
                    }
                )

            # Bỏ type trong body truyền sang nm
            connect_config_info_copy = connect_config_info.copy()
            payload_ses = self.get_send_payload(
                provider,
                connect_config_info_copy,
                domain.get(ProviderConfigKeys.DOMAIN),
                1,
            )
            payload_ses_transaction = {}

            if ConfigTypeChoices.NOTIFICATION in config_type and len(config_type) == 1:
                payload_ses_transaction = payload_ses.copy()
                payload_ses_transaction.update(
                    {ProviderConfigKeys.PROVIDER_TYPE: provider_ses_transaction_type}
                )
                payload_ses = {}
            elif (
                    ConfigTypeChoices.NOTIFICATION in config_type and len(config_type) == 2
            ):
                payload_ses_transaction = payload_ses.copy()
                payload_ses_transaction.update(
                    {ProviderConfigKeys.PROVIDER_TYPE: provider_ses_transaction_type}
                )

            if payload_ses:
                HandleQueue.push_message_upsert_email_nm(payload_ses, merchant_id)
            if payload_ses_transaction:
                HandleQueue.push_message_upsert_email_nm(payload_ses_transaction, merchant_id)


    def update_email_config_aws(self, email_config_id):
        func_name = self.update_email_config_aws.__name__
        merchant_id = validate_merchant_header()
        account_id = get_param_value_temp("id")

        payload = request.get_json()
        self.validate_update_email_config_aws(payload)

        log_action_model: LogActionModel = LogActionModel()
        provider_model: ProviderModel = ProviderModel()
        provider_config_model: ProviderConfigModel = ProviderConfigModel()
        notify_management_helper: NotifyManagementHelper = NotifyManagementHelper()

        # get provider config
        provider_config = provider_config_model.get_detail_provider_config_by_id(
            merchant_id, email_config_id
        )
        if not provider_config:
            raise InputNotFoundError()

        provider_type = provider_config.get(ProviderConfigKeys.PROVIDER_TYPE)
        # config_type = provider_config.get(ProviderConfigKeys.TYPE)
        connect_config_info_str = provider_config.get(
            ProviderConfigKeys.CONNECT_CONFIG_INFO
        )

        if provider_type == ProviderType.SES:

            data_decode = MobioAdminSDK().decrypt_values(
                merchant_id=merchant_id,
                module="MARKET_PLACE",
                field=ProviderConfigKeys.CONNECT_CONFIG_INFO,
                values=connect_config_info_str,
            )
            connect_config_info = json.loads(
                data_decode.get("data").get(connect_config_info_str)
            )

            # get provider
            provider = provider_model.get_provider_by_code(provider_type, "email")
            provider_ses_transaction_type = provider.get(
                ProviderKeys.PROVIDER_SES_TRANSACTION_TYPE
            )
            domains = provider_config.get(ProviderConfigKeys.DOMAINS)
            # Lấy danh sách những domain nào thêm mới, update => call upsert sang bên nm
            list_domain_create_nm, list_domain_update_nm, obj_domains_config = self.check_domain_update_sync_nm(domains_update=payload.get(ProviderConfigKeys.DOMAINS),
                                                                                                                domains=domains)
            if list_domain_create_nm:
                # Check TH những domain nào được thêm mới => check trùng với những domain đã tồn tại trong hệ thống
                if list_domain_create_nm:
                    self.check_domain_exists(ProviderType.SES, None, list_domain_create_nm, merchant_id)
                payload_create = {ProviderConfigKeys.DOMAINS: list_domain_create_nm}
                self.create_email_config_sync_nm(payload_create,
                                                 connect_config_info,
                                                 provider_ses_transaction_type,
                                                 provider,
                                                 merchant_id)
            if list_domain_update_nm:
                for domain_update in list_domain_update_nm:
                    connect_config_info_copy = connect_config_info.copy()
                    domain = domain_update.get(ProviderConfigKeys.DOMAIN)
                    config_type_update = domain_update.get(ProviderConfigKeys.TYPE)
                    config_type = obj_domains_config.get(domain_update.get(ProviderConfigKeys.DOMAIN)).get(ProviderConfigKeys.TYPE)

                    code_mapping = {
                        ConfigTypeChoices.MESSAGE: 203,
                        ConfigTypeChoices.NOTIFICATION: 204,
                    }

                    if (
                            len(config_type_update) == 1 and config_type_update != config_type
                    ):  # Một trong hai cấu hình được bật thì phải tắt cấu hình kia đi
                        payload_send = self.get_send_payload(
                            provider, connect_config_info_copy, domain, 1
                        )
                        provider_code_active = code_mapping[config_type_update[0]]

                        payload_send.update(
                            (
                                {
                                    ProviderConfigKeys.PROVIDER_TYPE: provider_code_active,
                                }
                            )
                        )
                        del code_mapping[config_type_update[0]]
                        (provider_code_deactivate,) = code_mapping.values()

                        MobioLogging().info(
                            "Upsert_email_config :: active_provider_config :: payload_send %s"
                            % payload_send
                        )

                        # update config ses or ses transaction
                        HandleQueue.push_message_upsert_email_nm(payload_send, merchant_id)

                        (provider_code_deactivate,) = code_mapping.values()
                        payload_send.update(
                            (
                                {
                                    ProviderConfigKeys.PROVIDER_TYPE: provider_code_deactivate,
                                    ProviderConfigKeys.STATUS: 2,
                                }
                            )
                        )
                        MobioLogging().info(
                            "Upsert_email_config :: deactivate_provider_config :: payload_send %s"
                            % payload_send
                        )
                        # delete config ses or ses transaction
                        HandleQueue.push_message_upsert_email_nm(payload_send, merchant_id)


                    elif len(config_type_update) == 2 and set(config_type_update).difference(
                            config_type
                    ):  # bật cả hai cấu hình lên
                        payload_send = self.get_send_payload(
                            provider, connect_config_info_copy, domain, 1
                        )
                        for key, value in code_mapping.items():
                            payload_send.update(
                                (
                                    {
                                        ProviderConfigKeys.PROVIDER_TYPE: value,
                                    }
                                )
                            )
                            HandleQueue.push_message_upsert_email_nm(payload_send, merchant_id)

            # Update provider_ses_transaction_type
            configs_type_domain = []
            for domain in payload.get(ProviderConfigKeys.DOMAINS):
                configs_type_domain.extend(domain.get(ProviderConfigKeys.TYPE))
            configs_type_domain = list(set(configs_type_domain))
            if ConfigTypeChoices.NOTIFICATION in configs_type_domain:
                payload.update(
                    {
                        ProviderConfigKeys.PROVIDER_SES_TRANSACTION_TYPE: provider_ses_transaction_type
                    }
                )
            else :
                payload.update({ProviderConfigKeys.PROVIDER_SES_TRANSACTION_TYPE: None})

        matched_count = provider_config_model.update_provider_config_by_id(
            email_config_id, payload, account_id
        )
        log_action_model.insert_log_action(
            merchant_id, account_id, email_config_id, "update", 1, "email"
        )
        MobioLogging().debug(
            "{} :: {} :: matched_count :: {}".format(
                self.__class__.__name__, func_name, matched_count
            )
        )
        return


    def check_domain_update_sync_nm(self, domains_update, domains):
        # Check domains_update nếu tồn tại 2 domain trùng nhau => chặn
        self.check_domain_duplicate(domains_update)

        list_domain_create, list_domain_update, list_domain_config = [], [], []
        obj_domains = {}
        for domain in domains:
            list_domain_config.append(domain.get(ProviderConfigKeys.DOMAIN))
            obj_domains.update({
                domain.get(ProviderConfigKeys.DOMAIN): domain
            })
        for domain_update in domains_update:
            # Check điều kiện thêm mới domain
            if domain_update.get(ProviderConfigKeys.DOMAIN) not in list_domain_config:
                list_domain_create.append(domain_update)

            # Check điều kiện Nếu domain update truyền lên != domain lưu trong cấu hình
            else:
                type_domain_config = obj_domains.get(domain_update.get(ProviderConfigKeys.DOMAIN)).get(ProviderConfigKeys.TYPE)
                if type_domain_config != domain_update.get(ProviderConfigKeys.TYPE):
                    list_domain_update.append(domain_update)

        return list_domain_create, list_domain_update, obj_domains


    def domain_change_status(self, email_config_id):
        func_name = self.domain_change_status.__name__
        merchant_id = validate_merchant_header()
        account_id = get_param_value_temp("id")
        payload = request.get_json()
        self.validate_change_status_domain(payload)

        provider_config = ProviderConfigModel().get_detail_provider_config_by_id(
            merchant_id, email_config_id
        )
        if not provider_config:
            raise InputNotFoundError()

        for domain in provider_config.get(ProviderConfigKeys.DOMAINS):
            if domain.get(ProviderConfigKeys.DOMAIN) == payload.get(ProviderConfigKeys.DOMAIN):
                domain[ProviderConfigKeys.STATUS] = payload.get(ProviderConfigKeys.STATUS)

        body_update = {
            "domains": provider_config.get(ProviderConfigKeys.DOMAINS)
        }
        matched_count = ProviderConfigModel().update_provider_config_by_id(
            email_config_id, body_update, account_id
        )
        LogActionModel().insert_log_action(
            merchant_id, account_id, email_config_id, "update", 1, "email"
        )
        MobioLogging().debug(
            "{} :: {} :: matched_count :: {}".format(
                self.__class__.__name__, func_name, matched_count
            )
        )
        return


    def delete_domain_config(self, email_config_id):
        func_name = self.delete_domain_config.__name__
        merchant_id = validate_merchant_header()
        account_id = get_param_value_temp("id")
        domain_delete = request.args.get("domain")

        provider_config = ProviderConfigModel().get_detail_provider_config_by_id(
            merchant_id, email_config_id
        )
        if not provider_config:
            raise InputNotFoundError()
        if len(provider_config.get(ProviderConfigKeys.DOMAINS)) <= 1:
            raise CustomError(self.lang.get(LANG.MULTIPLE_DOMAINS_REQUIRED_TO_REMOVE).get("message"))

        provider = ProviderModel().get_provider_by_code(provider_config.get(ProviderConfigKeys.PROVIDER_TYPE),
                                                        "email")

        data_update, config_type = [], []
        for domain in provider_config.get(ProviderConfigKeys.DOMAINS):
            if domain_delete == domain.get(ProviderConfigKeys.DOMAIN):
                config_type = domain.get(ProviderConfigKeys.TYPE)
                continue
            data_update.append(domain)

        # Check domain có đang được sử dụng không ?
        if config_type:
            self.check_domain_is_active(merchant_id, domain_delete, config_type, provider_config, provider)

        body_update = {ProviderConfigKeys.DOMAINS: data_update}
        matched_count = ProviderConfigModel().update_provider_config_by_id(
            email_config_id, body_update, account_id
        )
        LogActionModel().insert_log_action(
            merchant_id, account_id, email_config_id, "update", 1, "email"
        )
        MobioLogging().debug(
            "{} :: {} :: matched_count :: {}".format(
                self.__class__.__name__, func_name, matched_count
            )
        )
        return


    def check_domain_is_active(self, merchant_id, domain, config_type, provider_config, provider):
        connect_config_info = provider_config.get(ProviderConfigKeys.CONNECT_CONFIG_INFO)
        provider_type = provider_config.get(ProviderConfigKeys.PROVIDER_TYPE)
        provider_ses_transaction_type = provider_config.get(ProviderKeys.PROVIDER_SES_TRANSACTION_TYPE)

        payload_send_get_campaign_in_jb = {"channel": "EMAIL", "sender": domain}
        campaigns_in_jb = JourneyBuilderHelper().get_campaigns(
            merchant_id, payload=payload_send_get_campaign_in_jb
        )
        if campaigns_in_jb:
            raise CustomError(
                self.lang.get(LANG.INTERNAL_SERVER_ERROR).get("message"), 409
            )

        campaigns_in_noti = NotifyManagementHelper().get_modules_by_domain(
            merchant_id, domain
        )
        if campaigns_in_noti:
            raise CustomError(
                self.lang.get(LANG.INTERNAL_SERVER_ERROR).get("message"), 409
            )

        # [TODO] check in worflow
        workflows_used_domain = WorkflowHelper().get_wf_by_domain(merchant_id, domain)
        if workflows_used_domain:
            raise CustomError(
                self.lang.get(LANG.INTERNAL_SERVER_ERROR).get("message"), 409
            )

        data_decode = MobioAdminSDK().decrypt_values(
            merchant_id=merchant_id,
            module="MARKET_PLACE",
            field=ProviderConfigKeys.CONNECT_CONFIG_INFO,
            values=connect_config_info,
        )

        payload_send_update_config_in_jb = self.get_send_payload(
            provider,
            json.loads(data_decode.get("data").get(connect_config_info)),
            domain,
            2,
        )
        if provider_type == ProviderType.SES:
            if len(config_type) == 1 and provider_ses_transaction_type:
                payload_send_update_config_in_jb.update(
                    {
                        ProviderConfigKeys.PROVIDER_TYPE: provider_ses_transaction_type
                    }
                )
                HandleQueue.push_message_upsert_email_nm(payload_send_update_config_in_jb, merchant_id)

            elif len(config_type) == 1 and not provider_ses_transaction_type:
                HandleQueue.push_message_upsert_email_nm(payload_send_update_config_in_jb, merchant_id)

            elif len(config_type) == 2:
                HandleQueue.push_message_upsert_email_nm(payload_send_update_config_in_jb, merchant_id)

                payload_send_update_config_in_jb.update(
                    {
                        ProviderConfigKeys.PROVIDER_TYPE: provider_ses_transaction_type
                    }
                )
                HandleQueue.push_message_upsert_email_nm(payload_send_update_config_in_jb, merchant_id)


    def check_domain_duplicate(self, domains):
        seen, dups = set(), set()
        check_type_notification = []
        for d in domains:
            if d[ProviderConfigKeys.DOMAIN] in seen:
                dups.add(d[ProviderConfigKeys.DOMAIN])
            else:
                seen.add(d[ProviderConfigKeys.DOMAIN])

            # Check chỉ được cấu hình tối đa 1 domain cho mục đích thông báo
            if ConfigTypeChoices.NOTIFICATION in d[ProviderConfigKeys.TYPE]:
                check_type_notification.append(ConfigTypeChoices.NOTIFICATION)
        if dups:
            raise CustomError(self.lang.get(LANG.DOMAINS_DUPLICATE).get("message"), 413)
        if len(check_type_notification) > 1:
            raise CustomError(self.lang.get(LANG.MAX_DOMAIN_NOTIFICATION).get("message"), 413)


    def upsert_config_show_account_aws(self):
        merchant_id = validate_merchant_header()
        account_id = get_param_value_temp("id")
        payload = request.get_json()
        self.validate_show_config_account_ses(payload)
        payload.update({ConfigSesField.MERCHANT_ID: merchant_id})

        inserted_id = ConfigShowSesModel().upsert_account_ses_config(payload, account_id)
        if not inserted_id:
            raise CustomError(self.lang.get(LANG.UPSERT_ERROR).get("message"), 500)
        MobioLogging().info("EmailConfigController :: upsert_config_show_aws :: inserted_id :: %s" % inserted_id)
        return


    def check_domain_exists(self,
                            provider_type,
                            domain_mobio_mailer,
                            list_domain_aws_ses,
                            merchant_id
        ):
        if provider_type == ProviderType.MOBIO_MAILER:
            domain_list = [domain_mobio_mailer]
        elif provider_type == ProviderType.SES:
            domain_list = list_domain_aws_ses
        else:
            raise CustomError(self.lang.get(LANG.PROVIDER_NOT_FOUND).get("message"), 404)

        existing_domains = list(ProviderConfigModel().find({
            CommonKeys.MERCHANT_ID: merchant_id,
            "$or": [
                { ProviderConfigKeys.NAME: { "$in": domain_list } },
                { ProviderConfigKeys.DOMAINS: { "$elemMatch": { "domain": { "$in": domain_list } } } }
            ]
        }))
        if existing_domains:
            raise CustomError(self.lang.get(LANG.DOMAIN_EXIST).get("message"), 409)



    def get_list_domain_send_email_by_merchant(self):
        # Lấy danh sách domain đã active của merchant và đang được sử dụng để gửi email
        merchant_id = validate_merchant_header()
        provider_config_model: ProviderConfigModel = ProviderConfigModel()
        provider_configs = provider_config_model.get_email_provider_config_in_merchant(merchant_id)

        data_return = []
        for config in provider_configs:
            if (config.get(ProviderConfigKeys.PROVIDER_TYPE) == ProviderType.MOBIO_MAILER
                    and config.get(ProviderConfigKeys.STATUS)) and ConfigTypeChoices.MESSAGE in config.get(ProviderConfigKeys.TYPE):
                data_return.append({
                    ProviderConfigKeys.DOMAIN: config.get(ProviderConfigKeys.NAME)
                })
            elif config.get(ProviderConfigKeys.PROVIDER_TYPE) == ProviderType.SES:
                for domain in config.get(ProviderConfigKeys.DOMAINS):
                    if domain.get(ProviderConfigKeys.STATUS) and ConfigTypeChoices.MESSAGE in domain.get(ProviderConfigKeys.TYPE):
                        data_return.append({
                            ProviderConfigKeys.DOMAIN: domain.get(ProviderConfigKeys.DOMAIN)
                        })
        return {"data": data_return}
