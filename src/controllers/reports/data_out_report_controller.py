import datetime

from flask import request
from mobio.libs.validator import Each, In, InstanceOf
from mobio.sdks.base.common.mobio_exception import CustomError
from mobio.sdks.base.controllers import BaseController

from src.common import LANG, Common, ConstProcessRequestDataOut
from src.common.handle_headers import validate_merchant_header
from src.models.data_flow.process_data_out_model import ProcessDataOutModel


class DataOutReportSerializer:
    FORMAT_DATETIME = "%Y-%m-%dT%H:%M:%SZ"

    @staticmethod
    def _convert_line_per_sec_to_line_per_hour(value):
        """
        Convert lines per second to lines per hour.

        Args:
            value (float): Lines per second.

        Returns:
            float: Lines per hour.
        """
        return int(value * 3600)

    @staticmethod
    def build_response_detail_request_data_out(detail):
        result = {
            "id": str(detail["_id"]),
            "request_id": detail["request_id"],
            "account_id": detail["account_id"],
            "module_name": detail["module_name"],
            "area_code": detail["area_code"],
            "status_process": detail["status_process"],
            "current_process_row": detail.get("current_process_row"),
            "processing_speed": detail.get("processing_speed", 0),
            "extract_data_area": detail.get("extract_data_area", {}),
            "estimate_number_row": detail["estimate_number_row"],
            "actual_number_row": detail.get("actual_number_row"),
            "condition_filters": detail.get("condition_filters", {}),
            "search_filter": detail.get("search_filter"),
            "result_file": detail.get("result_file", {}),
        }

        # Datetime field
        for key in ProcessDataOutModel.LIST_FIELD_DATETIME:
            if key in detail:
                result.update({key: detail[key].strftime(DataOutReportSerializer.FORMAT_DATETIME)})

        return result

    @staticmethod
    def build_response_list_request_data_out(list_data):
        result = []
        for item in list_data:
            item_result = {
                "id": str(item["_id"]),
                "request_id": item["request_id"],
                "account_id": item["account_id"],
                "module_name": item["module_name"],
                "current_process_row": item.get("current_process_row"),
                "area_code": item["area_code"],
                "status_process": item["status_process"],
                "processing_speed": item.get("processing_speed", 0),
                "extract_data_area": item.get("extract_data_area", {}),
                "estimate_number_row": item["estimate_number_row"],
                "actual_number_row": item.get("actual_number_row"),
                "result_file": item.get("result_file", {}),
            }
            # Datetime field
            for key in ProcessDataOutModel.LIST_FIELD_DATETIME:
                if key in item:
                    item_result.update({key: item[key].strftime(DataOutReportSerializer.FORMAT_DATETIME)})
                else:
                    item_result.update({key: None})

            result.append(item_result)

        return result


class DataOutReportController(BaseController):

    def _validate_existed_request_data_out(self, merchant_id, source_key, process_data_out_id):
        request_data_out = ProcessDataOutModel().get_request_by_process_data_out_id(
            merchant_id=merchant_id,
            process_data_out_id=process_data_out_id,
            source_key=source_key,
        )
        if not request_data_out:
            raise CustomError(self.lang.get(LANG.NOT_FOUND).get("message"))

        # Response data
        return request_data_out

    def get_list_area_codes(self):
        result = list(ConstProcessRequestDataOut.AreaCodes.MAPPING.values())
        return result

    def get_detail_request_data_out(self, source_key, process_data_out_id):
        merchant_id = validate_merchant_header()

        # Validate existed request data out
        request_data_out = self._validate_existed_request_data_out(merchant_id, source_key, process_data_out_id)

        # Build response
        request_data_out = DataOutReportSerializer.build_response_detail_request_data_out(request_data_out)

        # Response data
        return {"data": request_data_out}

    def _validate_input_query_list_data_out(self, data_input):
        rules = {
            "status_process": [
                InstanceOf(list),
                Each(
                    {
                        InstanceOf(str),
                        In(ConstProcessRequestDataOut.StatusProcess.get_list_allow()),
                    },
                ),
            ],
            "account_request_ids": [
                InstanceOf(list),
                Each(
                    {InstanceOf(str)},
                ),
            ],
            "condition_request_time": [InstanceOf(dict)],
            "area_codes": [
                InstanceOf(list),
                Each(
                    {InstanceOf(str)},
                ),
            ],
        }
        self.abort_if_validate_error(rules, data_input)

    def get_list_request_data_out(self, source_key):
        merchant_id = validate_merchant_header()
        args = request.args

        # Get params
        per_page = args.get("per_page", default=20, type=int)
        token_in_query = args.get("after_token")
        merchant_id = validate_merchant_header()
        order_by = args.get("sort", default="request_time", type=str)
        order_type = args.get("order", default="desc", type=str)

        # Validate
        filter_list = request.json
        self._validate_input_query_list_data_out(filter_list)
        filter_list["source_key"] = source_key
        # Query db
        order_type = 1 if order_type == "asc" else -1
        list_request_data_out, after_token = ProcessDataOutModel().get_list_request_by_filter(
            merchant_id=merchant_id,
            filter=filter_list,
            sort_option=[(order_by, order_type)],
            after_token=token_in_query,
            per_page=per_page,
        )
        list_request_data_out = DataOutReportSerializer.build_response_list_request_data_out(list_request_data_out)

        # Response data
        return {
            "data": list_request_data_out,
            "paging": {"cursors": {"after_token": after_token, "before_token": token_in_query}},
        }

    def get_report_total_by_field(self, source_key, field):
        merchant_id = validate_merchant_header()

        # Validate filter
        filter_report = request.json
        self._validate_input_query_list_data_out(filter_report)
        filter_report["source_key"] = source_key

        default_order_key = "number"
        default_order_type = "desc"
        if field == "request_time":
            default_order_key = "date"
            default_order_type = "asc"

        args = request.args
        order_by = args.get("sort", default=default_order_key, type=str)
        order_type = args.get("order", default=default_order_type, type=str)

        # Query db
        order_type = 1 if order_type == "asc" else -1
        result = ProcessDataOutModel().get_total_request_group_by_field(
            merchant_id, filter_report, group_by=field, order_by=order_by, order_type=order_type
        )
        # Response data

        if field == "request_time" and "condition_request_time" in filter_report:
            start_time_str = filter_report["condition_request_time"].get("start_time")
            end_time_str = filter_report["condition_request_time"].get("end_time")

            if start_time_str and end_time_str:
                # Adding 7 hours for timezone conversion (UTC to UTC+7)
                start_time = datetime.datetime.strptime(
                    start_time_str, Common.DATE_TIME_FMT_WITHOUT_SECOND_PARSE
                ) + datetime.timedelta(hours=7)
                end_time = datetime.datetime.strptime(
                    end_time_str, Common.DATE_TIME_FMT_WITHOUT_SECOND_PARSE
                ) + datetime.timedelta(hours=7)

                # Create a lookup map for efficient data retrieval
                report_map = {item["date"]: item["number"] for item in result}

                # Generate the complete list of dates in the specified range
                date_list = []
                current_date = start_time
                while current_date.date() <= end_time.date():
                    date_list.append(current_date.strftime(Common.DATE_TIME_FMT_WITHOUT_SECOND_PARSE_WITHOUT_T))
                    current_date += datetime.timedelta(days=1)

                # Build the final list, filling in missing dates with 0
                result = [{"date": date_str, "number": report_map.get(date_str, 0)} for date_str in date_list]

                # The list is sorted ascending by date, reverse if descending was requested
                if order_type == -1:
                    result.reverse()

        return result

    def get_report_total_by_field_paging(self, source_key, field):
        merchant_id = validate_merchant_header()

        # Validate filter
        filter_report = request.json
        self._validate_input_query_list_data_out(filter_report)
        filter_report["source_key"] = source_key

        args = request.args
        # Paging
        per_page = args.get("per_page", default=20, type=int)
        token_in_query = args.get("after_token")

        # Order
        default_order_key = "number"
        default_order_type = "desc"
        if field == "request_time":
            default_order_key = "date"
            default_order_type = "asc"
        order_by = args.get("sort", default=default_order_key, type=str)
        order_type = args.get("order", default=default_order_type, type=str)

        # Query db
        order_type = 1 if order_type == "asc" else -1
        result, next_paging = ProcessDataOutModel().get_total_request_group_by_field_paging(
            merchant_id,
            filter_report,
            group_by=field,
            order_by=order_by,
            order_type=order_type,
            after_token=token_in_query,
            per_page=per_page,
        )
        # Response data
        return {"data": result, "paging": next_paging}
