#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 30/05/2024
"""


import subprocess
from collections import defaultdict
from datetime import datetime, timedelta

from flask import request
from mobio.libs.logging import MobioLogging
from mobio.libs.validator import In, InstanceOf, Length, Required
from mobio.sdks.base.common.mobio_exception import CustomError
from mobio.sdks.base.controllers import BaseController

from src.common import LANG
from src.common.data_flow_constant import (
    ConstantParamReportDataFlow,
    ConstantParamTimeReport,
    ConstantScheduleType,
    ConstantStatusSyncData,
    ConstantTypeReport,
)
from src.common.handle_headers import validate_merchant_header
from src.common.utils import (
    convert_string_datetime_to_datetime,
    convert_string_datetime_to_timezone,
)
from src.models.data_flow.config_connectors_model import ConfigConnectorsModel
from src.models.data_flow.object_handle_model import SettingObjectHandleModel
from src.models.data_flow.session_model import ConfigSessionModel, ConstantSessionModel
from src.models.reports.mongodb.daily_reports_model import DailyReportsModel
from src.models.reports.olap.dataflow.data_flow_model import DataFlowDialect


class DataFlowReportController(BaseController):
    FROM_FORMAT_DATETIME = "%Y-%m-%dT%H:%MZ"
    FROM_FORMAT_DATETIME_WITH_SECOND = "%Y-%m-%dT%H:%M:%SZ"
    TO_FORMAT_DATETIME = "%Y-%m-%d %H:%M:%S"

    @classmethod
    def __build_mapping_state(cls, states):
        mapping_state = {
            "consume": "wait_process",
            "processing": "processing",
            "processed": "done",
            "failed": "fail",
        }
        reverse_mapping_state = {v: k for k, v in mapping_state.items()}

        return {reverse_mapping_state.get(v): v for v in states.split(",")} if states else reverse_mapping_state

    def _validate_connector_get_report(self, merchant_id, connector_id, type_report):
        detail_connector = ConfigConnectorsModel().detail_connector_by_id(merchant_id, connector_id)
        if not detail_connector:
            raise CustomError(self.lang.get(LANG.NOT_FOUND).get("message"))
        return detail_connector

    def _validate_snapshot_total_sessions(self, data_validate):
        rules = {
            "start_time": [Required, InstanceOf(str), Length(1)],
            "end_time": [Required, InstanceOf(str), Length(1)],
            "status_sync": [],
        }
        self.abort_if_validate_error(rules, data_validate)

    def snapshot_total_sessions_by_table_query_daily_reports(self, connector_id):
        merchant_id = validate_merchant_header()

        detail_connector = self._validate_connector_get_report(merchant_id, connector_id, ConstantTypeReport.SNAPSHOT)
        start_time = request.args.get("start_time")
        end_time = request.args.get("end_time")
        status_sync = request.args.get("status_sync", "")

        if not start_time:
            start_time = detail_connector.get("created_time").strftime(self.FROM_FORMAT_DATETIME)

        data_validate = {
            "start_time": start_time,
            "end_time": end_time,
            "status_sync": status_sync,
        }
        MobioLogging().info("snapshot_total_sessions :: data_validate :: {}".format(data_validate))

        self._validate_snapshot_total_sessions(data_validate)

        mapping_state = self.__build_mapping_state(states=status_sync)

        MobioLogging().info("snapshot_total_sessions :: mapping_state :: {}".format(mapping_state))
        lst_consume_status = self._get_consume_status_by_status_sync(status_sync)
        start_time = convert_string_datetime_to_timezone(start_time, self.FROM_FORMAT_DATETIME)
        end_time = convert_string_datetime_to_timezone(end_time, self.FROM_FORMAT_DATETIME)

        agg_query = [
            {
                "$match": {
                    "connector_id": int(connector_id),
                    "process_status": {"$in": lst_consume_status},
                    "$and": [
                        {"start_time": {"$gte": start_time}},
                        {"start_time": {"$lte": end_time}},
                    ],
                    "mode": "batch",
                }
            },
            {
                "$group": {
                    "_id": "$process_status",
                    "count": {"$sum": 1},
                },
            },
            {
                "$group": {
                    "_id": None,
                    "statusCounts": {
                        "$push": {
                            "k": "$_id",
                            "v": "$count",
                        },
                    },
                },
            },
        ]

        is_streaming_to_batch = DailyReportsModel().check_mode_streaming_to_batch(connector_id)
        result = {
            "total_wait_process": 0,
            "total_processing": 0,
            "total_process_done": 1 if is_streaming_to_batch else 0,
            "total_process_fail": 0,
            "total_process_stop": 0,
        }

        total_sessions = DailyReportsModel().aggregate(agg_query)
        total_sessions = [*total_sessions]
        if total_sessions:
            for total_session in total_sessions:
                status_counts = total_session.get("statusCounts", [])
                for status_count in status_counts:
                    session_status = status_count.get("k")
                    count = status_count.get("v")
                    if session_status in [ConstantStatusSyncData.FINISHED]:
                        result["total_process_done"] += count
                    elif session_status in ConstantStatusSyncData.RUNNING:
                        result["total_processing"] += count
                    elif session_status in [ConstantStatusSyncData.INIT, ConstantStatusSyncData.PREPARE]:
                        result["total_wait_process"] += count
                    elif session_status in [ConstantStatusSyncData.STOP]:
                        result["total_process_stop"] += count
                    else:
                        result["total_process_fail"] += count

        return {"data": result}

    def snapshot_total_sessions(self, connector_id):
        return self.snapshot_total_sessions_by_table_query_daily_reports(connector_id)

    def snapshot_details_sessions_by_daily_reports(self, connector_id, session_id):
        merchant_id = validate_merchant_header()
        detail_connector = self._validate_connector_get_report(merchant_id, connector_id, ConstantTypeReport.SNAPSHOT)
        if session_id == "total_streaming_prev_snapshot":
            detail_session = DailyReportsModel().detail_total_streaming_prev_snapshot(merchant_id, connector_id)
        else:
            detail_session = DailyReportsModel().find_one(
                {"connector_id": int(connector_id), "session_id": int(session_id)}
            )
        if not detail_session:
            raise CustomError("Session ID %s not found" % session_id)
        mapping_status_sync = {
            ConstantStatusSyncData.FINISHED: "done",
            ConstantStatusSyncData.RUNNING: "processing",
            ConstantStatusSyncData.STOP: "stop",
            ConstantStatusSyncData.INIT: "wait_process",
            ConstantStatusSyncData.PREPARE: "wait_process",
            ConstantStatusSyncData.FAILED: "fail",
        }

        object_attribute = detail_connector.get("object_attribute", detail_connector.get("object"))

        end_time = detail_session.get("end_time")
        start_time = detail_session.get("start_time")

        process_status = detail_session.get("process_status")

        status_sync = mapping_status_sync.get(process_status)
        consume_count = detail_session.get("total_rows")
        processed_count = detail_session.get("total_rows_success")
        processing_count = detail_session.get("total_rows_processing", 0)
        error_count = detail_session.get("total_rows_error", 0)
        event_processed_count = detail_session.get(f"{object_attribute}_total_rows_success")
        event_error_count = detail_session.get(f"{object_attribute}_total_rows_error")

        object_new_count = detail_session.get(f"{object_attribute}_total_rows_add")
        object_update_count = detail_session.get(f"{object_attribute}_total_rows_updated")

        # update them field tra ve so luong proifle tao moi, toc do theo phut, gio, tac nhan dong bo
        total_new_profile = detail_session.get("profile_total_rows_add", 0)  # so luong ban ghi duoc tao moi
        processing_rate_min = 0
        config_sync_calendar = {}
        if start_time and end_time:
            duration_min = (end_time.timestamp() - start_time.timestamp()) / 60
            processing_rate_min = round(consume_count / duration_min, 1)
        else:
            end_time_pre = datetime.now()
            total_consume = DataFlowDialect().snapshot_total_consume_by_session(
                merchant_id=merchant_id,
                connector_id=connector_id,
                session_id=session_id,
                start_time=(end_time_pre - timedelta(minutes=1)).strftime(self.FROM_FORMAT_DATETIME),
                end_time=end_time_pre.strftime(self.FROM_FORMAT_DATETIME),
            )
            processing_rate_min = total_consume.first().total_consume if total_consume else 0
        processing_rate_hour = processing_rate_min * 60
        detail_connector = ConfigConnectorsModel().detail_connector_by_id(merchant_id, connector_id)
        if not detail_connector:
            raise CustomError(self.lang.get(LANG.NOT_FOUND).get("message"))
        if detail_connector.get("config_sync_calendar", {}).get("mode") == ConstantTypeReport.SNAPSHOT:
            if session_id == "total_streaming_prev_snapshot":
                config_sync_calendar = {
                    "schedule": {"type": ConstantScheduleType.MANUALLY, "config": {"account_id": None}}
                }
            else:
                detail_session_in_config_session = ConfigSessionModel().detail_session_by_id(
                    merchant_id=merchant_id, session_id=session_id
                )
                if not detail_session_in_config_session:
                    config_sync_calendar = {
                        "schedule": {"type": ConstantScheduleType.MANUALLY, "config": {"account_id": None}}
                    }
                else:
                    if (
                        detail_session_in_config_session.get(ConstantSessionModel.SCHEDULE_CONFIG_TYPE)
                        == ConstantScheduleType.MANUALLY
                    ):
                        agent = detail_session_in_config_session.get(ConstantSessionModel.AGENT)
                        config_sync_calendar = {
                            "schedule": {"type": ConstantScheduleType.MANUALLY, "config": {"account_id": agent}}
                        }
                    elif (
                        detail_session_in_config_session.get(ConstantSessionModel.SCHEDULE_CONFIG_TYPE)
                        == ConstantScheduleType.INTERVAL
                    ):
                        config_sync_calendar = {"schedule": detail_session_in_config_session.get("schedule")}

        return {
            "data": {
                "session_id": session_id,
                "start_time": start_time.strftime(self.FROM_FORMAT_DATETIME),
                "end_time": end_time.strftime(self.FROM_FORMAT_DATETIME) if end_time else None,
                "status_sync": status_sync,
                "total_wait_process": consume_count - processed_count - error_count - processing_count,
                "total_processing": processing_count,
                "total_process_done": processed_count,
                "total_process_fail": error_count,
                "report_object": [
                    {
                        "object": "profile",
                        "total_row_update": detail_session.get("profile_total_rows_updated", 0),
                        "total_row_add": detail_session.get("profile_total_rows_add", 0),
                    },
                    {
                        "object": "company",
                        "total_row_update": detail_session.get("company_total_rows_updated", 0),
                        "total_row_add": detail_session.get("company_total_rows_add", 0),
                    },
                    {
                        "object": "ticket",
                        "total_row_update": detail_session.get("ticket_total_rows_updated", 0),
                        "total_row_add": detail_session.get("ticket_total_rows_add", 0),
                    },
                    {
                        "object": "sale",
                        "total_row_update": detail_session.get("sale_total_rows_updated", 0),
                        "total_row_add": detail_session.get("sale_total_rows_add", 0),
                    },
                ],
                "total_new_profile": total_new_profile,
                "processing_rate_min": processing_rate_min,
                "processing_rate_hour": processing_rate_hour,
                "config_sync_calendar": config_sync_calendar,
                "event_error_count": event_error_count,
                "event_processed_count": event_processed_count,
                "object_new_count": object_new_count,
                "object_update_count": object_update_count,
            }
        }

    def snapshot_detail_sessions(self, connector_id, session_id):
        return self.snapshot_details_sessions_by_daily_reports(connector_id, session_id)

    @classmethod
    def _get_consume_status_by_status_sync(cls, status_sync):
        status_map = {
            "done": [ConstantStatusSyncData.FINISHED],
            "processing": [ConstantStatusSyncData.RUNNING],
            "wait_process": [ConstantStatusSyncData.INIT, ConstantStatusSyncData.PREPARE],
            "fail": [ConstantStatusSyncData.FAILED],
            "stop": [ConstantStatusSyncData.STOP],
        }
        if status_sync:

            return [status for s in status_sync.split(",") for status in status_map.get(s, [])]
        return [
            ConstantStatusSyncData.FINISHED,
            ConstantStatusSyncData.STOP,
            ConstantStatusSyncData.RUNNING,
            ConstantStatusSyncData.FAILED,
            ConstantStatusSyncData.INIT,
            ConstantStatusSyncData.PREPARE,
        ]

    def snapshot_get_list_session_by_daily_reports(self, connector_id):
        merchant_id = validate_merchant_header()
        detail_connector = self._validate_connector_get_report(merchant_id, connector_id, ConstantTypeReport.SNAPSHOT)
        start_time = request.args.get("start_time")
        end_time = request.args.get("end_time")
        status_sync = request.args.get("status_sync")

        per_page = int(request.args.get("per_page", default=15, type=int))
        before_token = request.args.get("after_token")
        order_by = int(request.args.get("order_by", default=-1, type=int))
        sort_by = request.args.get("sort_by", "session_id")

        if order_by == ConstantParamReportDataFlow.ParamOrderBy.ASC:
            order_by = 1
        elif order_by == ConstantParamReportDataFlow.ParamOrderBy.DESC:
            order_by = -1

        # data_validate = {"start_time": start_time, "end_time": end_time}

        # self._validate_streaming_real_time_result_by_object(data_validate)
        if not start_time:
            start_time = detail_connector.get("created_time").strftime(self.FROM_FORMAT_DATETIME)
        start_time = convert_string_datetime_to_timezone(start_time, self.FROM_FORMAT_DATETIME)
        end_time = convert_string_datetime_to_timezone(end_time, self.FROM_FORMAT_DATETIME)
        mapping_state = self.__build_mapping_state(None)

        mapping_field_sort = {
            "start_time": "start_time",
            "end_time": "end_time",
            "total_row": "total_rows",
            "session_id": "session_id",
        }

        sort_by = mapping_field_sort.get(sort_by, sort_by)

        lst_consume_status = self._get_consume_status_by_status_sync(status_sync)

        filter_options = {
            "connector_id": int(connector_id),
            "process_status": {"$in": lst_consume_status},
            "start_time": {"$gte": start_time},
            "mode": "batch",
            "$and": [
                {
                    "start_time": {"$gte": start_time},
                },
                {
                    "start_time": {"$lte": end_time},
                },
            ],
        }

        lst_session, after_token = DailyReportsModel().list_session_by_condition(
            filter_options, per_page, before_token, order_by, sort_by
        )
        mapping_status_sync = {
            ConstantStatusSyncData.FINISHED: "done",
            ConstantStatusSyncData.RUNNING: "processing",
            ConstantStatusSyncData.STOP: "stop",
            ConstantStatusSyncData.INIT: "wait_process",
            ConstantStatusSyncData.PREPARE: "wait_process",
            ConstantStatusSyncData.FAILED: "fail",
        }

        results = []
        for session in lst_session:
            consume_count = session.get("total_rows")
            processed_count = session.get("total_rows_success")
            error_count = session.get("total_rows_error")
            session_id = session.get("session_id")
            processing_rate_min = 0
            end_time = session.get("end_time")
            start_time = session.get("start_time")
            if start_time and end_time:
                duration_min = (end_time.timestamp() - start_time.timestamp()) / 60
                processing_rate_min = round(consume_count / duration_min, 1)
            else:
                end_time_pre = datetime.now()
                total_consume = DataFlowDialect().snapshot_total_consume_by_session(
                    merchant_id=merchant_id,
                    connector_id=connector_id,
                    session_id=session_id,
                    start_time=(end_time_pre - timedelta(minutes=1)).strftime(self.FROM_FORMAT_DATETIME),
                    end_time=end_time_pre.strftime(self.FROM_FORMAT_DATETIME),
                )
                processing_rate_min = total_consume.first().total_consume if total_consume else 0
            processing_rate_hour = processing_rate_min * 60
            status_sync = mapping_status_sync.get(session.get("process_status"))
            total_wait_process = consume_count - processed_count - error_count
            item = {
                "session_id": session_id,
                "mode": session.get("mode"),
                "status_sync": status_sync,
                "start_time": start_time.strftime(self.FROM_FORMAT_DATETIME_WITH_SECOND) if start_time else None,
                "end_time": end_time.strftime(self.FROM_FORMAT_DATETIME_WITH_SECOND) if end_time else None,
                "total_row": consume_count,
                "total_process_done": processed_count,
                "total_process_fail": error_count,
                "total_new_profile": session.get("profile_total_rows_add"),
                "processing_rate_min": processing_rate_min,
                "processing_rate_hour": processing_rate_hour,
                "total_wait_process": total_wait_process,
            }

            results.append(item)
        if len(results) < per_page:
            after_token = ""
            filter_aggregate_streaming_to_batch = {
                "connector_id": int(connector_id),
                "mode": "streaming",
            }
            streaming_to_batch = DailyReportsModel().aggregate_streaming_to_batch(filter_aggregate_streaming_to_batch)
            if streaming_to_batch:
                for streaming in streaming_to_batch:
                    end_time = streaming.get("end_time")
                    start_time = streaming.get("start_time")
                    consume_count = streaming.get("total_row")
                    processed_count = streaming.get("total_process_done")
                    error_count = streaming.get("total_process_fail")
                    total_new_profile = streaming.get("total_new_profile")
                    total_wait_process = streaming.get("total_wait_process")
                    duration_min = (end_time.timestamp() - start_time.timestamp()) / 60 if end_time else 1
                    processing_rate_min = round(consume_count / duration_min, 1)
                    processing_rate_hour = processing_rate_min * 60
                    results.append(
                        {
                            "session_id": "total_streaming_prev_snapshot",
                            "mode": "streaming",
                            "status_sync": "done",
                            "start_time": (
                                start_time.strftime(self.FROM_FORMAT_DATETIME_WITH_SECOND) if start_time else None
                            ),
                            "end_time": end_time.strftime(self.FROM_FORMAT_DATETIME_WITH_SECOND) if end_time else None,
                            "total_row": consume_count,
                            "total_process_done": processed_count,
                            "total_process_fail": error_count,
                            "total_new_profile": total_new_profile,
                            "processing_rate_min": processing_rate_min,
                            "processing_rate_hour": processing_rate_hour,
                            "total_wait_process": total_wait_process,
                        }
                    )

        return {
            "data": results,
            "paging": {
                "cursors": {"after": after_token, "before": before_token},
                "per_page": per_page,
            },
        }

    def snapshot_get_list_session(self, connector_id):
        return self.snapshot_get_list_session_by_daily_reports(connector_id)

    def __validate_streaming_result_real_time(self, data_validate):
        rules = {
            "start_time": [Required, InstanceOf(str), Length(1)],
            "end_time": [Required, InstanceOf(str), Length(1)],
            "time_step": [Required, InstanceOf(str), Length(1)],
            "time_unit": [
                Required,
                InstanceOf(str),
                Length(1),
                In(ConstantParamTimeReport.TimeUnit.get_all_attribute()),
            ],
        }
        self.abort_if_validate_error(rules, data_validate)

    def streaming_result_real_time(self, connector_id):
        merchant_id = validate_merchant_header()

        detail_connector = self._validate_connector_get_report(merchant_id, connector_id, ConstantTypeReport.STREAMING)

        start_time = request.args.get("start_time")
        end_time = request.args.get("end_time")
        time_step = request.args.get("time_step")
        time_unit = request.args.get("time_unit")

        data_validate = {
            "start_time": start_time,
            "end_time": end_time,
            "time_step": time_step,
            "time_unit": time_unit,
        }

        self.__validate_streaming_result_real_time(data_validate)

        start_time = convert_string_datetime_to_timezone(start_time, self.FROM_FORMAT_DATETIME).strftime(
            self.TO_FORMAT_DATETIME
        )
        end_time = convert_string_datetime_to_timezone(end_time, self.FROM_FORMAT_DATETIME).strftime(
            self.TO_FORMAT_DATETIME
        )
        mapping_state = self.__build_mapping_state(None)

        results = DataFlowDialect().streaming_realtime_result(
            merchant_id, connector_id, mapping_state, start_time, end_time, time_step, time_unit
        )
        return_data = []
        for result in results:
            result_as_dict = result._asdict()
            return_data.append(
                {
                    "date": convert_string_datetime_to_datetime(
                        result_as_dict.get("time_query"), "%Y-%m-%d %H:%M"
                    ).strftime(self.FROM_FORMAT_DATETIME),
                    "total_process_done": result["processed_count"],
                    "total_process_fail": result["error_count"],
                }
            )
        return {"data": return_data}

    def _validate_streaming_history_by_object(self, data_validate):
        rules = {
            "start_time": [Required, InstanceOf(str), Length(1)],
            "end_time": [Required, InstanceOf(str), Length(1)],
        }
        self.abort_if_validate_error(rules, data_validate)

    def streaming_history_by_object(self, connector_id):

        merchant_id = validate_merchant_header()

        detail_connector = self._validate_connector_get_report(merchant_id, connector_id, ConstantTypeReport.STREAMING)

        start_time = request.args.get("start_time")
        end_time = request.args.get("end_time")

        data_validate = {
            "start_time": start_time,
            "end_time": end_time,
        }

        self._validate_streaming_history_by_object(data_validate)
        start_time = convert_string_datetime_to_timezone(start_time, self.FROM_FORMAT_DATETIME)
        end_time = convert_string_datetime_to_timezone(end_time, self.FROM_FORMAT_DATETIME)
        results = DailyReportsModel().streaming_history_result_by_object(
            filter_options={
                "connector_id": int(connector_id),
                "start_time": {"$gte": start_time},
                "$or": [
                    {"end_time": {"$lte": end_time}},
                    {"end_time": None},
                ],
            },
        )

        data_response = []
        object_reports = SettingObjectHandleModel().get_object_relate_by_object_primary(
            merchant_id,
            detail_connector["data_type"],
            detail_connector.get("object"),
            detail_connector["source_key"],
        )

        for item in results:
            data_response.append(
                {
                    "date": item.get("date"),
                    "reports": [
                        {
                            "object": "sale" if object_item == "deal" else object_item,
                            "total_process_add": item.get(f"{object_item}_total_rows_add") or 0,
                            "total_process_update": item.get(f"{object_item}_total_rows_updated") or 0,
                        }
                        for object_item in object_reports
                    ],
                }
            )

        return {"data": data_response}

    def _validate_streaming_history_total_pipeline(self, data_validate):
        rules = {
            "start_time": [Required, InstanceOf(str), Length(1)],
            "end_time": [Required, InstanceOf(str), Length(1)],
        }
        self.abort_if_validate_error(rules, data_validate)

    def streaming_history_total_pipeline(self, connector_id):
        merchant_id = validate_merchant_header()

        self._validate_connector_get_report(merchant_id, connector_id, ConstantTypeReport.STREAMING)

        start_time = request.args.get("start_time")
        end_time = request.args.get("end_time")

        data_validate = {"start_time": start_time, "end_time": end_time}
        self._validate_streaming_history_total_pipeline(data_validate)

        start_time = convert_string_datetime_to_timezone(start_time, self.FROM_FORMAT_DATETIME)
        end_time = convert_string_datetime_to_timezone(end_time, self.FROM_FORMAT_DATETIME)

        results = DailyReportsModel().streaming_history_total_pipeline(
            filter_options={
                "connector_id": int(connector_id),
                "start_time": {"$gte": start_time},
                "$or": [
                    {"end_time": {"$lte": end_time}},
                    {"end_time": None},
                ],
            },
        )
        return {"data": [*results]}

    def _validate_streaming_real_time_result_by_status(self, data_validate):
        rules = {
            "start_time": [Required, InstanceOf(str), Length(1)],
            "end_time": [Required, InstanceOf(str), Length(1)],
        }
        self.abort_if_validate_error(rules, data_validate)

    def streaming_real_time_result_by_status(self, connector_id):
        merchant_id = validate_merchant_header()

        self._validate_connector_get_report(merchant_id, connector_id, ConstantTypeReport.STREAMING)

        start_time = request.args.get("start_time")
        end_time = request.args.get("end_time")

        data_validate = {"start_time": start_time, "end_time": end_time}

        self._validate_streaming_real_time_result_by_status(data_validate)

        start_time = convert_string_datetime_to_timezone(start_time, self.FROM_FORMAT_DATETIME).strftime(
            self.TO_FORMAT_DATETIME
        )
        end_time = convert_string_datetime_to_timezone(end_time, self.FROM_FORMAT_DATETIME).strftime(
            self.TO_FORMAT_DATETIME
        )
        mapping_state = self.__build_mapping_state(None)

        result = {
            "total_process_done": 0,
            "total_process_fail": 0,
            "total_processing": 0,
            "first_time": None,
            "last_time": None,
        }

        total_sessions = DataFlowDialect().streaming_realtime_result_by_status(
            merchant_id=merchant_id,
            mapping_state=mapping_state,
            connector_id=connector_id,
            start_time=start_time,
            end_time=end_time,
        )
        MobioLogging().info("snapshot_total_sessions :: total_sessions :: {}".format(total_sessions))

        if total_sessions:
            result = {
                "total_process_done": total_sessions.get("processed_count") or 0,
                "total_process_fail": total_sessions.get("error_count") or 0,
                "total_processing": total_sessions.get("processing_count") or 0,
                "first_time": (
                    total_sessions.get("first_time").strftime(self.FROM_FORMAT_DATETIME_WITH_SECOND)
                    if total_sessions.get("first_time")
                    else None
                ),
                "last_time": (
                    total_sessions.get("last_time").strftime(self.FROM_FORMAT_DATETIME_WITH_SECOND)
                    if total_sessions.get("first_time")
                    else None
                ),
            }

        return {"data": result}

    def _validate_streaming_real_time_result_by_object(self, data_validate):
        rules = {
            "start_time": [Required, InstanceOf(str), Length(1)],
            "end_time": [Required, InstanceOf(str), Length(1)],
        }
        self.abort_if_validate_error(rules, data_validate)

    def streaming_real_time_result_by_object(self, connector_id):
        merchant_id = validate_merchant_header()

        self._validate_connector_get_report(merchant_id, connector_id, ConstantTypeReport.STREAMING)

        start_time = request.args.get("start_time")
        end_time = request.args.get("end_time")

        data_validate = {"start_time": start_time, "end_time": end_time}

        self._validate_streaming_real_time_result_by_object(data_validate)

        start_time = convert_string_datetime_to_timezone(start_time, self.FROM_FORMAT_DATETIME).strftime(
            self.TO_FORMAT_DATETIME
        )
        end_time = convert_string_datetime_to_timezone(end_time, self.FROM_FORMAT_DATETIME).strftime(
            self.TO_FORMAT_DATETIME
        )
        mapping_state = self.__build_mapping_state(None)

        dict_result = {
            "profile": {"object": "profile", "total_process_update": 0, "total_process_add": 0},
            "company": {"object": "company", "total_process_update": 0, "total_process_add": 0},
            "ticket": {"object": "ticket", "total_process_update": 0, "total_process_add": 0},
            "sale": {"object": "sale", "total_process_update": 0, "total_process_add": 0},
        }

        total_by_object = DataFlowDialect().streaming_realtime_result_by_object(
            merchant_id=merchant_id,
            mapping_state=mapping_state,
            connector_id=connector_id,
            start_time=start_time,
            end_time=end_time,
        )
        MobioLogging().info("streaming_real_time_result_by_object :: total_by_object :: {}".format(total_by_object))

        mapping_object = {"deal": "sale"}

        if total_by_object:
            for data_object in total_by_object:
                data_object_to_dict = data_object._asdict()
                object = data_object_to_dict.get("object")
                if not object:
                    continue
                object = mapping_object.get(object, object)
                if dict_result.get(object, {}):
                    dict_result.get(object).update(
                        {
                            "total_process_update": data_object_to_dict.get("updated_count") or 0,
                            "total_process_add": data_object_to_dict.get("add_count") or 0,
                        }
                    )

        return {"data": list(dict_result.values())}

    def _convert_order_by(self, order_by):

        return {
            ConstantParamReportDataFlow.ParamOrderBy.ASC: 1,
            ConstantParamReportDataFlow.ParamOrderBy.DESC: -1,
        }.get(order_by, order_by)

    def streaming_history_pipeline_list(self, connector_id):
        merchant_id = validate_merchant_header()

        detail_connector = self._validate_connector_get_report(merchant_id, connector_id, ConstantTypeReport.STREAMING)

        start_time = request.args.get("start_time")
        end_time = request.args.get("end_time")

        per_page = int(request.args.get("per_page", default=15, type=int))
        before_token = request.args.get("after_token")
        order_by = int(request.args.get("order_by", default=-1, type=int))
        sort_by = request.args.get("sort_by", "date")

        order_by = self._convert_order_by(order_by)

        data_validate = {"start_time": start_time, "end_time": end_time}

        self._validate_streaming_real_time_result_by_object(data_validate=data_validate)

        start_time = convert_string_datetime_to_timezone(start_time, self.FROM_FORMAT_DATETIME)

        end_time = convert_string_datetime_to_timezone(end_time, self.FROM_FORMAT_DATETIME)

        mapping_field_sort = {
            "date": "_id"
        }

        lst_history_streaming, after_token = DailyReportsModel().streaming_history_pipeline_list(
            filter_options={
                "connector_id": int(connector_id),
                "start_time": {"$gte": start_time},
                "$or": [
                    {"end_time": {"$lte": end_time}},
                    {"end_time": None},
                ],
            },
            per_page=per_page,
            before_token=before_token,
            order_by=order_by,
            sort_by=mapping_field_sort.get(sort_by, sort_by),
        )
        results = [*lst_history_streaming]

        return {
            "data": {"date_count": len(results), "streaming_data": results},
            "paging": {
                "cursors": {"after": after_token, "before": before_token},
                "per_page": per_page,
            },
        }

    def snapshot_result_running_sessions(self, connector_id):
        """
        {
            "code": 200,
            "message": "request thành công."
            "data": [
                {
                    "session_id": "20250216043077",
                    "detail_data": [
                        {
                            "time_group": "2025-02-17 13:30:00",
                            "total_consume": 1423
                        },
                        ...
                    }
                },
                ...
            ]
        }
        """
        merchant_id = validate_merchant_header()

        detail_connector = self._validate_connector_get_report(merchant_id, connector_id, ConstantTypeReport.SNAPSHOT)

        start_time = request.args.get("start_time")
        end_time = request.args.get("end_time")
        status_sync = request.args.get("status_sync", "processing,done")

        if not start_time:
            start_time = detail_connector.get("created_time").strftime(self.FROM_FORMAT_DATETIME)

        start_time = convert_string_datetime_to_timezone(start_time, self.FROM_FORMAT_DATETIME).strftime(
            self.TO_FORMAT_DATETIME
        )
        end_time = convert_string_datetime_to_timezone(end_time, self.FROM_FORMAT_DATETIME).strftime(
            self.TO_FORMAT_DATETIME
        )

        lst_consume_status = self._get_consume_status_by_status_sync(status_sync)
        lst_session_of_connector = DataFlowDialect().snapshot_result_running_session_of_connector(
            connector_id=connector_id,
            start_time=start_time,
            end_time=end_time,
            lst_consume_status=lst_consume_status,
        )
        # Dùng defaultdict để nhóm các bản tin theo session_id
        grouped = defaultdict(list)
        for result in lst_session_of_connector:
            record = result._asdict()
            grouped[record["session_id"]].append(
                {"time_group": record["time_query"], "total_consume": record["total_consume"]}
            )

        # Chuyển đổi thành danh sách các dict theo định dạng yêu cầu
        results = []
        for session_id, details in grouped.items():
            results.append({"session_id": session_id, "detail_data": details})

        return {"data": results}

    def snapshot_total_session_by_status_everyday_by_daily_reports(self, connector_id):
        merchant_id = validate_merchant_header()

        detail_connector = self._validate_connector_get_report(merchant_id, connector_id, ConstantTypeReport.SNAPSHOT)

        start_time = request.args.get("start_time")
        end_time = request.args.get("end_time")
        status_sync = request.args.get("status_sync", "wait_process,processing,done")
        if not start_time:
            start_time = detail_connector.get("created_time").strftime(self.FROM_FORMAT_DATETIME)
        start_time = convert_string_datetime_to_timezone(start_time, self.FROM_FORMAT_DATETIME)
        end_time = convert_string_datetime_to_timezone(end_time, self.FROM_FORMAT_DATETIME)

        lst_consume_status = self._get_consume_status_by_status_sync(status_sync)

        pipeline = [
            # Match documents with the specified connector_id and within the time range
            {
                "$match": {
                    "connector_id": int(connector_id),
                    "process_status": {"$in": lst_consume_status},
                    "start_time": {"$gte": start_time},
                    "$or": [
                        {"end_time": {"$lte": end_time}},
                        {"end_time": None},
                    ],
                    "mode": "batch",
                }
            },
            # Add a field with just the date part (no time)
            {"$addFields": {"date_only": {"$dateToString": {"format": "%Y-%m-%d 00:00:00", "date": "$start_time"}}}},
            # Group by the date and status
            {"$group": {"_id": {"date": "$date_only", "status": "$process_status"}, "count": {"$sum": 1}}},
            # Reshape for easier processing
            {"$group": {"_id": "$_id.date", "statuses": {"$push": {"status": "$_id.status", "count": "$count"}}}},
            # Sort by date
            {"$sort": {"_id": 1}},
        ]

        # Execute the aggregation
        result = list(DailyReportsModel().aggregate(pipeline))

        # Process results into the desired format
        detail_data = []

        for item in result:
            # Initialize counters for each status
            status_counts = {"done": 0, "fail": 0, "processing": 0, "wait_process": 0, "stop": 0}

            # Update counters based on aggregation results
            for status_info in item["statuses"]:
                status = status_info["status"]
                if status in [ConstantStatusSyncData.FINISHED]:
                    status_counts["done"] += status_info["count"]
                elif status in ConstantStatusSyncData.RUNNING:
                    status_counts["processing"] += status_info["count"]
                elif status in [ConstantStatusSyncData.INIT, ConstantStatusSyncData.PREPARE]:
                    status_counts["wait_process"] += status_info["count"]
                elif status in [ConstantStatusSyncData.STOP]:
                    status_counts["stop"] += status_info["count"]
                else:
                    status_counts["fail"] += status_info["count"]

            # Add to detail_data in the required format
            detail_data.append({"date_group": item["_id"], "total_done_sessions": status_counts["done"]})

        # Construct the final response
        response = {
            "detail_data": detail_data,
            "start_time": start_time.strftime(self.TO_FORMAT_DATETIME),
            "end_time": end_time.strftime(self.TO_FORMAT_DATETIME),
            "status_sync": "wait_process,processing,done,fail",
        }
        return {"data": response}

    def snapshot_total_session_by_status_everyday(self, connector_id):
        """{
            "code": 200,
            "message": "request thành công."
            "data": [
                "status_sync": "done",
                "start_time": "2025-02-10 23:50:00",
                "end_time": "2025-02-17 23:50:00",
                "detail_data": {
                    "date_group": "2025-01-02 00:00:00",
                    "total_done_sessions": 3
                }
            ]
        }"""
        merchant_id = validate_merchant_header()
        table_query = request.args.get("table_query", "daily_reports")
        if table_query == "daily_reports":
            return self.snapshot_total_session_by_status_everyday_by_daily_reports(connector_id)
        # merchant_id= '1cb2a489-b34a-41b3-aa7d-f1efc580d687'

        detail_connector = self._validate_connector_get_report(merchant_id, connector_id, ConstantTypeReport.SNAPSHOT)

        start_time = request.args.get("start_time")
        end_time = request.args.get("end_time")
        status_sync = request.args.get("status_sync", "wait_process,processing,done,stop")
        # start_time = "2024-12-20T00:00Z"
        # end_time = "2025-03-19T18:30Z"
        # status_sync = "wait_process,processing,done"
        if not start_time:
            start_time = detail_connector.get("created_time").strftime(self.FROM_FORMAT_DATETIME)

        start_time = convert_string_datetime_to_timezone(start_time, self.FROM_FORMAT_DATETIME).strftime(
            self.TO_FORMAT_DATETIME
        )
        end_time = convert_string_datetime_to_timezone(end_time, self.FROM_FORMAT_DATETIME).strftime(
            self.TO_FORMAT_DATETIME
        )

        lst_consume_status = self._get_consume_status_by_status_sync(status_sync)
        lst_total_session_by_date = DataFlowDialect().snapshot_total_sessions_by_status_every_day(
            connector_id=connector_id,
            start_time=start_time,
            end_time=end_time,
            lst_consume_status=lst_consume_status,
        )

        # Chuyển đổi thành danh sách các dict theo định dạng yêu cầu
        detail_data = [detail._asdict() for detail in lst_total_session_by_date]
        results = {
            "status_sync": status_sync,
            "start_time": start_time,
            "end_time": end_time,
            "detail_data": detail_data,
        }
        return {"data": results}

    def streaming_result_real_time_by_object_event(self, connector_id):
        # merchant_id = "57d559c1-39a1-4cee-b024-b953428b5ac8"
        # start_time = "2025-02-26T14:00Z"
        # end_time = "2025-02-26T15:00Z"
        # time_step = "15"
        # time_unit = "minute"
        merchant_id = validate_merchant_header()

        # detail_connector = self._validate_connector_get_report(merchant_id, connector_id, ConstantTypeReport.STREAMING)

        start_time = request.args.get("start_time")
        end_time = request.args.get("end_time")
        time_step = request.args.get("time_step")
        time_unit = request.args.get("time_unit")

        data_validate = {
            "start_time": start_time,
            "end_time": end_time,
            "time_step": time_step,
            "time_unit": time_unit,
        }

        self.__validate_streaming_result_real_time(data_validate)

        start_time = convert_string_datetime_to_timezone(start_time, self.FROM_FORMAT_DATETIME).strftime(
            self.TO_FORMAT_DATETIME
        )
        end_time = convert_string_datetime_to_timezone(end_time, self.FROM_FORMAT_DATETIME).strftime(
            self.TO_FORMAT_DATETIME
        )
        mapping_state = self.__build_mapping_state(None)

        results = DataFlowDialect().streaming_realtime_result_by_object_event(
            merchant_id, connector_id, mapping_state, start_time, end_time, time_step, time_unit
        )

        return_data = []
        for result in results:
            result_as_dict = result._asdict()
            return_data.append(
                {
                    "date": convert_string_datetime_to_datetime(
                        result_as_dict.get("time_query"), "%Y-%m-%d %H:%M"
                    ).strftime(self.TO_FORMAT_DATETIME),
                    "profile_add_count": result["profile_add_count"],
                    "dynamic_event_total_rows_success": result["dynamic_event_total_rows_success"],
                    "dynamic_event_total_rows_error": result["dynamic_event_total_rows_error"],
                    "product_holding_total_rows_success": result["product_holding_total_rows_success"],
                    "product_holding_total_rows_error": result["product_holding_total_rows_error"],
                }
            )
        return {"data": return_data}

    def streaming_result_history_by_object_event(self, connector_id):
        merchant_id = validate_merchant_header()

        start_time = request.args.get("start_time")
        end_time = request.args.get("end_time")
        time_step = request.args.get("time_step")
        time_unit = request.args.get("time_unit")

        data_validate = {
            "start_time": start_time,
            "end_time": end_time,
            "time_step": time_step,
            "time_unit": time_unit,
        }

        self.__validate_streaming_result_real_time(data_validate)

        start_time = convert_string_datetime_to_timezone(start_time, self.FROM_FORMAT_DATETIME)
        end_time = convert_string_datetime_to_timezone(end_time, self.FROM_FORMAT_DATETIME)
        mapping_state = self.__build_mapping_state(None)

        results = DailyReportsModel().streaming_history_result_by_object_event(
            filter_options={
                "connector_id": int(connector_id),
                "start_time": {"$gte": start_time},
                "$or": [{"end_time": {"$lte": end_time}}, {"end_time": None}],
            },
        )
        return {"data": [*results]}

    def streaming_history_result_from_source(self, connector_id):
        time_step = "15"
        time_unit = "minute"
        merchant_id = validate_merchant_header()

        start_time = request.args.get("start_time")
        end_time = request.args.get("end_time")

        data_validate = {"start_time": start_time, "end_time": end_time, "time_step": time_step, "time_unit": time_unit}

        self.__validate_streaming_result_real_time(data_validate)

        start_time = convert_string_datetime_to_timezone(start_time, self.FROM_FORMAT_DATETIME)
        end_time = convert_string_datetime_to_timezone(end_time, self.FROM_FORMAT_DATETIME)

        results = DailyReportsModel().streaming_history_result_from_source(
            filter_options={
                "connector_id": int(connector_id),
                "start_time": {"$gte": start_time},
                "$or": [{"end_time": {"$lte": end_time}}, {"end_time": None}],
            },
        )
        results = [*results]
        return {"data": results[0] if results else {}}

    def trigger_aggregate_report(self):
        subprocess.Popen(
            ["python3.11", "-m", "src.cron_job.cron_job_agg_report_df_log_to_daily_report"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
        )
        return {"data": "success"}

    def trigger_send_noti(self):
        from src.cron_job.cron_job_report_time_frame import (
            process_job_send_noti_report_result_connector,
        )

        start_time = datetime.now(datetime.UTC).replace(hour=0, minute=0, second=0, microsecond=0)
        process_job_send_noti_report_result_connector(start_time, datetime.now())
        return {"data": "success"}


# if __name__=='__main__':
# DataFlowReportController().snapshot_total_session_by_status_everyday(connector_id=1519)
# DataFlowReportController().snapshot_detail_sessions(connector_id=1553, session_id=20250224045224)
# DataFlowReportController().snapshot_detail_sessions(connector_id=1806, session_id=20250318075984)
# DataFlowReportController().snapshot_total_session_by_status_everyday(connector_id=1849)
# DataFlowReportController().snapshot_get_list_session(connector_id=1871)
