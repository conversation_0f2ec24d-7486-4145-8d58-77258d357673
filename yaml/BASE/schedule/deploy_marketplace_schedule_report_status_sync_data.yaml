apiVersion: apps/v1
kind: Deployment
metadata:
  name: marketplace-sch-report-status-sync-data
  labels:
    app: marketplace-sch-report-status-sync-data
spec:
  replicas: 1
  selector:
    matchLabels:
      app: marketplace-sch-report-status-sync-data
  template:
    metadata:
      labels:
        app: marketplace-sch-report-status-sync-data
    spec:
      serviceAccountName: mobio
      containers:
        - name: market-place
          image: {image}
          imagePullPolicy: IfNotPresent
          command: ["/bin/sh", "-c"]
          args:
            [
              "cd $MARKET_PLACE_HOME; sh prepare_env.sh && python3.11 -u start_schedule.py marketplace-sch-report-status-sync-data",
            ]
          resources:
            requests:
              memory: 30Mi
              cpu: 40m
            limits:
              memory: 1Gi
              cpu: 500m
          envFrom:
            - configMapRef:
                name: mobio-config
            - secretRef:
                name: mobio-secret
          volumeMounts:
            - name: mobio-shared-data
              mountPath: /media/data/resources/
            - name: mobio-public-shared-data
              mountPath: /media/data/public_resources/
      initContainers:
        - name: init-market-place
          image: {image}
          command:
            [
              "/bin/sh",
              "-c",
              "cd $MARKET_PLACE_HOME; sh prepare_env.sh && sh check_image.sh",
            ]
          envFrom:
            - configMapRef:
                name: mobio-config
            - secretRef:
                name: mobio-secret
          volumeMounts:
            - name: mobio-shared-data
              mountPath: /media/data/resources/
            - name: mobio-public-shared-data
              mountPath: /media/data/public_resources/
      imagePullSecrets:
        - name: registrypullsecret
      volumes:
        - name: mobio-shared-data
          persistentVolumeClaim:
            claimName: mobio-resources-pvc
        - name: mobio-public-shared-data
          persistentVolumeClaim:
            claimName: mobio-public-resources-pvc
