apiVersion: batch/v1
kind: CronJob
metadata:
  name: marketplace-cj-agg-df-log-to-daily-report
  labels:
    app: marketplace-cj-agg-df-log-to-daily-report
spec:
  schedule: "1 */1 * * *"
  # schedule: 0 * * * *
  startingDeadlineSeconds: 200  # Thời hạn bắt đầu <Default: không được thiết lập>
  concurrencyPolicy: Allow  # Chính sách đồng thời: Allow, Forbid, Replace <Default: Allow>
  suspend: false  # Tạm dừng CronJob <Default: false>
  successfulJobsHistoryLimit: 3  # Giớ<PERSON> hạn lịch sử Job thành công <Default: 3>
  failedJobsHistoryLimit: 1  # Giới hạn lịch sử Job thất bại <Default: 1>
  jobTemplate:
    spec:
      parallelism: 1  # Giá trị mặc định là 1l
      completions: 1  # Gi<PERSON> trị mặc định là 1
      activeDeadlineSeconds: 3600 # <Default: kh<PERSON><PERSON> đư<PERSON><PERSON> thiết lập>
      backoffLimit: 6  # Gi<PERSON> trị mặc định là 6
      ttlSecondsAfterFinished: 3600 #<Default: không được thiết lập>
      template:
        spec:
          serviceAccountName: mobio
          containers:
          - name: market-place
            image: {image}
            imagePullPolicy: IfNotPresent
            command: ["/bin/sh", "-c"]
            args: [ "cd $MARKET_PLACE_HOME; sh prepare_env.sh && python3.11 -u start_cron_job.py agg-df-log-to-daily-report" ]
            resources:
              requests:
                memory: 30Mi
                cpu: 40m
              limits:
                memory: 1Gi
                cpu: 500m
            envFrom:
              - configMapRef:
                  name: mobio-config
              - secretRef:
                  name: mobio-secret
            volumeMounts:
              - name: mobio-shared-data
                mountPath: /media/data/resources/
              - name: mobio-public-shared-data
                mountPath: /media/data/public_resources/
          initContainers:
            - name: init-market-place
              image: {image}
              command: [ '/bin/sh', '-c', "cd $MARKET_PLACE_HOME; sh prepare_env.sh && sh check_image.sh" ]
              envFrom:
                - configMapRef:
                    name: mobio-config
                - secretRef:
                    name: mobio-secret
              volumeMounts:
                - name: mobio-shared-data
                  mountPath: /media/data/resources/
                - name: mobio-public-shared-data
                  mountPath: /media/data/public_resources/
          imagePullSecrets:
            - name: registrypullsecret
          volumes:
            - name: mobio-shared-data
              persistentVolumeClaim:
                claimName: mobio-resources-pvc
            - name: mobio-public-shared-data
              persistentVolumeClaim:
                claimName: mobio-public-resources-pvc
          restartPolicy: OnFailure